{"name": "adwav-dashboard", "version": "1.0.0", "description": "Dashboard to create Ads on Adwav", "main": "main.js", "dependencies": {"@babel/runtime": "^7.12.5", "@bugsnag/js": "^7.5.4", "@bugsnag/plugin-vue": "^7.5.4", "@coreui/coreui": "^3.4.0", "@coreui/icons": "^2.0.0-rc.0", "@coreui/utils": "^1.3.1", "@coreui/vue": "^3.2.7", "@coreui/vue-chartjs": "^1.0.6", "@fortawesome/fontawesome-svg-core": "^1.2.32", "@fortawesome/free-solid-svg-icons": "^5.15.1", "@fortawesome/vue-fontawesome": "^2.0.2", "autoprefixer": "^9.8.6", "axios": "^0.21.1", "babel-polyfill": "^6.26.0", "date-fns": "^2.23.0", "debounce": "^1.2.1", "jquery": "^3.5.1", "lodash": "^4.17.20", "numeral": "^2.0.6", "papaparse": "^5.3.1", "slugify": "^1.4.6", "sweetalert": "^2.1.2", "uslug": "^1.0.4", "v-calendar": "^2.3.0", "v-idle": "^0.2.0", "vue": "^2.6.12", "vue-cli": "^2.9.6", "vue-date-pick": "^1.4.1", "vue-debounce": "^2.6.0", "vue-form-wizard": "^0.8.4", "vue-multiselect": "^2.1.6", "vue-notification": "^1.3.20", "vue-router": "~3.1.6", "vue-sidebar-menu": "^4.7.4", "vue-sweetalert2": "^4.2.0", "vue-toast-notification": "^0.6.3", "vue2-daterange-picker": "^0.6.2", "vue2-timepicker": "^1.1.5", "vuedraggable": "^2.24.3", "vueperslides": "^2.15.0", "vuex": "^3.6.0"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-runtime": "^7.12.10", "@types/debounce": "^1.2.0", "@vue/cli-plugin-babel": "~4.3.1", "@vue/cli-plugin-e2e-nightwatch": "~4.3.1", "@vue/cli-plugin-eslint": "~4.3.1", "@vue/cli-plugin-unit-jest": "^3.12.1", "@vue/cli-service": "~4.3.1", "@vue/test-utils": "1.0.0-beta.29", "babel-eslint": "~10.1.0", "babel-jest": "~25.2.6", "chromedriver": "^88.0.0", "core-js": "~3.6.4", "eslint": "~6.8.0", "eslint-plugin-vue": "~6.2.2", "npm-run-all": "~4.1.5", "prettier": "^3.3.3", "prettier-plugin-vue": "^1.1.6", "sass": "^1.50.0", "sass-loader": "~8.0.2", "vue-template-compiler": "^2.6.12", "vue-tweet-embed": "^2.4.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "build-dev": "vue-cli-service build --mode development", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "clearCache": "jest --clear<PERSON>ache", "release": "npm-run-all clearCache lint build test:unit test:e2e", "production": "vue-cli-service serve --mode production", "prettier": "prettier --write \"src/**/*.{js,vue,css,html}\"", "prettier:check": "prettier --check \"src/**/*.{js,vue,css,html}\""}, "repository": {"type": "git", "url": "git+https://github.com/arifzuhair/Adwav-Dashboard.git"}, "author": "<PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/arifzuhair/Adwav-Dashboard/issues"}, "homepage": "https://github.com/arifzuhair/Adwav-Dashboard#readme"}