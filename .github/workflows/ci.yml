name: CI

on:
  pull_request:
    branches:
      - master

jobs:
  ci:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16.x'

      - name: Install dependencies
        run: npm install

      - name: Run linter check
        run: npm run lint

      - name: Run prettier check
        run: npm run prettier:check
