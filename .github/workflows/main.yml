name: deploy_to_firebase_hosting

on:
  push:
    branches:
      - master

jobs:
  deploy_to_firebase_hosting:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout the repository
        uses: actions/checkout@master

      - uses: actions/setup-node@master
        with:
          node-version: '12.x'

      - run: npm install

      - name: Build
        run: |
          cp .production .env && npm run build

      - name: Deploy to Firebase
        uses: w9jds/firebase-action@master
        with:
          args: deploy --only hosting:adwav-dashboard
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
