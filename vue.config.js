module.exports = {
  lintOnSave: false,
  runtimeCompiler: true,
  configureWebpack: {
    devtool: 'source-map', // Enables source maps in development
    //Necessary to run npm link https://webpack.js.org/configuration/resolve/#resolve-symlinks
    resolve: {
      symlinks: false,
      extensions: ['*', '.mjs', '.js', '.json'],
    },
    module: {
      rules: [
        {
          test: /\.mjs$/,
          include: /node_modules/,
          type: 'javascript/auto',
        },
      ],
    },
  },
  transpileDependencies: ['@coreui/utils'],
};
