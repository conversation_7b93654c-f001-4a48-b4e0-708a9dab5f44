function getCancelScheduleText(pn) {
  let text = `Cancel scheduled PN for ${pn.payload.articleId} ?`;
  if (
    pn.payload.articleId.startsWith('ATI') ||
    pn.payload.articleId.startsWith('ATV')
  ) {
    if (pn.payload.articleId.startsWith('ATI')) {
      text = `Cancel scheduled Auto-PN Interest ${pn.payload.articleId} ?`;
    } else {
      text = `Cancel scheduled Auto-PN Views ${pn.payload.articleId} ?`;
    }
  }
  return text;
}

function isAutoPn(articleId) {
  return articleId.startsWith('ATI_') || articleId.startsWith('ATV_');
}

function getUpdateScheduleText(pn) {
  let text = `Schedule PN for ${pn.payload.articleId} has been successfully updated`;
  if (
    pn.payload.articleId.startsWith('ATI') ||
    pn.payload.articleId.startsWith('ATV')
  ) {
    if (pn.payload.articleId.startsWith('ATI')) {
      text = `Schedule Auto-PN Interest ${pn.payload.articleId} has been successfully updated`;
    } else {
      text = `Schedule Auto-PN Views ${pn.payload.articleId} has been successfully updated`;
    }
  }
  return text;
}

function getPushText(pn, scheduled) {
  let text = `Article ID ${pn.articleId} is successfully sent`;
  if (pn.articleId.startsWith('ATI') || pn.articleId.startsWith('ATV')) {
    if (pn.articleId.startsWith('ATI')) {
      text = `Auto-PN Interest ${pn.articleId} is successfully sent`;
    } else {
      text = `Auto-PN Views ${pn.articleId} is successfully sent`;
    }
  }
  if (scheduled === '1') {
    text = `Article ID ${pn.articleId} is successfully scheduled`;
    if (pn.articleId.startsWith('ATI') || pn.articleId.startsWith('ATV')) {
      if (pn.articleId.startsWith('ATI')) {
        text = `Auto-PN Interest ${pn.articleId} is successfully scheduled`;
      } else {
        text = `Auto-PN Views ${pn.articleId} is successfully scheduled`;
      }
    }
  }
  return text;
}

export { getCancelScheduleText, getUpdateScheduleText, getPushText, isAutoPn };
