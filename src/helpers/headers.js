export function createHeaders(token, ntoken = null, type = 'user') {
  const headers = {
    headers: {
      Authorization: 'Bearer ' + token,
      languages: process.env.VUE_APP_LANGUAGES,
      'request-type': type,
    },
  };

  if (ntoken) {
    headers.headers.token = ntoken;
    headers.headers.hash = ntoken;
    headers.headers.dashboard = 1;
  }

  return headers;
}

export function createHeadersForPn2(token) {
  var headers = {
    headers: {
      apiKey: token,
    },
  };

  return headers;
}
