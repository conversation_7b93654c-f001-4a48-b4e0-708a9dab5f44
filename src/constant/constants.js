const ADS_TYPE = {
  bigcard: 'Feed Ad',
  splash: 'Splash Ad',
  spotlight: 'Spotlight Ad',
  sponsored: 'Sponsored Ad',
};

const ADS_TABLE_FIELDS = [
  { key: 'id', label: 'Advertisement ID' },
  { key: 'name', label: 'Advertisement Name' },
  { key: 'type', label: 'Advertisement Type' },
  { key: 'body', label: 'Advertisement Title' },
  { key: 'title', label: 'Advertisement Description' },
  { key: 'language', label: 'Language' },
  { key: 'show_details', sorter: false, label: 'Action' },
];

const CAMPAIGN_ADS_TABLE_FIELDS = [
  { key: 'id', label: 'Advertisement ID' },
  { key: 'name', label: 'Advertisement Name' },
  { key: 'type', label: 'Advertisement Type' },
  { key: 'language', label: 'Language' },
  { key: 'impressions', label: 'Impressions' },
  { key: 'clicks', label: 'Clicks' },
  { key: 'ctr', label: 'CTR%' },
  { key: 'show_details', sorter: false, label: 'Action' },
];

const CAMPAIGN_STATUS_COLORS = {
  completed: 'success',
  active: 'success',
  snoozed: 'secondary',
  paused: 'secondary',
  new: 'primary',
  scheduled: 'warning',
  stopped: 'danger',
};

const CAMPAIGN_STATUS_ICONS = {
  active: 'cil-media-play',
  snoozed: 'cil-media-pause',
  paused: 'cil-media-pause',
  completed: 'cil-check-circle',
  scheduled: 'cil-av-timer',
  new: 'cil-library-add',
  stopped: 'cil-ban',
};

const CAMPAIGN_FIELD_NAMES = {
  name: 'Name',
  total_budget: 'Budget',
  cpm: 'CPM',
  start_date: 'Start Date',
  start_hour: 'Start Hour',
  end_date: 'End Date',
  type: 'Campaign Type',
  targeted_states: 'Location',
  targeted_telcos: 'Telco Provider',
  targeted_feeds: 'Feed',
  impressions_per_day: 'Max Impression / Day',
  impressions_per_user: 'Impression / User / Campaign',
  clicks_per_user: 'Clicks / User / Campaign',
  impressions_per_session: 'Impression / User / Session',
  clicks_per_sessions: 'Clicks / User / Session',
  impressions_per_time_block: 'Impression / User / Day',
  clicks_per_time_block: 'Clicks / User / Day',
  status: 'Status',
  targeted_interests: 'Interest',
  targeted_devices: 'Device',
  ads_type: 'Ad Type',
};

const DEFAULT_PODS = {
  v3_default_pod: 10,
  v3_track_default_pod: 5,
  comment_api_default_pod: 6,
  feed_api_default_pod: 10,
};

const SCALED_PODS = {
  v3_scaled_pod: 40,
  v3_track_scaled_pod: 15,
  comment_api_scaled_pod: 12,
  feed_api_scaled_pod: 40,
};

const GENERAL = {
  default_announcement_background_color: '#f9dc5c',
  default_announcement_text_color: '#000000',
};

const HTML_AD_TYPES = {
  CAROUSEL: {
    value: 'CAROUSEL',
    label: 'Carousel',
    validation: { min_images: 4 },
  },
  IMAGE_SLIDER: {
    value: 'IMAGE_SLIDER',
    label: 'Image Slider',
    validation: { min_images: 2 },
  },
  VIDEO_SLIDER: {
    value: 'VIDEO_SLIDER',
    label: 'Video Slider',
    validation: { min_images: 2 },
  },
};

const VIDEO_TYPES = ['mp4', 'mov', 'avi', 'webm', 'mkv'];
const IMAGE_TYPES = ['jpg', 'jpeg', 'png', 'bmp', 'webp'];
const VIDEO_TYPE = 'video';
const IMAGE_TYPE = 'image';

const LANGING_PAGE_TYPES = [
  { value: 'url', label: 'Landing URL' },
  { value: 'lead', label: 'Lead Form' },
  { value: 'nw_post', label: 'NW Post' },
];

const ALL_LANGUAGES = ['en', 'zh', 'ms'];

const HTML_AD_PLACEMENTS = {
  COVER: 'COVER',
  SLIDER: 'SLIDER',
};

const AI_PN_KEYWORDS_CRITERIA_TYPE = 'AI_PN_KEYWORDS';
const BREAKING_SCORE_CRITERIA_TYPE = 'BREAKING_SCORE';

export {
  ADS_TYPE,
  ADS_TABLE_FIELDS,
  CAMPAIGN_STATUS_COLORS,
  CAMPAIGN_STATUS_ICONS,
  CAMPAIGN_ADS_TABLE_FIELDS,
  CAMPAIGN_FIELD_NAMES,
  DEFAULT_PODS,
  SCALED_PODS,
  GENERAL,
  HTML_AD_TYPES,
  LANGING_PAGE_TYPES,
  ALL_LANGUAGES,
  HTML_AD_PLACEMENTS,
  VIDEO_TYPES,
  IMAGE_TYPES,
  VIDEO_TYPE,
  IMAGE_TYPE,
  AI_PN_KEYWORDS_CRITERIA_TYPE,
  BREAKING_SCORE_CRITERIA_TYPE,
};
