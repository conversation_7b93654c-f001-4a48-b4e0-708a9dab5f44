<template>
  <div>
    <!-- <notifications group="error"  position="bottom right" width="30%" :speed="500" /> -->
    <notifications
      group="error"
      :duration="5000"
      width="60%"
      position="bottom right"
    >
      <template slot="body" slot-scope="props">
        <div class="custom-template">
          <div class="custom-template-icon">
            <i class="icon ion-android-checkmark-circle"></i>
          </div>
          <div class="custom-template-content">
            <div class="custom-template-title">
              {{ props.item.title }}
              <p></p>
            </div>
            <div class="custom-template-text" v-html="props.item.text"></div>
          </div>
          <div class="custom-template-close" @click="props.close">
            <font-awesome-icon icon="times" />
          </div>
        </div>
      </template>
    </notifications>
    <CElementCover
      v-if="loading"
      :boundaries="[{ sides: ['top', 'left'], query: '.media-body' }]"
      :opacity="0.8"
    >
      <h1 class="d-inline">Loading...</h1>
      <CSpinner size="5xl" color="success" />
    </CElementCover>
    <router-view></router-view>
  </div>
</template>

<script>
export default {
  name: 'App',
  computed: {
    loading() {
      return this.$store.getters.getPageLoading;
    },
  },
};
</script>

<style lang="scss">
// Import Main styles for this application
@import 'assets/scss/style';
.custom-template {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  text-align: left;
  font-size: 13px;
  margin: 5px;
  margin-bottom: 15px;
  align-items: center;
  justify-content: center;
  &,
  & > div {
    box-sizing: border-box;
  }
  background: #fdb0b0;
  .custom-template-icon {
    flex: 0 1 auto;
    color: #c31515;
    font-size: 32px;
    padding: 0 10px;
  }
  .custom-template-close {
    flex: 0 1 auto;
    padding: 0 20px;
    font-size: 16px;
    opacity: 0.2;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }
  }
  .custom-template-content {
    padding: 10px;
    flex: 1 0 auto;
    .custom-template-title {
      letter-spacing: 1px;
      text-transform: uppercase;
      font-size: 10px;
      font-weight: 600;
    }
  }
}
</style>
