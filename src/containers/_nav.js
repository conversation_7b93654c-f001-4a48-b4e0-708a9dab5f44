let navItems = [
  {
    _name: 'CSidebarNavItem',
    name: 'Newswav Dashboard',
    to: '/dashboard',
    icon: 'cil-home',
  },
  {
    _name: 'CSidebarNavTitle',
    _children: ['Adwav'],
    roles: ['bd', 'admin', 'investor'],
    actions: ['advertiser_list', 'lead_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Advertisers',
    to: '/advertisers',
    icon: 'cil-speedometer',
    roles: ['bd', 'admin', 'investor'],
    actions: ['advertiser_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Lead Form',
    to: '/lead-forms',
    icon: 'cil-basket',
    roles: ['bd', 'admin'],
    actions: ['lead_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Preview Link Builder',
    to: '/preview-link',
    icon: 'cil-external-link',
    roles: ['bd', 'admin'],
    actions: ['preview_link'],
  },
  {
    _name: 'CSidebarNavTitle',
    _children: ['PollWav'],
    roles: ['bd', 'admin'],
    actions: ['lead_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Poll',
    to: '/polls',
    icon: 'cil-library-add',
    roles: ['bd', 'admin'],
    actions: ['lead_list'],
  },
  {
    _name: 'CSidebarNavTitle',
    _children: ['content'],
    roles: ['cs', 'bd', 'pn', 'admin'],
    actions: ['all_content_list', 'pin_list', 'search_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Announcement',
    to: '/announcements',
    icon: 'cil-voice-over-record',
    actions: ['announcement_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Highlights',
    to: '/highlights',
    icon: 'cil-highlighter',
    actions: ['get_highlights'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'All Content',
    to: '/all-content',
    icon: 'cil-magnifying-glass',
    roles: ['cs', 'bd', 'pn', 'admin'],
    actions: ['all_content_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'All Mikrowav',
    to: '/all-mikrowav',
    icon: 'cil-magnifying-glass',
    roles: ['cs', 'bd', 'pn', 'admin'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Search',
    to: '/search',
    icon: 'cil-magnifying-glass',
    roles: ['cs', 'bd', 'pn', 'admin'],
    actions: ['search_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Pin',
    to: '/pin',
    icon: 'cib-pinboard',
    roles: ['bd', 'pn', 'admin'],
    actions: ['pin_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Reported Content',
    to: '/report-content',
    icon: 'cil-warning',
    roles: ['cs', 'admin'],
    actions: ['list_reported_content'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Upload YT Video',
    to: '/ytvideo',
    icon: 'cil-video',
  },
  {
    _name: 'CSidebarNavTitle',
    _children: ['Comments'],
    roles: ['cs', 'admin'],
    actions: ['all_comment_list', 'report_comment_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'All Comments',
    to: '/comments',
    icon: 'cil-comment-bubble',
    roles: ['cs', 'admin'],
    actions: ['all_comment_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Reported Comments',
    to: '/report-comment',
    icon: 'cil-bullhorn',
    roles: ['cs', 'admin'],
    actions: ['report_comment_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'AI Reported Comments',
    to: '/ai-report-comment',
    icon: 'cib-probot',
    roles: ['cs', 'admin'],
    actions: ['report_comment_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Keyword List',
    to: '/report-keywords',
    icon: 'cil-bullhorn',
    roles: ['cs', 'admin'],
    actions: ['keyword_list'],
  },
  {
    _name: 'CSidebarNavTitle',
    _children: ['Live Comments'],
    actions: [
      'view_live_reported_comments',
      'view_live_banned_users',
      'view_live_sessions',
      'view_ongoing_live_sessions',
    ],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Reported Comments',
    to: '/reported-live-comment',
    icon: 'cil-bullhorn',
    actions: ['view_live_reported_comments'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Live Banned Users',
    to: '/live-banned-users',
    icon: 'cil-people',
    actions: ['view_live_banned_users'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Live Session',
    to: '/live-session',
    icon: 'cil-comment-bubble',
    actions: ['view_live_sessions'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Ongoing Live Session',
    to: '/ongoing-live-session',
    icon: 'cil-comment-bubble',
    actions: ['view_ongoing_live_sessions'],
  },
  {
    _name: 'CSidebarNavTitle',
    _children: ['Push Notifications'],
    roles: ['pn', 'admin'],
    actions: ['pn_test'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Test PN',
    to: '/pn-test',
    icon: 'cil-sync',
    roles: ['pn', 'admin', 'developer'],
    actions: [
      'pn_test',
      'pn_article',
      'pn_podcast',
      'pn_video',
      'pn_delete',
      'pn_cancel',
      'pn_schedule',
    ],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'PN',
    to: '/pn-dashboard',
    icon: 'cil-send',
    roles: ['pn', 'admin'],
    actions: ['pn_article'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'PN 2.0',
    to: '/pn-dashboard-2',
    icon: 'cil-send',
    roles: ['pn', 'admin'],
    actions: ['pn_article'],
  },
  // {
  //     _name: 'CSidebarNavItem',
  //     name: 'PN - Video',
  //     to: '/pn-video',
  //     icon: 'cil-send',
  //     roles:['pn', 'admin'],
  //     actions:['pn_video'],
  // },
  // {
  //     _name: 'CSidebarNavItem',
  //     name: 'PN - Podcast',
  //     to: '/pn-podcast',
  //     icon: 'cil-send',
  //     roles:['pn', 'admin'],
  //     actions:['pn_podcast'],
  // },
  {
    _name: 'CSidebarNavItem',
    name: 'PN - Scheduled',
    to: '/pn-scheduled',
    icon: 'cil-clock',
    roles: ['pn', 'admin'],
    actions: ['pn_schedule'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Cancel/Delete PN',
    to: '/pn-stop',
    icon: 'cil-wifi-signal-off',
    roles: ['pn', 'admin', 'developer'],
    actions: ['pn_cancel', 'pn_delete'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Scale Pods',
    to: '/scale-pods',
    icon: 'cil-mouse',
    roles: ['pn', 'admin', 'developer'],
  },
  {
    _name: 'CSidebarNavTitle',
    _children: ['analytics'],
    roles: ['cs', 'bd', 'pn', 'admin'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Most Viewed Articles',
    to: '/most-viewed-articles',
    icon: 'cil-newspaper',
  },
  {
    _name: 'CSidebarNavTitle',
    _children: ['Publisher'],
    roles: ['publisher', 'admin'],
    actions: ['publisher_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Pub. Management',
    to: '/publishers',
    icon: 'cil-institution',
    roles: ['publisher', 'admin'],
    actions: ['publisher_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Daily eCPM',
    to: '/cpm',
    icon: 'cil-institution',
    roles: ['publisher', 'admin'],
    actions: ['list_cpm'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Monetization',
    to: '/monetization',
    icon: 'cil-institution',
    roles: ['publisher', 'admin'],
    actions: ['list_revenue'],
  },
  {
    _name: 'CSidebarNavTitle',
    _children: ['Users'],
    roles: ['cs', 'admin'],
    actions: ['user_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'User List',
    to: '/users',
    icon: 'cil-people',
    roles: ['cs', 'admin'],
    actions: ['user_list'],
  },
  {
    _name: 'CSidebarNavTitle',
    _children: ['Election'],
    roles: ['pn', 'admin', 'bd'],
    actions: ['election'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Election',
    to: '/election',
    icon: '',
    roles: ['pn', 'admin', 'bd'],
    actions: ['election'],
  },
  {
    _name: 'CSidebarNavTitle',
    _children: ['COVID-19'],
    roles: ['covid', 'admin'],
    actions: ['covid_19'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'COVID-19',
    to: '/covid',
    icon: 'cil-bug',
    roles: ['covid', 'admin'],
    actions: ['covid_19'],
  },
  {
    _name: 'CSidebarNavTitle',
    _children: ['Newsletter'],
    roles: ['admin'],
    actions: ['newsletter_list'],
  },
  {
    _name: 'CSidebarNavItem',
    name: 'Newsletter',
    to: '/newsletter',
    icon: 'cil-comment-square',
    roles: ['admin'],
    actions: ['newsletter_list'],
  },
  // {
  //     _name: 'CSidebarNavItem',
  //     name: 'Blocked Users',
  //     to: '/users/blocked',
  //     icon: 'cil-report-slash'
  // }
];

export default [
  {
    _name: 'CSidebarNav',
    _children: navItems,
  },
];
