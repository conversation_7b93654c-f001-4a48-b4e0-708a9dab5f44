<template>
  <CSidebar
    fixed
    :minimize="minimize"
    :show="show"
    @update:show="(value) => $store.commit('set', ['sidebarShow', value])"
  >
    <!--        <CSidebarBrand class="d-md-down-none" to="/">-->
    <!--            <CIcon-->
    <!--                    class="c-sidebar-brand-full"-->
    <!--                    name="logo"-->
    <!--                    size="custom-size"-->
    <!--                    :height="35"-->
    <!--                    viewBox="0 0 556 134"-->
    <!--            />-->
    <!--            <CIcon-->
    <!--                    class="c-sidebar-brand-minimized"-->
    <!--                    name="logo"-->
    <!--                    size="custom-size"-->
    <!--                    :height="35"-->
    <!--                    viewBox="0 0 110 134"-->
    <!--            />-->
    <!--        </CSidebarBrand>-->
    <!-- <CSpinner  style="width:4rem;height:4rem;margin: 0 auto; top:50%; margin-top:50px"  class="align-middle" color="success"/> -->
    <CRenderFunction
      v-show="!loading"
      flat
      :content-to-render="computedSidebar"
    />

    <CSidebarMinimizer
      v-show="!loading"
      class="d-md-down-none"
      @click.native="$store.commit('set', ['sidebarMinimize', !minimize])"
    />
    <CElementCover v-show="loading" :opacity="0.2" />
  </CSidebar>
</template>

<script>
import navItems from './_nav';

export default {
  name: 'Sidebar',
  data() {
    return {
      role: ['cs', 'covid'],
      nav: navItems[0]._children,
    };
  },
  created() {
    if (this.actions.length == 0) {
      this.$store.dispatch('getRoles');
    }
  },
  computed: {
    show() {
      return this.$store.state.sidebarShow;
    },
    minimize() {
      return this.$store.state.sidebarMinimize;
    },
    actions() {
      return this.$store.getters.getActions;
    },
    loading() {
      return this.$store.getters.getSidebarLoading;
    },
    currentItems() {
      return this.nav.filter((item) => {
        let found = false;
        if (item.actions) {
          for (var i = 0; i < item.actions.length; i++) {
            if (this.actions.indexOf(item.actions[i]) != -1) {
              return true;
            }
          }
        }
        return !item.actions || found;
      });
    },
    computedSidebar() {
      return [
        {
          _name: 'CSidebarNav',
          _children: this.currentItems,
        },
      ];
    },
  },
};
</script>
