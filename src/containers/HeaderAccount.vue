<template>
  <div>
    <CDropdown
      inNav
      class="c-header-nav-items"
      placement="bottom-end"
      add-menu-classes="pt-0"
    >
      <template #toggler>
        <CHeaderNavLink>
          <div class="c-avatar">
            <img
              src="https://cdn.newswav.com/logo/profile.png"
              class="c-avatar-img"
            />
          </div>
        </CHeaderNavLink>
      </template>
      <CDropdownHeader tag="div" class="text-center" color="light">
        <strong>Account</strong>
      </CDropdownHeader>
      <CDropdownItem @click="goToProfile">
        <CIcon name="cil-user" /> Profile
      </CDropdownItem>
      <CDropdownItem @click="$router.push('/settings')">
        <CIcon name="cil-settings" /> Settings
      </CDropdownItem>
      <CDropdownItem @click="callModal">
        <CIcon name="cil-lock-locked" /> Logout
      </CDropdownItem>
    </CDropdown>
    <Logout
      :show="modal_show"
      @cancel-logout="callModal"
      @proceed-logout="logout"
    />
  </div>
</template>

<script>
import Logout from '../components/User/Logout';
export default {
  name: 'HeaderAccount',
  components: { Logout },
  data() {
    return {
      modal_show: false,
    };
  },
  methods: {
    callModal() {
      this.modal_show = !this.modal_show;
    },
    logout() {
      this.$store
        .dispatch('logout')
        .then(() => {
          this.$router.push('/login');
        })
        .catch((err) => {
          console.log(err);
        });
    },
    goToProfile() {
      this.$router.push('/profile');
    },
  },
};
</script>

<style scoped>
.c-icon {
  margin-right: 0.3rem;
}
</style>
