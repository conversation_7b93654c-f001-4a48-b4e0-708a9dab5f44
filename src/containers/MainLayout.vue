<template>
  <div>
    <div class="c-app">
      <Sidebar />
      <CWrapper>
        <Header />
        <div class="c-body">
          <main class="c-main">
            <CContainer fluid>
              <transition name="fade">
                <router-view></router-view>
              </transition>
            </CContainer>
          </main>
        </div>
      </CWrapper>
    </div>
    <v-idle @idle="logoutUser" class="invisible" :duration="3600" />
    <auto-logout class="invisible" @timeout="logoutUser(true)" />
  </div>
</template>

<script>
import Sidebar from './Sidebar';
import Header from './Header';
import AutoLogout from '../components/views/AutoLogout';

export default {
  name: 'MainLayout',
  components: {
    AutoLogout,
    Sidebar,
    Header,
  },
  methods: {
    logoutUser(auto = false) {
      this.$store
        .dispatch('logout')
        .then(() => {
          if (auto === true) alert('4-hours timeout. Logging out');
          else alert("You've been inactive for 1-hour. Logging out");
          this.$router.push('/login');
        })
        .catch((err) => {
          if (auto === true) alert('4-hours timeout. Logging out');
          else alert("You've been inactive for 1-hour. Logging out");
          this.$router.push('/login');
          console.log(err);
        });
    },
  },
};
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
