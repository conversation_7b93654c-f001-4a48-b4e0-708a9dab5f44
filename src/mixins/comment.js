export default {
  methods: {
    truncate: function (text, length, suffix) {
      if (!text) return '';
      if (text.length > length) {
        return text.substring(0, length) + suffix;
      } else {
        return text;
      }
    },
    formatMessage(data) {
      let message = data.message;
      for (const tagged of data.taggedUsers) {
        message = message.replace(
          `[~accountid:${tagged.firebaseId}]`,
          `<b>@${tagged.name}</b>`
        );
      }
      return message;
    },
    formatMessageByUsername(data) {
      let message = data.message;
      const taggedUsers = data.taggedUser ?? [];
      for (const tagged of taggedUsers) {
        message = message.replace(`@${tagged.name}`, `<b>@${tagged.name}</b>`);
      }
      return message;
    },
    formatTones(data) {
      let html = `<ul>`;
      for (const tone of [...new Set(data.aiData.tones.map((i) => i.tone))]) {
        html += `
            <li>${tone}</li>
            `;
      }
      return `${html}
          </ul>`;
    },
    formatApproval(data) {
      return `<b>${data.aiData.approval}</b>`;
    },
    formatReason(data) {
      return `${data.aiData.reason}`;
    },
  },
};
