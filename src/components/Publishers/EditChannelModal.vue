<template>
  <div>
    <CButton variant="outline" color="warning" @click="show = !show">
      <CIcon name="cil-pencil"></CIcon>
    </CButton>
    <CForm @submit.prevent="editChannel">
      <CModal
        :close-on-backdrop="false"
        title="Edit Channel"
        :show.sync="show"
        centered
      >
        <BaseChannelForm :channel="new_channel" :isVisible="true" />

        <template slot="footer">
          <CButton
            type="submit"
            color="success"
            variant="outline"
            :disabled="ch_disabled"
            >Save</CButton
          >
        </template>
      </CModal>
    </CForm>
  </div>
</template>

<script>
import BaseChannelForm from '@/components/Publishers/BaseChannelForm';
export default {
  name: 'EditChannelModal',
  components: { BaseChannelForm },
  props: {
    channel: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      show: false,
      new_channel: JSON.parse(JSON.stringify(this.channel)),
    };
  },
  watch: {
    channel: {
      handler(newChannel) {
        this.new_channel = JSON.parse(JSON.stringify(newChannel));
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ch_disabled() {
      let errors = [];
      let onlyCheck = ['host', 'ch_language', 'enabled', 'content_type'];
      Object.entries(this.new_channel).forEach(([prop, val]) => {
        if (onlyCheck.includes(prop) && val === '') {
          errors.unshift(prop);
        }
        if (prop === 'content_type' && val === 'article') {
          if (this.new_channel.isReaderMode === '') {
            errors.unshift('isReaderMode');
          }
        }
      });
      return errors.length > 0;
    },
  },
  methods: {
    editChannel() {
      this.$swal
        .fire({
          icon: 'warning',
          text: 'This will update the channel settings. Continue?',
        })
        .then((res) => {
          if (res.isConfirmed) {
            let cha = JSON.parse(JSON.stringify(this.new_channel));
            let pass = {};
            Object.entries(cha).forEach(([prop, val]) => {
              pass[prop] = val;
            });
            this.$store
              .dispatch('editPublisherChannel', pass)
              .then(() => {
                this.$swal
                  .fire({
                    icon: 'success',
                    text: 'Channels settings successfully updated!',
                    showCancelButton: false,
                  })
                  .then(() => {
                    this.show = !this.show;
                    this.$store.dispatch(
                      'listPublisherById',
                      parseInt(this.$route.params.pub_id)
                    );
                  });
              })
              .catch((err) => {
                let text = 'Something went wrong in updating channel';
                if (err.response.data.msg) {
                  text = err.response.data.msg;
                }
                this.$swal.fire({
                  icon: 'error',
                  text: text,
                  showCancelButton: false,
                });
              });
          }
        });
    },
  },
};
</script>

<style scoped></style>
