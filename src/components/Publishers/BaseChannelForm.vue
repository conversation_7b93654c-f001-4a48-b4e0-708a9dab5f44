<template>
  <div>
    <CSelect
      :value.sync="channel.content_type"
      label="Content Type"
      :options="channel_type"
      placeholder="Select Content Type"
    />
    <CTextarea
      v-model="channel.host"
      label="Website / Feed"
      invalid-feedback="Website / Feed must start with http:// or https://"
      :is-valid="urlValidator"
    />
    <CRow>
      <CCol>Language</CCol>
      <CCol>
        <div
          class="form-check form-check-inline"
          v-for="(option, optionIndex) in language_options"
          :key="option.value + optionIndex + 'chn'"
        >
          <input
            class="form-check-input"
            type="radio"
            :value="option.value"
            :id="option.value + optionIndex + 'chn'"
            v-model="channel.ch_language"
          />
          <label
            class="form-check-label"
            :for="option.value + optionIndex + 'pn'"
            >{{ option.label }}</label
          >
        </div>
      </CCol>
    </CRow>
    <div v-if="channel.content_type === 'article'">
      <br />
      <CRow>
        <CCol>Native</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in yes_no_options"
            :key="(option.value % 1) + optionIndex + 'yesno_reader'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="(option.value % 1) + optionIndex + 'yesno_reader'"
              :value="option.value"
              v-model="channel.isReaderMode"
            />
            <label
              class="form-check-label"
              :for="(option.value % 1) + optionIndex + 'yesno_reader'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
    </div>
    <div>
      <br />
      <CRow>
        <CCol>Status</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in enabled_options"
            :key="(option.value % 1) + optionIndex + 'yesno_reader'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="(option.value % 1) + optionIndex + 'yesno_reader'"
              :value="option.value"
              v-model="channel.enabled"
            />
            <label
              class="form-check-label"
              :for="(option.value % 1) + optionIndex + 'yesno_reader'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
    </div>
    <br />
    <CSelect
      v-if="isVisible"
      :disabled="isCrawlerFrequencyNull"
      label="Crawler Frequency"
      :options="displayCrawlerFrequencyOption"
      v-model="channel.crawler_frequency"
      :values.sync="channel.crawler_frequency"
    />
  </div>
</template>

<script>
export default {
  name: 'BaseChannelForm',
  props: {
    channel: {
      type: Object,
      required: true,
    },
    isVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      yes_no_options: [
        { value: 1, label: 'Yes' },
        { value: 0, label: 'No' },
      ],
      channel_type: [
        { value: 'article', label: 'Article' },
        { value: 'video', label: 'Video' },
        { value: 'podcast', label: 'Podcast' },
      ],
      language_options: [
        { value: 'en', label: 'EN' },
        { value: 'ms', label: 'MS' },
        { value: 'zh', label: 'ZH' },
      ],
      enabled_options: [
        { value: 1, label: 'Enabled' },
        { value: 0, label: 'Disabled' },
      ],
      crawler_frequency_options: [
        { value: 3, label: '3 Minutes' },
        { value: 5, label: '5 Minutes' },
        { value: 10, label: '10 Minutes' },
        { value: 15, label: '15 Minutes' },
        { value: 30, label: '30 Minutes' },
        { value: 60, label: '1 Hour' },
        { value: 720, label: '12 Hours' },
        { value: 1440, label: '24 Hours' },
      ],
      null_option: [{ value: null, label: 'null' }],
    };
  },
  computed: {
    isCrawlerFrequencyNull() {
      return this.channel.crawler_frequency === null;
    },
    displayCrawlerFrequencyOption() {
      return this.channel.crawler_frequency === null
        ? this.null_option
        : this.crawler_frequency_options;
    },
  },
  methods: {
    urlValidator(val) {
      return (
        (val.includes('http://') || val.includes('https://')) && val !== ''
      );
    },
  },
};
</script>

<style scoped></style>
