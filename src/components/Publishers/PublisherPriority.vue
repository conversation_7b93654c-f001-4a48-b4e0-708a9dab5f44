<template>
  <div>
    <div v-show="checkAction('pub_prio_add')">
      <CButton variant="outline" color="info" shape="pill" @click="show = !show"
        >PN-Prioritized Pub</CButton
      >
    </div>
    <CModal
      :close-on-backdrop="false"
      centered
      size="xl"
      title="Prioritized Pub List"
      :show.sync="show"
      @update:show="prio_items = []"
    >
      <CRow>
        <CCol>
          <CRow>
            <CCol lg="10">
              <label for="selectItems">Add to PN-Prioritized Pub</label>
              <multiselect
                id="selectItems"
                v-model="prio_items"
                :options="total_publisher_list"
                :multiple="true"
                track-by="id"
                label="name"
              >
                <span slot="noResult"
                  >Oops! No items found. Consider changing the search
                  query.</span
                >
              </multiselect>
            </CCol>
            <CCol lg="2">
              <CButton
                color="success"
                variant="outline"
                :disabled="prio_items.length === 0"
                style="margin-top: 30px; height: 40px"
                @click="addToPrio"
                >Add
              </CButton>
            </CCol>
          </CRow>
        </CCol>
      </CRow>
      <br />
      <CRow>
        <CCol>
          <CDataTable
            :key="tableKey"
            :sorter-value="{ column: 'name', asc: true }"
            :fields="pub_prio_fields"
            :loading="loading"
            :items="pub_prio_list"
            pagination
            :items-per-page="10"
            items-per-page-select
            sorter
            table-filter
          >
            <template #action="{ item }">
              <td>
                <CButtonToolbar>
                  <div v-show="checkAction('pub_prio_delete')">
                    <CButton
                      variant="outline"
                      color="danger"
                      v-c-tooltip="'Remove from PN-Prioritized Pub list'"
                      @click="removeFromPrio(item)"
                    >
                      <CIcon name="cil-trash"></CIcon>
                    </CButton>
                  </div>
                </CButtonToolbar>
              </td>
            </template>
          </CDataTable>
        </CCol>
      </CRow>
      <template slot="footer">
        <CButton color="secondary" @click="show = !show">Close</CButton>
      </template>
    </CModal>
  </div>
</template>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<script>
import { mapGetters, mapActions } from 'vuex';
import Multiselect from 'vue-multiselect';

export default {
  name: 'FollowList',
  components: { Multiselect },
  data() {
    return {
      show: false,
      prio_items: [],
      pub_prio_fields: [
        { key: 'name', label: 'Name' },
        { key: 'action', label: 'Action', sorter: false, filter: false },
      ],
    };
  },
  computed: {
    ...mapGetters({
      pub_prio_list: 'getPrioritizedPubList',
      loading: 'getDefaultLoading',
      allPublisher: 'getAllPublisher',
    }),
    tableKey() {
      if (this.pub_prio_list.length > 0) {
        const idx = Math.floor(Math.random() * this.pub_prio_list.length);
        return this.pub_prio_list[idx].id;
      }
      return Math.random();
    },
    total_publisher_list() {
      let data = [];
      this.allPublisher.forEach((i) => {
        let found = this.pub_prio_list.find(
          (data) => data.publisher_id === i.id
        );
        if (!found) {
          let item = {
            id: i.id,
            name: i.name,
          };
          data.push(item);
        }
      });
      data.sort((a, b) => a.name.localeCompare(b.name));
      return data;
    },
  },
  methods: {
    ...mapActions({
      addToPubPriorityList: 'addToPubPriorityList',
      updatePubPriorityList: 'updatePubPriorityList',
      listPrioritizedPubs: 'listPubPriority',
    }),
    addToPrio() {
      this.$swal
        .fire({
          icon: 'question',
          text: 'This will add selections to PN-Prioritized Pub list. Continue?',
        })
        .then((res) => {
          if (res.isConfirmed) {
            let pass = this.prio_items;
            let postObj = { pubId: [] };
            pass.forEach((item) => {
              postObj.pubId.unshift(item.id);
            });
            this.addToPubPriorityList(postObj)
              .then(() => {
                this.$swal
                  .fire({
                    icon: 'success',
                    text: `Successfully added selections PN-Prioritized Pub list.`,
                    showCancelButton: false,
                  })
                  .then(() => {
                    this.prio_items = [];
                    this.listPrioritizedPubs();
                  });
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'warning',
                  text: `Something went wrong in adding selections from PN-Prioritized Pub list.`,
                  showCancelButton: false,
                });
              });
          }
        });
    },
    removeFromPrio(item) {
      this.$swal
        .fire({
          icon: 'question',
          text: `This will remove ${item.name} from PN-Prioritized Pub. Continue?`,
        })
        .then((res) => {
          if (res.isConfirmed) {
            this.updatePubPriorityList(item.id)
              .then(() => {
                this.$swal.fire({
                  icon: 'success',
                  text: `Successfully removed ${item.name} from PN-Prioritized Pub.`,
                  showCancelButton: false,
                });
                this.listPrioritizedPubs();
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'warning',
                  text: `Something went wrong in removing ${item.name} from PN-Prioritized Pub.`,
                  showCancelButton: false,
                });
              });
          }
        });
    },
  },
};
</script>

<style scoped></style>
