<template>
  <div>
    <CRow>
      <CCol sm="12" md="4" lg="4" xl="4"
        ><CInput :value="publisher.content" label="Content Type" readonly
      /></CCol>
      <CCol sm="12" md="4" lg="4" xl="4"
        ><CInput
          :value="publisher.languageShow ? langShow : ''"
          label="Language"
          readonly
      /></CCol>
      <CCol sm="12" md="4" lg="4" xl="4"
        ><CInput :value="publisher.isNative" label="Native" readonly
      /></CCol>
    </CRow>
    <br />
    <CRow>
      <CCol sm="12" md="4" lg="4" xl="4"
        ><CInput :value="publisher.topicShow" label="Topic" readonly
      /></CCol>
      <CCol sm="12" md="4" lg="4" xl="4"
        ><CInput :value="publisher.hasLogo" label="Logo" readonly
      /></CCol>
      <CCol sm="12" md="4" lg="4" xl="4"
        ><CInput :value="publisher.autoFollowShow" label="Auto Follow" readonly
      /></CCol>
    </CRow>
  </div>
</template>

<script>
export default {
  name: 'PublisherDetails',
  props: {
    publisher: {
      type: Object,
      required: true,
    },
    logo_display: {
      type: String,
      required: true,
    },
  },
  computed: {
    langShow() {
      return this.publisher.languageShow.toUpperCase();
    },
  },
};
</script>

<style scoped></style>
