<template>
  <div>
    <OverlayLoader v-if="loading" />
    <CInput
      :description="publisher.name ? publisher.name.length + ' Characters' : ''"
      label="Name"
      v-model="publisher.name"
      invalid-feedback="Name is required"
      :is-valid="publisher.name ? publisher.name.length > 0 : false"
      :disabled="publisher.project_type === 'ugc'"
    />
    <CInput
      :description="
        publisher.description
          ? publisher.description.length + ' Characters'
          : ''
      "
      label="Description"
      v-model="publisher.description"
      invalid-feedback="Description is required"
      :is-valid="
        publisher.description ? publisher.description.length > 0 : false
      "
      :disabled="publisher.project_type === 'ugc'"
    />
    <CInput
      label="Website URL"
      v-model="publisher.website_url"
      invalid-feedback="Website URL is required"
      :is-valid="
        (publisher.website_url ? publisher.website_url.length > 0 : false) &&
        urlValidator
      "
      :disabled="publisher.project_type === 'ugc'"
    />
    <CRow>
      <CCol>
        <label for="adIconFile1">Logo</label>
      </CCol>
      <CCol>
        <label
          class="custom-file-upload"
          :class="publisher.project_type === 'ugc' ? 'ugc' : ''"
        >
          <input
            type="file"
            id="adIconFile1"
            @change="onFileChange($event)"
            ref="inputIcon"
            class="form-control-file"
            data-content="icon"
            accept=".png, .jpg, .jpeg"
            :disabled="publisher.project_type === 'ugc'"
          />
          <i class="fa fa-cloud-upload"></i> Upload
        </label>
      </CCol>
    </CRow>
    <div>
      <br />
      <CRow>
        <CCol> Verified </CCol>
        <CCol class="choices">
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in yes_no_options"
            :key="(option.value % 1) + optionIndex + 'yesno'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="(option.value % 1) + optionIndex + 'yesno'"
              :value="option.value"
              v-model="publisher.verified"
            />
            <label
              class="form-check-label"
              :for="(option.value % 1) + optionIndex + 'yesno'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
      <br />
    </div>
    <div>
      <CRow>
        <CCol>Status</CCol>
        <CCol class="choices" :style="{ 'flex-wrap': 'wrap' }">
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in enabled_options"
            :style="{ 'margin-bottom': '4px' }"
            :key="(option.value % 1) + optionIndex + 'enable'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="(option.value % 1) + optionIndex + 'enable'"
              :value="option.value"
              v-model="publisher.enabled"
            />
            <label
              class="form-check-label"
              :for="(option.value % 1) + optionIndex + 'enable'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
      <br />
    </div>
    <div>
      <!-- NWGEN 490 -->
      <!-- <CRow>
        <CCol>
          Language(s)
        </CCol>
        <CCol>
          <div class="form-check form-check-inline" v-for="(option, optionIndex) in language_options"
               :for="option.value+optionIndex+'pn'" :key="option.value % 1 + optionIndex +'pn'">
            <input v-model="publisher.language" :id="option.value+optionIndex+'pn'" class="form-check-input" type="checkbox" :value="option.value" 
              :disabled="publisher.project_type === 'ugc'">
            <label class="form-check-label" :for="option.value+optionIndex+'pn'">{{ option.label }}</label>
          </div>
        </CCol>
      </CRow> -->
      <br />
    </div>
    <!--    <div>-->
    <!--      <CRow>-->
    <!--        <CCol>-->
    <!--          Native-->
    <!--        </CCol>-->
    <!--        <CCol>-->
    <!--          <div class="form-check form-check-inline" v-for="(option, optionIndex) in yes_no_options"-->
    <!--               :key="option.value % 1 + optionIndex +'yesno_reader'">-->
    <!--            <input class="form-check-input" type="radio" :id="option.value % 1 +  optionIndex +'yesno_reader'"-->
    <!--                   :value="option.value"-->
    <!--                   v-model="publisher.isReaderMode">-->
    <!--            <label class="form-check-label" :for="option.value % 1 +  optionIndex+'yesno_reader'">{{ option.label }}</label>-->
    <!--          </div>-->
    <!--        </CCol>-->
    <!--      </CRow>-->
    <!--      <br>-->
    <!--    </div>-->
    <CInput v-model="publisher.ga_id" label="GA Tracking ID" />
    <CSelect
      label="Publisher Type"
      placeholder="Select Publisher Type"
      :options="
        publisher.project_type === 'ugc'
          ? publisher_type
          : publisher_type.filter((type) => type.value !== 'ugc')
      "
      :value.sync="publisher.project_type"
      :disabled="publisher.project_type === 'ugc'"
    />
    <label class="typo__label">Topics</label>
    <multiselect
      id="topic"
      v-model="publisher.topics"
      :options="topicOptions"
      :multiple="true"
      :close-on-select="false"
      :clear-on-select="false"
      :preserve-search="true"
      placeholder="Topics"
      label="label"
      track-by="value"
      :preselect-first="false"
      select-label=""
      deselect-label=""
      @input="handleTopics($event)"
      style="margin-bottom: 1rem"
    >
    </multiselect>
    <div>
      <CRow>
        <CCol> Auto Follow </CCol>
        <CCol class="choices">
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in yes_no_options"
            :for="option.value + optionIndex + 'atf'"
            :key="(option.value % 1) + optionIndex + 'atf'"
          >
            <input
              class="form-check-input"
              type="radio"
              :value="option.value"
              :id="option.value + optionIndex + 'atf'"
              v-model="publisher.autoFollow"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'atf'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
      <br />
    </div>
    <CInput
      type="number"
      step="0.1"
      v-model="publisher.ad_share"
      placeholder="Please input Ad Share Rev"
      label="Ad Share Rev (%)"
      invalid-feedback="Ad Share Rev is required"
      :is-valid="publisher.ad_share != ''"
    />
    <div>
      <br />
      <CRow>
        <CCol> Monitoring </CCol>
        <CCol class="choices">
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in on_off_options"
            :key="optionIndex + 'onoff'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="optionIndex + 'onoff'"
              :value="option.value"
              v-model="publisherMonitoring.status"
            />
            <label class="form-check-label" :for="optionIndex + 'onoff'">{{
              option.label
            }}</label>
          </div>
        </CCol>
      </CRow>
      <br />
    </div>
    <div v-if="publisherMonitoring.status === 1">
      <br />
      <CRow>
        <CCol> Monitoring Frequency </CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in frequency_options"
            :key="optionIndex + 'frequency'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="optionIndex + 'frequency'"
              :value="option.value"
              v-model="publisherMonitoring.frequency"
              invalid-feedback="Frequency is required"
              :is-valid="publisherMonitoring.frequency !== ''"
            />
            <label class="form-check-label" :for="optionIndex + 'frequency'">{{
              option.label
            }}</label>
          </div>
        </CCol>
      </CRow>
      <br />
    </div>
  </div>
</template>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<script>
import { mapGetters } from 'vuex';
import slugify from 'slugify';
import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import OverlayLoader from '@/components/views/OverlayLoader';
import Multiselect from 'vue-multiselect';

export default {
  name: 'BasePublisherForm',
  components: { OverlayLoader, Multiselect },
  props: {
    publisher: {
      type: Object,
      required: true,
    },
    publisherMonitoring: {
      type: Object,
      required: true,
    },
    edit: {
      type: Boolean,
      required: true,
      default: false,
    },
  },
  data() {
    return {
      language_options: [
        { value: 'en', label: 'EN' },
        { value: 'ms', label: 'MS' },
        { value: 'zh', label: 'ZH' },
      ],
      yes_no_options: [
        { value: 1, label: 'Yes' },
        { value: 0, label: 'No' },
      ],
      on_off_options: [
        { value: 1, label: 'On' },
        { value: 0, label: 'Off' },
      ],
      frequency_options: [
        { value: 'high', label: 'High' },
        { value: 'medium', label: 'Medium' },
        { value: 'low', label: 'Low' },
      ],
      enabled_options: [
        { value: 1, label: 'Enabled' },
        { value: 0, label: 'Disabled' },
      ],
      publisher_type: [
        { value: 'headliner', label: 'Headliner' },
        { value: 'newswav', label: 'Newswav' },
        { value: 'publisher', label: 'Publisher' },
        { value: 'wavmaker', label: 'Wavmaker' },
        { value: 'lokalwav', label: 'Lokalwav' },
        { value: 'ugc', label: 'UGC' },
      ],
      content_type: [
        { value: 'article', label: 'Articles' },
        { value: 'video', label: 'Videos' },
        { value: 'podcast', label: 'Podcasts' },
      ],
    };
  },
  computed: {
    ...mapGetters({
      loading: 'getLoading',
      allTopics: 'getTopics',
    }),
    loading() {
      return this.$store.getters.getLoading;
    },
    topicOptions() {
      return [{ value: '-1', label: '-' }, ...this.allTopics];
    },
  },
  methods: {
    urlValidator(val) {
      return !!(val.includes('http://') || val.includes('https://'));
    },
    onFileChange(event) {
      let input = event.target;
      if (input.files && input.files[0]) {
        let iSize = input.files[0].size / 1000 / 1000;
        if (iSize <= 0.5) {
          this.getIconDimension(input);
        } else {
          alert('File size exceeded. Max 500kb');
          this.$refs.inputIcon.value = null;
        }
      }
    },
    getIconDimension(input) {
      let file = input.files[0];
      if (!file || file.type.indexOf('image/') !== 0) {
        alert('image not found');
      } else {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (evt) => {
          let img = new Image();
          img.onload = () => {
            let w = img.width;
            let h = img.height;
            let r = this.gcd(w, h);

            var error = 'Logo must be in 1:1 ratio';
            var a1 = w / r;
            var a2 = h / r;
            if (a1 == 1 && a2 == 1) {
              this.googleUpload(input);
            } else {
              alert(error);
              this.$refs.inputIcon.value = null;
            }
          };
          img.src = evt.target.result;
        };
        reader.onerror = (evt) => {
          console.error(evt);
        };
      }
    },
    gcd(a, b) {
      return b == 0 ? a : this.gcd(b, a % b);
    },
    googleUpload: function (input) {
      let fileName = slugify(input.files[0].name);
      let fileData = new FormData();
      fileData.append('path', 'boss-images/temp');
      fileData.append('file', input.files[0]);
      fileData.append('name', fileName);
      this.$store.commit('SET_GENERAL_LOADING', true);
      axios
        .post(process.env.VUE_APP_UPLOAD_FILE_TO_GCS, fileData)
        .then((res) => {
          let send = {
            data: res.data.url,
          };
          this.$emit('setImageLink', send);
          this.$store.commit('SET_GENERAL_LOADING', false);
        })
        .catch((error) => {
          this.$store.commit('SET_GENERAL_LOADING', false);
          alert('Something went wrong with uploading media');
          console.log('----Error----');
          console.log(error);
          Bugsnag.notify(error);
        });
    },
    handleTopics(value) {
      if (value.length > 0) {
        if (value.at(-1).value === '-1') {
          this.publisher.topics = value = this.publisher.topics.filter(
            (top) => top.value === '-1'
          );
        } else if (value.at(-1).value !== '-1') {
          this.publisher.topics = value = this.publisher.topics.filter(
            (top) => top.value !== '-1'
          );
        }
      }
    },
  },
};
</script>
<style scoped>
input[type='file'] {
  display: none;
}

.custom-file-upload {
  border: 1px solid #ccc;
  display: inline-block;
  padding: 6px 12px;
  cursor: pointer;
  border-radius: 0.25rem;
}

.custom-file-upload.ugc {
  background-color: #d8dbe0;
  cursor: default;
}

@media (max-width: 750px) {
  .choices {
    display: flex;
    justify-content: space-between;
  }
}
</style>

<style>
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  margin: 0 !important;
}

input[type='number'] {
  -moz-appearance: textfield !important; /* Firefox */
}
</style>
