<template>
  <div>
    <CCard>
      <CCardHeader> Publishers List </CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <CButtonToolbar class="float-right">
              <router-link :to="'/publishers/create'">
                <CButton color="success" variant="outline" shape="pill"
                  >Add Publisher</CButton
                >
              </router-link>
              <!--              <AddPublisherModal/>-->
              <FollowList />
              <PublisherPriority />
            </CButtonToolbar>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CDataTable
              :sorter-value="{ column: 'name', asc: true }"
              :key="tableKey"
              v-bind:loading="publisherLoading"
              small
              :items="allPublisher"
              :fields="fields"
              items-per-page-select
              :items-per-page="perPage"
              :sorter="{ external: true, resetable: false }"
              :sorterValue="sorterValue"
              :columnFilter="{ external: true, lazy: true }"
              :columnFilterValue="columnFilterValue"
              @update:sorter-value="handleSort"
              @update:column-filter-value="handleFilters"
              @pagination-change="handlePageSize"
            >
              <template #content-filter>
                <multiselect
                  :value="contentTypeFilter"
                  :options="contentTypeOptions"
                  :multiple="true"
                  :close-on-select="false"
                  :clear-on-select="false"
                  :preserve-search="true"
                  placeholder="Content Type"
                  label="label"
                  track-by="value"
                  :preselect-first="false"
                  select-label=""
                  deselect-label=""
                  style="min-width: 200px"
                  @input="handleCustomFilter($event, 'contentType')"
                >
                </multiselect>
                <!-- <v-select :value="languagesFilter" :options="languageOptions" multiple @option:selected="handleCustomFilter($event, 'languages')"></v-select> -->
              </template>
              <template #isVerified-filter>
                <CSelect
                  :value="verifiedFilter"
                  :options="verifiedFilterOptions"
                  size="sm"
                  @update:value="handleCustomFilter($event, 'verified')"
                ></CSelect>
              </template>
              <template #isEnabled-filter>
                <CSelect
                  :value="statusFilter"
                  :options="statusFilterOptions"
                  size="sm"
                  @update:value="handleCustomFilter($event, 'status')"
                ></CSelect>
              </template>
              <template #isNative-filter>
                <CSelect
                  :value="nativeFilter"
                  :options="nativeFilterOptions"
                  size="sm"
                  @update:value="handleCustomFilter($event, 'native')"
                ></CSelect>
              </template>
              <template #autoFollowShow-filter>
                <CSelect
                  :value="autoFollowFilter"
                  :options="autoFollowFilterOptions"
                  size="sm"
                  @update:value="handleCustomFilter($event, 'autoFollow')"
                ></CSelect>
              </template>
              <template #project_show-filter>
                <CSelect
                  :value="projectTypeFilter"
                  :options="projectTypeFilterOptions"
                  size="sm"
                  @update:value="handleCustomFilter($event, 'projectType')"
                ></CSelect>
              </template>
              <template #topicShow-filter>
                <multiselect
                  v-model="topicFilter"
                  :options="topicsFilterOptions"
                  :multiple="true"
                  :close-on-select="false"
                  :clear-on-select="false"
                  :preserve-search="true"
                  placeholder="Topics"
                  label="label"
                  track-by="value"
                  :preselect-first="false"
                  select-label=""
                  deselect-label=""
                  @input="handleCustomFilter($event, 'topics')"
                >
                </multiselect>
              </template>
              <template #topicShow="{ item }">
                <td v-if="item.topicShow.length > 0">
                  <ul style="padding-left: 15px">
                    <li v-for="(topic, index) in item.topicShow" :key="index">
                      {{ topic }}
                    </li>
                  </ul>
                </td>
                <td v-else>-</td>
              </template>
              <template #languageShow-filter>
                <multiselect
                  v-model="languagesFilter"
                  :options="languageOptions"
                  :multiple="true"
                  :close-on-select="false"
                  :clear-on-select="false"
                  :preserve-search="true"
                  placeholder="Language"
                  label="label"
                  track-by="value"
                  :preselect-first="false"
                  select-label=""
                  deselect-label=""
                  @input="handleCustomFilter($event, 'languages')"
                >
                </multiselect>
              </template>
              <template #languageShow="{ item }">
                <td>
                  {{ item.languageShow | uppercase }}
                </td>
              </template>
              <template #ad_share="{ item }">
                <td>
                  {{ item.ad_share | percentage }}
                </td>
              </template>
              <template #show_details="{ item }">
                <td>
                  <CButtonToolbar>
                    <div v-show="checkAction('publisher_edit')">
                      <CButton
                        variant="outline"
                        color="warning"
                        v-c-tooltip="'Edit Publisher'"
                        @click="$router.push('publishers/edit-pub/' + item.id)"
                      >
                        <CIcon name="cil-pencil"></CIcon>
                      </CButton>
                    </div>
                    <router-link :to="'/publishers/' + item.id">
                      <CButton
                        color="primary"
                        variant="outline"
                        v-c-tooltip="'Show Publisher'"
                      >
                        <CIcon name="cil-share"></CIcon>
                      </CButton>
                    </router-link>
                  </CButtonToolbar>
                </td>
              </template>
            </CDataTable>
            <CPagination
              @update:activePage="goToPage"
              :pages="pagination.last_page"
              :activePage.sync="pagination.current_page"
              align="center"
            />
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import Multiselect from 'vue-multiselect';
import FollowList from '@/components/Publishers/FollowList';
import PublisherPriority from '@/components/Publishers/PublisherPriority';
import numeral from 'numeral';
const statusFilterOptions = [
  { value: '', label: 'All' },
  { value: 1, label: 'Enabled' },
  { value: 0, label: 'Disabled' },
];
const yesNoOptions = [
  { value: '', label: 'All' },
  { value: 1, label: 'Yes' },
  { value: 0, label: 'No' },
];
const nativeOptions = [
  { value: '', label: 'All' },
  { value: 1, label: 'Yes' },
  { value: 0, label: 'No' },
  { value: 'mixed', label: 'Mixed' },
];
const projectTypeFilterOptions = [
  { value: '', label: 'All' },
  { value: 'headliner', label: 'headliner' },
  { value: 'wavmaker', label: 'wavmaker' },
  { value: 'newswav', label: 'newswav' },
  { value: 'publisher', label: 'publisher' },
  { value: 'lokalwav', label: 'lokalwav' },
  { value: 'rookie', label: 'rookie' },
  { value: 'ugc', label: 'ugc' },
];
const languageOptions = [
  { value: 'en', label: 'EN' },
  { value: 'ms', label: 'MS' },
  { value: 'zh', label: 'ZH' },
];
const contentTypeOptions = [
  { value: 'article', label: 'Article' },
  { value: 'video', label: 'Video' },
  { value: 'podcast', label: 'Podcast' },
];

export default {
  name: 'ListPublishers',
  components: { FollowList, PublisherPriority, Multiselect },
  computed: {
    ...mapGetters({
      allPublisher: 'getAllPublisher',
      publisherLoading: 'getPublisherLoading',
      allTopics: 'getTopics',
      pagination: 'getPublisherPagination',
      sorterValue: 'getPublisherSort',
      columnFilterValue: 'getPublisherFilters',
      defaultFollowList: 'getDefaultFollowList',
      totalList: 'getTotalList',
      prioritizedPubs: 'getPrioritizedPubList',
    }),
    tableKey() {
      if (this.allPublisher.length > 0) {
        const idx = Math.floor(Math.random() * this.allPublisher.length);
        return this.allPublisher[idx].id;
      }
      return Math.random();
    },
    perPage() {
      return this.pagination?.['per_page'];
    },
    topicsFilterOptions() {
      return [
        { value: '', label: 'All' },
        { value: '-1', label: '-' },
        ...this.allTopics,
      ];
    },
  },
  data() {
    return {
      fields: [
        { key: 'id', sorter: true, filter: true, label: 'ID' },
        { key: 'name', sorter: true, label: 'Name' },
        { key: 'content', sorter: false, filter: true, label: 'Content Type' },
        { key: 'isVerified', sorter: true, label: 'Verified' },
        { key: 'isEnabled', sorter: true, label: 'Status' },
        { key: 'isNative', sorter: true, filter: true, label: 'Native' },
        { key: 'languageShow', sorter: true, label: 'Language' },
        { key: 'ga_id', sorter: true, label: 'GA Tracking ID' },
        { key: 'project_show', sorter: true, filter: true, label: 'Type' },
        { key: 'topicShow', sorter: false, filter: false, label: 'Topic' },
        {
          key: 'autoFollowShow',
          sorter: false,
          filter: true,
          label: 'Auto Follow',
        },
        { key: 'ad_share', sorter: true, filter: true, label: 'Ad Share Rev' },
        { key: 'show_details', sorter: false, filter: false, label: 'Action' },
      ],
      verifiedFilter: '',
      verifiedFilterOptions: yesNoOptions,
      statusFilter: '',
      statusFilterOptions,
      nativeFilter: '',
      nativeFilterOptions: nativeOptions,
      autoFollowFilter: '',
      autoFollowFilterOptions: yesNoOptions,
      projectTypeFilter: '',
      projectTypeFilterOptions,
      languagesFilter: '',
      languageOptions,
      topicFilter: [],
      contentTypeFilter: '',
      contentTypeOptions,
    };
  },
  filters: {
    logo(val) {
      return val !== '' ? 'Yes' : 'No';
    },
    verify(val) {
      return val !== 0 ? 'Yes' : 'No';
    },
    uppercase(val) {
      return val.toUpperCase();
    },
    percentage(val) {
      return `${numeral(val).format('0.0')}%`;
    },
  },
  created() {
    if (this.allTopics.length === 0) {
      this.listTopics().then(() => {
        this.initApiCalls();
      });
    } else {
      this.initApiCalls();
    }
  },
  methods: {
    ...mapActions({
      listPublishers: 'listAllPublishers',
      listTotalList: 'listTotalList',
      listPrioritisedPubs: 'listPubPriority',
      listTopics: 'listTopics',
      listDefaultFollowList: 'listDefaultFollowList',
    }),
    initApiCalls() {
      if (this.defaultFollowList.length === 0) {
        this.listDefaultFollowList();
      }
      if (this.totalList.length === 0) {
        this.listTotalList();
      }
      if (this.prioritizedPubs.length === 0) {
        this.listPrioritisedPubs();
      }
      if (this.allPublisher.length === 0) {
        this.listPublishers();
      }
    },
    goToPage(page) {
      this.listPublishers({ page });
    },
    handleSort(sort) {
      this.$store.commit('SET_SORT', sort);
      this.listPublishers();
    },
    handlePageSize(limit) {
      this.listPublishers({ limit });
    },
    handleFilters(filters) {
      this.$store.commit('SET_FILTERS', filters);
      this.listPublishers();
    },
    handleCustomFilter(value, type) {
      const currentFilters = this.columnFilterValue;
      this[`${type}Filter`] = value;
      if (type === 'languages' || type === 'contentType') {
        const newArr = value.map((val) => {
          return val.value;
        });
        const newStr = newArr.join(',');
        value = newStr;
      }
      if (type === 'topics') {
        if (value.length > 0) {
          if (value.at(-1).value === '') {
            this.topicFilter = value = this.topicFilter.filter(
              (top) => top.value === ''
            );
          } else if (value.at(-1).value === '-1') {
            this.topicFilter = value = this.topicFilter.filter(
              (top) => top.value === '-1'
            );
          } else if (value.at(-1).value !== '' && value.at(-1).value !== '-1') {
            this.topicFilter = value = this.topicFilter.filter(
              (top) => top.value !== '' && top.value !== '-1'
            );
          }
        }
        const newArr = value.map((val) => {
          return val.value;
        });
        const newStr = newArr.join(',');
        value = newStr;
      }
      this.$store.commit('SET_FILTERS', { ...currentFilters, [type]: value });
      this.listPublishers();
    },
  },
};
</script>

<style scoped>
.form-group {
  margin: 0;
}

.multiselect__placeholder,
.multiselect__option,
.multiselect__input {
  font-size: 0.765625rem;
  font-weight: 400;
  color: #768192;
  margin-bottom: 0;
}
.multiselect,
.multiselect__tags {
  min-height: 28.38px;
}
.multiselect__tags,
.multiselect__select {
  min-height: 28.38px;
  height: 100%;
}
.multiselect__tags {
  padding: 0 40px 0 8px;
  border-color: #d8dbe0;
  border-radius: 0.2rem;
}
.multiselect__tag {
  font-size: 0.765625rem;
  font-weight: 400;
  margin-top: 2px;
  margin-bottom: 2px;
}
.multiselect__tags-wrap {
  display: flex;
}
.multiselect__tag-icon {
  line-height: 18px;
}
</style>
