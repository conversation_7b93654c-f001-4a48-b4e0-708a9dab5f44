<template>
  <div>
    <OverlayLoader v-if="loading" />
    <CInput
      :description="publisher.name ? publisher.name.length + ' Characters' : ''"
      label="Publisher Name"
      v-model="publisher.name"
      invalid-feedback="Publisher Name is required"
      :is-valid="publisher.name !== ''"
    />
    <CInput
      :description="
        publisher.description
          ? publisher.description.length + ' Characters'
          : ''
      "
      label="Publisher Description"
      v-model="publisher.description"
      invalid-feedback="Publisher Description is required"
      :is-valid="publisher.description !== ''"
    />
    <CInput
      v-if="publisher.hasOwnProperty('host')"
      v-model="publisher.host"
      label="Publisher Website / Page / Feed"
      invalid-feedback="Publisher Website / Page / Feed must start with http:// or https://"
      :is-valid="urlValidator"
    />
    <CRow>
      <CCol>
        <label for="adIconFile1">Logo</label>
      </CCol>
      <CCol>
        <label class="custom-file-upload">
          <input
            type="file"
            id="adIconFile1"
            @change="onFileChange($event)"
            ref="inputIcon"
            class="form-control-file"
            data-content="icon"
            accept=".png, .jpg, .jpeg"
          />
          <i class="fa fa-cloud-upload"></i> Upload
        </label>
      </CCol>
    </CRow>
    <div>
      <br />
      <CRow>
        <CCol> Verified </CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in yes_no_options"
            :key="(option.value % 1) + optionIndex + 'yesno'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="(option.value % 1) + optionIndex + 'yesno'"
              :value="option.value"
              v-model="publisher.verified"
            />
            <label
              class="form-check-label"
              :for="(option.value % 1) + optionIndex + 'yesno'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
      <br />
    </div>
    <div>
      <CRow>
        <CCol>Status</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in enabled_options"
            :key="(option.value % 1) + optionIndex + 'enable'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="(option.value % 1) + optionIndex + 'enable'"
              :value="option.value"
              v-model="publisher.enabled"
            />
            <label
              class="form-check-label"
              :for="(option.value % 1) + optionIndex + 'enable'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
      <br />
    </div>
    <CInput v-model="publisher.ga_id" label="GA Tracking ID" />
    <div>
      <br />
      <CRow>
        <CCol> Native </CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in yes_no_options"
            :key="(option.value % 1) + optionIndex + 'yesno_reader'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="(option.value % 1) + optionIndex + 'yesno_reader'"
              :value="option.value"
              v-model="publisher.isReaderMode"
            />
            <label
              class="form-check-label"
              :for="(option.value % 1) + optionIndex + 'yesno_reader'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
      <br />
    </div>
    <CSelect
      :options="allTopics"
      label="Topic"
      placeholder="Select Topic"
      :value.sync="publisher.topic"
    />
    <div>
      <CRow>
        <CCol> Auto Follow </CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in yes_no_options"
            :key="option.value + optionIndex + 'atf'"
          >
            <input
              class="form-check-input"
              type="radio"
              :value="option.value"
              :id="option.value + optionIndex + 'atf'"
              v-model="publisher.autoFollow"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'atf'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
      <br />
    </div>
  </div>
</template>

<script>
import slugify from 'slugify';
import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import OverlayLoader from '@/components/views/OverlayLoader';

export default {
  name: 'BasePublisherForm',
  components: { OverlayLoader },
  props: {
    publisher: {
      type: Object,
      required: true,
    },
    edit: {
      type: Boolean,
      required: true,
      default: false,
    },
  },
  data() {
    return {
      language_options: [
        { value: 'en', label: 'EN' },
        { value: 'ms', label: 'MS' },
        { value: 'zh', label: 'ZH' },
      ],
      yes_no_options: [
        { value: 1, label: 'Yes' },
        { value: 0, label: 'No' },
      ],
      enabled_options: [
        { value: 1, label: 'Enabled' },
        { value: 0, label: 'Disabled' },
      ],
      publisher_type: [
        { value: 'article', label: 'Articles' },
        { value: 'video', label: 'Videos' },
      ],
    };
  },
  computed: {
    loading() {
      return this.$store.getters.getLoading;
    },
    allTopics() {
      return this.$store.getters.getTopics;
    },
  },
  methods: {
    urlValidator(val) {
      return !!(val.includes('http://') || val.includes('https://'));
    },
    onFileChange(event) {
      let input = event.target;
      if (input.files && input.files[0]) {
        let iSize = input.files[0].size / 1000 / 1000;
        if (iSize <= 0.5) {
          this.getIconDimension(input);
        } else {
          alert('File size exceeded. Max 500kb');
          this.$refs.inputIcon.value = null;
        }
      }
    },
    getIconDimension(input) {
      let file = input.files[0];
      if (!file || file.type.indexOf('image/') !== 0) {
        alert('image not found');
      } else {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (evt) => {
          let img = new Image();
          img.onload = () => {
            let w = img.width;
            let h = img.height;
            let r = this.gcd(w, h);

            var error = 'Logo must be in 1:1 ratio';
            var a1 = w / r;
            var a2 = h / r;
            if (a1 == 1 && a2 == 1) {
              this.googleUpload(input);
            } else {
              alert(error);
              this.$refs.inputIcon.value = null;
            }
          };
          img.src = evt.target.result;
        };
        reader.onerror = (evt) => {
          console.error(evt);
        };
      }
    },
    gcd(a, b) {
      return b == 0 ? a : this.gcd(b, a % b);
    },
    googleUpload: function (input) {
      let fileName = slugify(input.files[0].name);
      let fileData = new FormData();
      fileData.append('path', 'boss-images/temp');
      fileData.append('file', input.files[0]);
      fileData.append('name', fileName);
      this.$store.commit('SET_GENERAL_LOADING', true);
      axios
        .post(process.env.VUE_APP_UPLOAD_FILE_TO_GCS, fileData)
        .then((res) => {
          let send = {
            data: res.data.url,
          };
          this.$emit('setImageLink', send);
          this.$store.commit('SET_GENERAL_LOADING', false);
        })
        .catch((error) => {
          this.$store.commit('SET_GENERAL_LOADING', false);
          alert('Something went wrong with uploading media');
          console.log('----Error----');
          console.log(error);
          Bugsnag.notify(error);
        });
    },
  },
};
</script>
<style scoped>
input[type='file'] {
  display: none;
}

.custom-file-upload {
  border: 1px solid #ccc;
  display: inline-block;
  padding: 6px 12px;
  cursor: pointer;
}
</style>

<style>
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  margin: 0 !important;
}

input[type='number'] {
  -moz-appearance: textfield !important; /* Firefox */
}
</style>
