<template>
  <div>
    <CRow>
      <CCol>
        <CButton
          variant="outline"
          color="primary"
          shape="pill"
          @click="$router.go(-1)"
          >Back</CButton
        >
      </CCol>
    </CRow>
    <br />
    <CCard>
      <CCardHeader>Edit Publisher</CCardHeader>
      <CCardBody>
        <CRow>
          <CCol sm="12" md="6" lg="6" xl="6">
            <BasePublisherForm
              :publisher="publisher"
              :publisherMonitoring="publisherMonitoring"
              :edit="true"
              @setImageLink="setDisplay"
            />
          </CCol>
          <CCol sm="12" md="6" lg="6" xl="6">
            <CCard>
              <CCardHeader>Publisher Logo</CCardHeader>
              <CCardBody>
                <div class="d-flex flex-row justify-content-center">
                  <img
                    v-if="logo_display"
                    width="150"
                    height="150"
                    style="border-radius: 50%; border: 1px solid #b2b2b2"
                    :src="logo_display"
                    alt=""
                  />
                  <div
                    v-else
                    style="
                      height: 150px;
                      width: 150px;
                      background-color: lightblue;
                      border-radius: 50%;
                    "
                  ></div>
                </div>
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
        <CButton block variant="outline" color="success" @click="alterPublisher"
          >Save</CButton
        >
      </CCardBody>
    </CCard>
    <OverlayLoader v-if="this.$store.getters.getLoading" />
  </div>
</template>

<script>
import BasePublisherForm from '@/components/Publishers/BasePublisherForm';
import OverlayLoader from '@/components/views/OverlayLoader';

export default {
  name: 'EditPublisher',
  components: { OverlayLoader, BasePublisherForm },
  data() {
    return {
      publisher: {},
      publisherMonitoring: {},
      fieldsMap: [
        { key: 'name', label: 'Publisher Name' },
        { key: 'description', label: 'Publisher Description' },
        { key: 'host', label: 'Publisher Website / Page / Feed' },
        { key: 'imageLink', label: 'Logo' },
        { key: 'topic', label: 'Topic' },
        { key: 'language', label: 'Language' },
        { key: 'website_url', label: 'Website URL' },
      ],
    };
  },
  computed: {
    logo_display: {
      get() {
        return this.publisher.logo_url;
      },
      set(val) {
        this.publisher.logo_url = val;
      },
    },
  },
  created() {
    this.$store.commit('SET_GENERAL_LOADING', true);
    this.$store
      .dispatch('listTopics')
      .then(() => {
        this.$store
          .dispatch('listPublisherById', parseInt(this.$route.params.pub_id))
          .then((res) => {
            this.publisher = res[0];
            this.$store.commit('SET_GENERAL_LOADING', false);
          })
          .catch(() => {
            alert('Something went wrong retrieving data');
            this.$store.commit('SET_GENERAL_LOADING', false);
          });
        this.$store
          .dispatch(
            'listPublisherMonitoringById',
            parseInt(this.$route.params.pub_id)
          )
          .then((res) => {
            this.publisherMonitoring = res;
            console.log('waooo', this.publisher);
            this.$store.commit('SET_GENERAL_LOADING', false);
          })
          .catch(() => {
            alert('Something went wrong retrieving data');
            this.$store.commit('SET_GENERAL_LOADING', false);
          });
      })
      .catch((err) => {
        console.log(err);
        this.$store.commit('SET_GENERAL_LOADING', false);
        alert('Something went wrong retrieving data');
      });
  },
  methods: {
    alterPublisher() {
      this.$swal
        .fire({
          icon: 'question',
          text: `This will update ${this.publisher.name} information. Continue?`,
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            let pass = JSON.parse(JSON.stringify(this.publisher));
            let sudo = {};
            let publisherMonitor = {
              id: this.publisherMonitoring.publisher_id,
              status: this.publisherMonitoring.status,
              frequency: this.publisherMonitoring.frequency,
            };
            Object.entries(pass).forEach(([prop, val]) => {
              sudo[prop] = val;
            });
            sudo.imageLink = sudo.logo_url;
            sudo.language = sudo.language.join(',');
            sudo.topics = sudo.topics.length
              ? sudo.topics.at(-1).value === '-1'
                ? []
                : sudo.topics.map((val) => {
                    return val.value;
                  })
              : [];
            if (this.validateValues(sudo)) {
              this.$store
                .dispatch('editPublisher', sudo)
                .then(() => {
                  // get monitor settings will return null if the publisher has no
                  // monitoring set up in the monitoring service yet, so no need to
                  // send an update request
                  if (publisherMonitor.frequency !== null) {
                    this.$store.dispatch(
                      'updatePublisherMonitoringById',
                      publisherMonitor
                    );
                  }
                })
                .then(() => {
                  this.$swal
                    .fire({
                      icon: 'success',
                      text: `Successfully updated ${pass.name} information!`,
                    })
                    .then(() => {
                      this.$router.go(-1);
                    });
                })
                .catch((err) => {
                  this.$swal.fire({
                    icon: 'error',
                    text: `Something went wrong in updating ${pass.name} information!`,
                  });
                  console.log(err);
                });
            }
          }
        });
    },
    validateValues(item) {
      let errors = [];
      let onlyCheck = [
        'language',
        'description',
        'imageLink',
        'isReaderMode',
        'enabled',
        'verified',
        'name',
        'website_url',
      ];
      Object.entries(item).forEach(([prop, val]) => {
        if (onlyCheck.includes(prop) && val === '') {
          errors.unshift(prop);
        }
      });
      if (errors.length > 0) {
        this.handleFailed(errors);
      }
      return errors.length === 0;
    },
    handleFailed(errors) {
      let html = "<ul style='text-align: left'>";
      errors.reverse().forEach((item) => {
        let dummy = this.fieldsMap.find((map) => map.key === item);
        html += `<li>${dummy.label}</li>`;
      });
      html += '</ul>';
      this.$swal.fire({
        icon: 'warning',
        title: 'Please fill in the compulsory fields',
        html: html,
        showConfirmButton: 'OK',
      });
    },
    setDisplay(item) {
      this.publisher.logo_url = item.data;
      this.logo_display = item.data;
    },
  },
};
</script>

<style scoped></style>
