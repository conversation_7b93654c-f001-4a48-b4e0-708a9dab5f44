<template>
  <div>
    <div v-show="checkAction('auto_follow_add')">
      <CButton variant="outline" color="info" shape="pill" @click="show = !show"
        >Auto Follow-Topic</CButton
      >
    </div>
    <CModal
      :close-on-backdrop="false"
      centered
      size="xl"
      title="Auto Follow List"
      :show.sync="show"
      @update:show="follow_items = []"
    >
      <CRow>
        <CCol>
          <CRow>
            <CCol lg="10">
              <label for="selectItems">Add to Default Follow List</label>
              <multiselect
                id="selectItems"
                v-model="follow_items"
                :options="total_list"
                :multiple="true"
                track-by="id"
                label="name"
              >
                <span slot="noResult"
                  >Oops! No items found. Consider changing the search
                  query.</span
                >
              </multiselect>
            </CCol>
            <CCol lg="2">
              <CButton
                color="success"
                variant="outline"
                :disabled="follow_items.length === 0"
                style="margin-top: 30px; height: 40px"
                @click="addToFollow"
                >Add
              </CButton>
            </CCol>
          </CRow>
        </CCol>
      </CRow>
      <br />
      <CRow>
        <CCol>
          <CDataTable
            :key="tableKey"
            :sorter-value="{ column: 'name', asc: true }"
            :fields="follow_fields"
            :loading="loading"
            :items="auto_follow_list"
            pagination
            :items-per-page="10"
            items-per-page-select
            sorter
            table-filter
          >
            <template #action="{ item }">
              <td>
                <CButtonToolbar>
                  <div v-show="checkAction('auto_follow_delete')">
                    <CButton
                      variant="outline"
                      color="danger"
                      v-c-tooltip="'Remove from Auto Follow'"
                      @click="removeFromFollow(item)"
                    >
                      <CIcon name="cil-trash"></CIcon>
                    </CButton>
                  </div>
                </CButtonToolbar>
              </td>
            </template>
          </CDataTable>
        </CCol>
      </CRow>
      <template slot="footer">
        <CButton color="secondary" variant="outline" @click="show = !show"
          >Close</CButton
        >
      </template>
    </CModal>
  </div>
</template>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<script>
import { mapGetters, mapActions } from 'vuex';
import Multiselect from 'vue-multiselect';

export default {
  name: 'FollowList',
  components: { Multiselect },
  data() {
    return {
      show: false,
      follow_items: [],
      follow_fields: [
        { key: 'name', label: 'Name' },
        { key: 'action', label: 'Action', sorter: false, filter: false },
      ],
    };
  },
  computed: {
    ...mapGetters({
      total_list: 'getTotalList',
      auto_follow_list: 'getDefaultFollowList',
      loading: 'getDefaultLoading',
    }),
    tableKey() {
      if (this.auto_follow_list.length > 0) {
        const idx = Math.floor(Math.random() * this.auto_follow_list.length);
        return this.auto_follow_list[idx].id;
      }
      return Math.random();
    },
  },
  methods: {
    ...mapActions({
      addPubToFollow: 'addToFollowList',
      listDefaultFollow: 'listDefaultFollowList',
      listTotal: 'listTotalList',
      updatePubFromFollow: 'updateFollowList',
    }),
    addToFollow() {
      this.$swal
        .fire({
          icon: 'question',
          text: 'This will add selections to default follow list. Continue?',
        })
        .then((res) => {
          if (res.isConfirmed) {
            let pass = this.follow_items;
            let postObj = { topics: [] };
            pass.forEach((item) => {
              postObj.topics.unshift(item.id);
            });
            this.addPubToFollow(postObj)
              .then(() => {
                this.$swal
                  .fire({
                    icon: 'success',
                    text: `Successfully added selections default follow list.`,
                    showCancelButton: false,
                  })
                  .then(() => {
                    this.follow_items = [];
                    this.listDefaultFollow();
                    this.listTotal();
                  });
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'warning',
                  text: `Something went wrong in adding selections from default follow list.`,
                  showCancelButton: false,
                });
              });
          }
        });
    },
    removeFromFollow(item) {
      this.$swal
        .fire({
          icon: 'question',
          text: `This will remove ${item.name} from default follow list. Continue?`,
        })
        .then((res) => {
          if (res.isConfirmed) {
            this.updatePubFromFollow(item.id)
              .then(() => {
                this.$swal.fire({
                  icon: 'success',
                  text: `Successfully removed ${item.name} from default follow list.`,
                  showCancelButton: false,
                });
                this.listDefaultFollow();
                this.listTotal();
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'warning',
                  text: `Something went wrong in removing ${item.name} from default follow list.`,
                  showCancelButton: false,
                });
              });
          }
        });
    },
  },
};
</script>

<style scoped></style>
