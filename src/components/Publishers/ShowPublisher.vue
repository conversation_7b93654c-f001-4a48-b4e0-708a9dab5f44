<template>
  <div>
    <CRow>
      <CCol>
        <CButton @click="goBack" color="primary" shape="pill" variant="outline"
          >Back</CButton
        >
        <CButtonToolbar class="float-right">
          <a
            :href="'https://newswav.com/publisher/' + publisher.id"
            target="_blank"
          >
            <CButton shape="pill" color="primary">View Publisher Feed</CButton>
          </a>
        </CButtonToolbar>
      </CCol>
    </CRow>
    <br />
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader> {{ publisher.name }} Details </CCardHeader>
          <CCardBody>
            <CRow>
              <CCol>
                <CButtonToolbar class="float-right">
                  <CButton
                    variant="outline"
                    shape="pill"
                    color="info"
                    @click="show_details = !show_details"
                    >Show Details</CButton
                  >
                  <CButton
                    variant="outline"
                    shape="pill"
                    color="warning"
                    @click="$router.push('edit-pub/' + publisher.id)"
                  >
                    Edit Publisher
                  </CButton>
                </CButtonToolbar>
              </CCol>
            </CRow>
            <br />
            <CRow>
              <CCol sm="12" md="4" lg="4" xl="4">
                <div
                  class="d-flex flex-row align-items-center"
                  style="margin-bottom: 10px"
                >
                  <div style="margin-right: 10px">
                    <img
                      v-if="logo_display"
                      width="100"
                      height="100"
                      style="border-radius: 50%; border: 1px solid #b2b2b2"
                      :src="logo_display"
                      alt=""
                    />
                    <div
                      v-else
                      style="
                        height: 100px;
                        width: 100px;
                        background-color: lightblue;
                        border-radius: 50%;
                      "
                    ></div>
                  </div>
                  <div>
                    <div class="d-flex flex-column flex-wrap">
                      <div style="font-size: 22px">
                        <div class="d-flex flex-row align-items-center">
                          <div class="mr-2">
                            <strong>{{
                              publisher.name ? publisher.name : 'Loading..'
                            }}</strong>
                          </div>
                          <div>
                            <svg
                              v-if="publisher.verified"
                              id="Verified"
                              xmlns="http://www.w3.org/2000/svg"
                              width="22"
                              height="22"
                              viewBox="0 0 16 16"
                            >
                              <g
                                id="icon_partner_publisher_mark"
                                data-name="icon/ partner publisher mark"
                              >
                                <g id="Group_7" data-name="Group 7">
                                  <g
                                    id="Star"
                                    fill="#ec3434"
                                    stroke-miterlimit="10"
                                  >
                                    <path
                                      d="M 10.262375831604 14.96286869049072 L 8.207810401916504 14.02402973175049 L 8 13.92907047271729 L 7.792190074920654 14.02402973175049 L 5.737624168395996 14.96286869049072 L 4.627289772033691 12.99569034576416 L 4.514979839324951 12.79671955108643 L 4.291049957275391 12.75139999389648 L 2.077026844024658 12.30329322814941 L 2.335030078887939 10.05916976928711 L 2.361119985580444 9.832180023193359 L 2.206589937210083 9.66388988494873 L 0.6787991523742676 8 L 2.206589937210083 6.33611011505127 L 2.361119985580444 6.167819976806641 L 2.335030078887939 5.940830230712891 L 2.077026605606079 3.696706771850586 L 4.291039943695068 3.248610019683838 L 4.514979839324951 3.203289985656738 L 4.627289772033691 3.004319906234741 L 5.737624168395996 1.03713321685791 L 7.792190074920654 1.975980043411255 L 8 2.070940017700195 L 8.207810401916504 1.975980043411255 L 10.262375831604 1.03713321685791 L 11.37271022796631 3.004319906234741 L 11.48501968383789 3.203289985656738 L 11.70895957946777 3.248610019683838 L 13.9229736328125 3.696706771850586 L 13.66497039794922 5.940830230712891 L 13.63887977600098 6.167819976806641 L 13.7934103012085 6.33611011505127 L 15.32120132446289 8 L 13.7934103012085 9.66388988494873 L 13.63887977600098 9.832180023193359 L 13.66497039794922 10.05916976928711 L 13.9229736328125 12.30329322814941 L 11.70895004272461 12.75139999389648 L 11.48501968383789 12.79671955108643 L 11.37271022796631 12.99569034576416 L 10.262375831604 14.96286869049072 Z"
                                      stroke="none"
                                    />
                                    <path
                                      d="M 10.05261135101318 14.31728649139404 L 11.16189002990723 12.35198020935059 L 13.37380599975586 11.90429782867432 L 13.11604976654053 9.662309646606445 L 14.6423921585083 8 L 13.11604976654053 6.337689876556396 L 13.37380504608154 4.095705032348633 L 11.16189002990723 3.648030042648315 L 10.05261135101318 1.682716488838196 L 8 2.620670080184937 L 5.947388648986816 1.682716488838196 L 4.838109970092773 3.648030042648315 L 2.626194715499878 4.095705032348633 L 2.883949995040894 6.337689876556396 L 1.357608318328857 8 L 2.883949995040894 9.662309646606445 L 2.626194477081299 11.90429782867432 L 4.838109970092773 12.35198020935059 L 5.947388648986816 14.31728649139404 L 8 13.37934017181396 L 10.05261135101318 14.31728649139404 M 10.47214031219482 15.60844993591309 L 8 14.47879981994629 L 5.527860164642334 15.60844993591309 L 4.191860198974609 13.24145984649658 L 1.527860045433044 12.70228004455566 L 1.838299989700317 10.00205993652344 L 0 8 L 1.838299989700317 5.997940063476562 L 1.527860045433044 3.297719955444336 L 4.191860198974609 2.758549928665161 L 5.527860164642334 0.3915500044822693 L 8 1.521209955215454 L 10.47214031219482 0.3915500044822693 L 11.80813980102539 2.758549928665161 L 14.47214031219482 3.297719955444336 L 14.16170024871826 5.997940063476562 L 16 8 L 14.16170024871826 10.00205993652344 L 14.47214031219482 12.70228004455566 L 11.80813980102539 13.24145984649658 L 10.47214031219482 15.60844993591309 Z"
                                      stroke="none"
                                      fill="#ec3434"
                                    />
                                  </g>
                                  <path
                                    id="Path_13"
                                    data-name="Path 13"
                                    d="M0,2.211,1.572,4,6,0"
                                    transform="translate(5 6)"
                                    fill="none"
                                    stroke="#f8f8f6"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-miterlimit="10"
                                    stroke-width="2"
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                        </div>
                      </div>
                      <div
                        style="
                          font-size: 18px;
                          -webkit-line-clamp: 1; /* number of lines to show */
                          max-width: 350px;
                        "
                      >
                        {{
                          publisher.description
                            ? publisher.description
                            : 'Loading...'
                        }}
                      </div>
                      <div>
                        <strong>{{ followers }}</strong> Followers
                      </div>
                    </div>
                  </div>
                </div>
              </CCol>
              <CCol sm="12" md="8" lg="8" xl="8">
                <CRow>
                  <CCol sm="12" md="4" lg="4" xl="4">
                    <CWidgetIcon
                      text="Status"
                      :header="
                        publisher.isEnabled ? publisher.isEnabled : 'Loading..'
                      "
                      :color="getStatusColor(publisher.enabled)"
                      :icon-padding="false"
                    >
                      <CIcon name="cil-power-standby" width="24"></CIcon>
                    </CWidgetIcon>
                  </CCol>
                  <CCol sm="12" md="4" lg="4" xl="4">
                    <CWidgetIcon
                      text="Type"
                      :header="
                        publisher.project_show
                          ? publisher.project_show
                          : 'Loading..'
                      "
                      color="info"
                      :icon-padding="false"
                    >
                      <CIcon name="cil-description" width="24"></CIcon>
                    </CWidgetIcon>
                  </CCol>
                  <CCol sm="12" md="4" lg="4" xl="4">
                    <CWidgetIcon
                      text="Ad Share Rev"
                      :header="publisher.ad_share ? percentage : 'Loading..'"
                      color="warning"
                      :icon-padding="false"
                    >
                      <CIcon name="cil-money" width="24"></CIcon>
                    </CWidgetIcon>
                  </CCol>
                </CRow>
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CCollapse :show.sync="show_details">
                  <PublisherDetails
                    :publisher="publisher"
                    :logo_display="logo_display"
                  />
                </CCollapse>
              </CCol>
            </CRow>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader>Publisher Content Channels</CCardHeader>
          <CCardBody>
            <CRow>
              <CCol>
                <CButtonToolbar class="float-right">
                  <AddChannelToPublisher :publisher="publisher" />
                </CButtonToolbar>
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CDataTable
                  :loading="loading"
                  :fields="channel_fields"
                  :items="channels"
                  table-filter
                  :items-per-page="5"
                  pagination
                  sorter
                >
                  <template #action="{ item }">
                    <td>
                      <EditChannelModal :channel="item" :edit="true" />
                    </td>
                  </template>
                </CDataTable>
              </CCol>
            </CRow>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
    <OverlayLoader v-if="loading" />
  </div>
</template>

<script>
import { mapGetters, mapActions, mapMutations } from 'vuex';
import OverlayLoader from '@/components/views/OverlayLoader';
import PublisherDetails from '@/components/Publishers/PublisherDetails';
import AddChannelToPublisher from '@/components/Publishers/AddChannelToPublisher';
import numeral from 'numeral';
import EditChannelModal from '@/components/Publishers/EditChannelModal';

export default {
  name: 'ShowPublisher',
  components: {
    EditChannelModal,
    AddChannelToPublisher,
    PublisherDetails,
    OverlayLoader,
  },
  data() {
    return {
      channel_fields: [
        // {key : 'check', label : '', sorter:false, filter:false},
        { key: 'id', label: 'Channel ID' },
        { key: 'contentType', label: 'Content Type' },
        { key: 'isEnabled', label: 'Status' },
        { key: 'readerShow', label: 'Native' },
        { key: 'contentLang', label: 'Language' },
        { key: 'crawler_frequency', label: 'Crawler Frequency' },
        { key: 'action', label: 'Action', sorter: false, filter: false },
      ],
      checkState: [],
      show_details: false,
    };
  },
  computed: {
    ...mapGetters({
      loading: 'getPublisherLoading',
      publisher: 'getCurrentPublisher',
      channels: 'getCurrentChannels',
      allTopics: 'getTopics',
    }),
    percentage() {
      return `${numeral(this.publisher.ad_share).format('0.0')}%`;
    },
    logo_display: {
      get() {
        return this.publisher.logo_url;
      },
      set(val) {
        this.publisher.logo_url = val;
      },
    },
    followers() {
      if (this.publisher.real_followers > 1000) {
        return numeral(this.publisher.real_followers).format('0.0a');
      } else {
        return this.publisher.real_followers;
      }
    },
  },
  async created() {
    if (this.allTopics.length === 0) {
      this.listTopics().then(() => {
        this.publisherById(parseInt(this.$route.params.pub_id));
      });
    } else {
      this.publisherById(parseInt(this.$route.params.pub_id));
    }
  },
  methods: {
    ...mapActions({
      publisherById: 'listPublisherById',
      listTopics: 'listTopics',
    }),
    ...mapMutations({
      resetChannels: 'SET_CURRENT_CHANNELS',
      resetCurrentPublisher: 'SET_CURRENT_PUBLISHER',
    }),
    getStatusIcon(status) {
      return status === 'open' || status === 'active'
        ? 'cil-media-play'
        : status === 'scheduled'
          ? 'cil-av-timer'
          : 'cil-book';
    },
    getStatusColor(status) {
      return status === 1 ? 'success' : 'danger';
    },
    goBack() {
      this.resetCurrentPublisher({});
      this.resetChannels([]);
      this.$router.push('/publishers');
    },
  },
};
</script>

<style scoped></style>
