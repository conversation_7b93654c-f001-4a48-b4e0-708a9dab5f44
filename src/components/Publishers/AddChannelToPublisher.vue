<template>
  <div>
    <CButton
      variant="outline"
      shape="pill"
      color="success"
      @click="show = !show"
      >Add Channel</CButton
    >
    <CForm @submit.prevent="addChannel">
      <CModal
        :show.sync="show"
        :title="'Add Content Channel to ' + publisher.name"
        centered
        :close-on-backdrop="false"
      >
        <BaseChannelForm :channel="channel" />
        <template slot="footer">
          <CButton
            variant="outline"
            color="success"
            :disabled="disabled"
            @click="addChannel"
            >Submit</CButton
          >
        </template>
      </CModal>
    </CForm>
  </div>
</template>

<script>
import BaseChannelForm from '@/components/Publishers/BaseChannelForm';

export default {
  name: 'AddChannelToPublisher',
  components: { BaseChannelForm },
  props: ['publisher'],
  data() {
    return {
      show: false,
      channel: {
        content_type: '',
        host: '',
        ch_language: '',
        isReaderMode: 0,
        publisher_id: this.$route.params.pub_id,
      },
    };
  },
  computed: {
    disabled() {
      let errors = [];
      let check = ['type', 'host', 'language'];
      Object.entries(this.channel).forEach(([prop, val]) => {
        if (check.includes(prop)) {
          if (val.length === 0 || val === '') {
            errors.unshift(prop);
          }
        }
      });
      return errors.length > 0;
    },
  },
  methods: {
    addChannel() {
      this.$swal
        .fire({
          icon: 'question',
          text:
            'This will add content channels to ' +
            this.publisher.name +
            '. Continue?',
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            let pass = JSON.parse(JSON.stringify(this.channel));
            let send = {};
            Object.entries(pass).forEach(([prop, val]) => {
              send[prop] = val;
            });
            this.$store.commit('SET_PUBLISHER_LOADING', true);
            this.$store
              .dispatch('createPublisherChannel', send)
              .then(() => {
                this.$store.commit('SET_PUBLISHER_LOADING', false);
                this.resetChannels();
                this.$swal
                  .fire({
                    icon: 'success',
                    text: 'Channel successfully added!',
                    showCancelButton: false,
                  })
                  .then(() => {
                    this.show = !this.show;
                    this.$store.dispatch(
                      'listPublisherById',
                      parseInt(this.$route.params.pub_id)
                    );
                  });
              })
              .catch((err) => {
                let text =
                  "Something went wrong in creating publisher's channel";
                if (err.response.data.msg) {
                  text = err.response.data.msg;
                }
                this.$swal.fire({
                  icon: 'error',
                  text: text,
                  showCancelButton: false,
                });
                this.$store.commit('SET_PUBLISHER_LOADING', false);
              });
          }
        });
    },
    resetChannels() {
      this.channel = {
        content_type: '',
        host: '',
        ch_language: '',
        isReaderMode: 0,
        publisher_id: this.$route.params.pub_id,
      };
    },
  },
};
</script>

<style scoped></style>
