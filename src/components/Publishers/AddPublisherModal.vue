<template>
  <div>
    <div v-show="checkAction('publisher_add')">
      <CButton
        variant="outline"
        shape="pill"
        color="success"
        @click="show = !show"
        class="float-right mb-3"
        >Add Publisher
      </CButton>
    </div>
    <CForm @submit.prevent="createPublisher">
      <CModal
        :show.sync="show"
        title="Add Publisher"
        :close-on-backdrop="false"
        centered
        size="lg"
        @update:show="onClose"
      >
        <CRow>
          <CCol sm="12" md="6" lg="6" xl="6">
            <BasePublisherForm
              :publisher="publisher"
              @setImageLink="setImage"
              :edit="false"
            />
          </CCol>
          <CCol sm="12" md="6" lg="6" xl="6">
            <CCard>
              <CCardHeader>Publisher Logo</CCardHeader>
              <CCardBody>
                <div class="d-flex flex-row justify-content-center">
                  <img
                    v-if="logo_display"
                    :src="logo_display"
                    alt=""
                    class="logo_place"
                  />
                  <div v-else class="placeholder_img"></div>
                </div>
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
        <template slot="footer">
          <CButton
            type="submit"
            color="success"
            variant="outline"
            :disabled="is_disabled"
            >Save</CButton
          >
        </template>
      </CModal>
    </CForm>
  </div>
</template>

<script>
import BasePublisherForm from '@/components/Publishers/BasePublisherForm';

export default {
  name: 'AddPublisherModal',
  components: { BasePublisherForm },
  data() {
    return {
      show: false,
      publisher: {
        name: '',
        project_type: '',
        content_type: [],
        description: '',
        host: '',
        imageLink: '',
        language: [],
        isReaderMode: 0,
        enabled: 0,
        verified: 0,
        ga_id: '',
        topic: '',
        autoFollow: 0,
      },
      fieldsMap: [
        { key: 'name', label: 'Publisher Name' },
        { key: 'description', label: 'Publisher Description' },
        { key: 'host', label: 'Publisher Website / Page / Feed' },
        { key: 'imageLink', label: 'Logo' },
        { key: 'topic', label: 'Topic' },
        { key: 'language', label: 'Language' },
      ],
      logo_display: '',
    };
  },
  computed: {
    is_disabled() {
      let errors = [];
      let custom = ['language', 'ga_id', 'host', 'topic'];
      Object.entries(this.publisher).forEach(([prop, val]) => {
        if (!custom.includes(prop) && val === '') {
          errors.unshift(prop);
        }
        if (prop === 'host' && !val.includes('http')) {
          errors.unshift('host');
        }
        if (prop === 'language' && val.length === 0) {
          errors.unshift('language');
        }
      });
      return errors.length > 0;
    },
  },
  methods: {
    setImage(item) {
      this.publisher.imageLink = item.data;
      this.logo_display = item.data;
    },
    createPublisher() {
      let suedo = {};
      Object.entries(this.publisher).forEach(([prop, val]) => {
        suedo[prop] = val;
      });
      this.$swal
        .fire({
          icon: 'question',
          text: `This will make ${this.publisher.name} as a publisher in Newswav. Continue?`,
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            suedo.language = suedo.language.join(',');
            if (this.validateValues(suedo)) {
              this.$store
                .dispatch('createNewPublisher', suedo)
                .then((res) => {
                  this.show = !this.show;
                  this.$router.push('/publishers/' + res.data[0].id);
                })
                .catch(() => {});
            }
          }
        });
    },
    validateValues(item) {
      let errors = [];
      let custom = ['ga_id', 'host', 'topic'];
      Object.entries(item).forEach(([prop, val]) => {
        if (!custom.includes(prop) && val === '') {
          errors.unshift(prop);
        }
        if (prop === 'host' && !val.includes('http')) {
          errors.unshift('host');
        }
      });
      if (errors.length > 0) {
        this.handleFailed(errors);
      }
      return errors.length === 0;
    },
    handleFailed(errors) {
      let html = "<ul style='text-align: left'>";
      errors.reverse().forEach((item) => {
        let dummy = this.fieldsMap.find((map) => map.key === item);
        html += `<li>${dummy.label}</li>`;
      });
      html += '</ul>';
      this.$swal.fire({
        icon: 'warning',
        title: 'Please fill in the compulsory fields',
        html: html,
        showConfirmButton: 'OK',
      });
    },
    onClose() {
      this.publisher = {
        name: '',
        description: '',
        host: '',
        imageLink: '',
        language: [],
        isReaderMode: 0,
        enabled: 0,
        verified: 0,
        ga_id: '',
        topic: '',
        autoFollow: 0,
      };
      this.logo_display = '';
    },
  },
};
</script>

<style scoped>
.placeholder_img {
  height: 150px;
  width: 150px;
  background-color: lightblue;
  border-radius: 50%;
  border: 1px solid #b2b2b2;
}

.logo_place {
  border-radius: 50%;
  border: 1px solid #b2b2b2;
  width: 150px;
  height: 150px;
}
</style>
