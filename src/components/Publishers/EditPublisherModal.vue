<template>
  <div>
    <CButton
      v-if="!from_show"
      color="warning"
      variant="outline"
      @click="show = !show"
    >
      <CIcon name="cil-pencil"></CIcon>
    </CButton>
    <CButton
      v-else
      color="warning"
      shape="pill"
      variant="outline"
      @click="show = !show"
      >Edit Publisher</CButton
    >
    <CForm @submit.prevent="updatePublisher">
      <CModal
        :show.sync="show"
        title="Edit Publisher"
        :close-on-backdrop="false"
        centered
        size="xl"
      >
        <CRow>
          <CCol sm="12" md="6" lg="6" xl="6">
            <BasePublisherForm
              :publisher="newPublisher"
              :edit="true"
              @setImageLink="passDisplay"
              @update:show="onClose"
            />
          </CCol>
          <CCol sm="12" md="6" lg="6" xl="6">
            <CCard>
              <CCardHeader>Publisher Logo</CCardHeader>
              <CCardBody>
                <div class="d-flex flex-row justify-content-center">
                  <img
                    v-if="logo_display"
                    width="150"
                    height="150"
                    style="border-radius: 50%; border: 1px solid #b2b2b2"
                    :src="logo_display"
                    alt=""
                  />
                  <div
                    v-else
                    style="
                      height: 150px;
                      width: 150px;
                      background-color: lightblue;
                      border-radius: 50%;
                    "
                  ></div>
                </div>
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
        <template slot="footer">
          <CButton type="submit" color="success" variant="outline"
            >Save</CButton
          >
        </template>
      </CModal>
    </CForm>
  </div>
</template>

<script>
import BasePublisherForm from '@/components/Publishers/BasePublisherForm';

export default {
  name: 'EditPublisherModal',
  components: { BasePublisherForm },
  props: {
    publisher: {
      type: Object,
      required: true,
    },
    from_show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      show: false,
      newPublisher: JSON.parse(JSON.stringify(this.publisher)),
      fieldsMap: [
        { key: 'name', label: 'Publisher Name' },
        { key: 'description', label: 'Publisher Description' },
        { key: 'host', label: 'Publisher Website / Page / Feed' },
        { key: 'imageLink', label: 'Logo' },
        { key: 'topic', label: 'Topic' },
        { key: 'language', label: 'Language' },
        { key: 'website_url', label: 'Website URL' },
      ],
    };
  },
  watch: {
    publisher() {
      this.newPublisher = JSON.parse(JSON.stringify(this.publisher));
    },
  },
  computed: {
    logo_display: {
      get() {
        return this.newPublisher.logo_url;
      },
      set(val) {
        this.newPublisher.logo_url = val;
      },
    },
  },
  methods: {
    updatePublisher() {
      this.$swal
        .fire({
          icon: 'question',
          text: `This will update ${this.newPublisher.name} information. Continue?`,
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            let sudo = {};
            Object.entries(this.newPublisher).forEach(([prop, val]) => {
              sudo[prop] = val;
            });
            sudo.imageLink = sudo.logo_url;
            sudo.language = sudo.language.join(',');
            if (this.validateValues(sudo)) {
              this.$store
                .dispatch('editPublisher', sudo)
                .then(() => {
                  this.$swal.fire({
                    icon: 'success',
                    text: `Successfully updated ${sudo.name} information!`,
                    showCancelButton: false,
                  });
                  this.$store.dispatch(
                    'listPublisherById',
                    parseInt(this.$route.params.pub_id)
                  );
                  this.show = !this.show;
                })
                .catch(() => {});
            }
          }
        });
    },
    validateValues(item) {
      let errors = [];
      let onlyCheck = [
        'language',
        'description',
        'imageLink',
        'isReaderMode',
        'enabled',
        'verified',
        'topic',
        'name',
        'website_url',
      ];
      Object.entries(item).forEach(([prop, val]) => {
        if (onlyCheck.includes(prop) && val === '') {
          errors.unshift(prop);
        }
      });
      if (errors.length > 0) {
        this.handleFailed(errors);
      }
      return errors.length === 0;
    },
    handleFailed(errors) {
      let html = "<ul style='text-align: left'>";
      errors.reverse().forEach((item) => {
        let dummy = this.fieldsMap.find((map) => map.key === item);
        html += `<li>${dummy.label}</li>`;
      });
      html += '</ul>';
      this.$swal.fire({
        icon: 'warning',
        title: 'Please fill in the compulsory fields',
        html: html,
        showConfirmButton: 'OK',
      });
    },
    onClose() {
      this.newPublisher = JSON.parse(JSON.stringify(this.publisher));
      this.logo_display = JSON.parse(
        JSON.stringify(this.newPublisher.logo_url)
      );
    },
    passDisplay(item) {
      this.newPublisher.logo_url = item.data;
      this.logo_display = item.data;
    },
  },
};
</script>

<style scoped></style>
