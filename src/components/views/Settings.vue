<template>
  <div>
    <CCard>
      <CCardHeader> Dashboard Settings </CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>Dry Mode</CCol>
          <CCol>
            <CSwitch
              shape="pill"
              color="warning"
              :checked.sync="dryMode"
            ></CSwitch>
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
export default {
  name: 'Settings',
  data() {
    return {
      dryTest: false,
    };
  },
  computed: {
    dryMode: {
      get() {
        return this.$store.state.user.dryMode;
      },
      set(val) {
        this.$store.commit('SET_DRY_MODE', val);
      },
    },
  },
};
</script>

<style scoped></style>
