<template>
  <div>
    <CCard>
      <CCardHeader>
        <CButton
          v-show="checkAction('clear_cache_group')"
          class="float-right mb-2"
          variant="outline"
          color="success"
          shape="pill"
          @click="theButton()"
          >Clear Cache & Update Image</CButton
        >
      </CCardHeader>
      <CCardBody v-if="hasElection">
        <CRow style="margin-bottom: 10px">
          <CCol sm="3">
            <multiselect
              v-model="type"
              deselect-label="Can't remove this value"
              track-by="value"
              label="name"
              :show-labels="false"
              :disabled="loading"
              placeholder="Select one"
              :options="area_type"
              :searchable="false"
              :allow-empty="false"
              @select="updateType"
            >
              <template slot="singleLabel" slot-scope="{ option }"
                ><strong>{{ option.name }}</strong></template
              >
            </multiselect>
          </CCol>
          <CCol sm="9">
            <multiselect
              v-model="selected_state"
              deselect-label="Can't remove this value"
              track-by="name"
              label="name"
              :disabled="loading"
              placeholder="Select one"
              :options="electionState.states"
              :searchable="true"
              :allow-empty="false"
              @select="updateState"
            >
              <template slot="singleLabel" slot-scope="{ option }"
                ><strong>{{ option.name }}</strong></template
              >
            </multiselect>
          </CCol>
        </CRow>
        <CDataTable
          column-filter
          pagination
          small
          :fields="area_fields"
          :items-per-page="15"
          :items="electionData"
          :loading="loading"
        >
          <template #code="{ item }">
            <td>
              {{ item.code }}
            </td>
          </template>
          <template #name="{ item }">
            <td>
              {{ item.name }}
            </td>
          </template>
          <template #candidate_id="{ item }">
            <td v-if="!item.edit">
              <div v-if="item.candidate">
                <img
                  v-if="item.candidate.image"
                  class="option__image"
                  :src="item.candidate.image"
                  alt=""
                />
                <div class="option__desc">
                  <span class="option__title"
                    ><b>{{
                      item.candidate.coalition
                        ? `[${item.candidate.coalition}] `
                        : ''
                    }}</b
                    >{{ item.candidate.abbreviation }} - </span
                  ><span class="option__small">{{ item.candidate.name }}</span>
                </div>
              </div>
              <div v-else>-</div>
            </td>
            <td v-else>
              <multiselect
                v-if="!item.loading"
                v-model="item.candidate"
                deselect-label=""
                :show-labels="false"
                track-by="name"
                placeholder="Select one"
                :options="item.candidate_list"
                :searchable="true"
                :allow-empty="true"
              >
                <!-- <template slot="singleLabel" slot-scope="{ option }"><strong>{{ option.name }}</strong></template> -->
                <template slot="singleLabel" slot-scope="{ option }"
                  ><img
                    v-if="option.image"
                    class="option__image"
                    :src="option.image"
                    alt=""
                  /><span class="option__desc"
                    ><span class="option__title"
                      ><b>{{
                        item.candidate.coalition
                          ? `[${item.candidate.coalition}] `
                          : ''
                      }}</b
                      >{{ option.abbreviation }}</span
                    ></span
                  ></template
                >
                <template slot="option" slot-scope="{ option }"
                  ><img
                    v-if="option.image"
                    class="option__image"
                    :src="option.image"
                    alt=""
                  />
                  <div class="option__desc">
                    <span class="option__title"
                      ><b>{{
                        option.coalition ? `[${option.coalition}] ` : ''
                      }}</b
                      >{{ option.abbreviation }} - </span
                    ><span class="option__small">{{ option.name }}</span>
                  </div>
                </template>
              </multiselect>
              <div
                v-else
                style="display: flex; justify-content: center; padding-top: 5px"
              >
                <CSpinner color="success" size="sm" />
              </div>
            </td>
          </template>
          <template #official="{ item }">
            <td v-if="!item.edit">
              <CBadge :color="item.official ? 'success' : 'danger'">{{
                item.official ? 'Yes' : 'No'
              }}</CBadge>
            </td>
            <td v-else>
              <CSwitch
                v-if="!item.loading"
                color="success"
                :checked.sync="item.official"
              />
              <div
                v-else
                style="display: flex; justify-content: center; padding-top: 5px"
              >
                <CSpinner color="success" size="sm" />
              </div>
            </td>
          </template>
          <template #action="{ item }">
            <td>
              <div class="d-flex flex-row">
                <CButton
                  v-if="item.edit"
                  color="warning"
                  class="m-2"
                  @click="cancelEdit(item)"
                  size="sm"
                >
                  Cancel
                </CButton>
                <CButton
                  v-if="item.edit"
                  color="primary"
                  class="m-2"
                  @click="confirmEdit(item)"
                  :disabled="item.loading"
                  size="sm"
                >
                  Confirm
                </CButton>
                <CButton
                  v-if="!item.edit"
                  color="primary"
                  class="m-2"
                  @click="edit(item)"
                  size="sm"
                  :disabled="!item.canEdit"
                >
                  Edit
                </CButton>
              </div>
            </td>
          </template>
        </CDataTable>
      </CCardBody>
      <CCardBody v-if="!loading && electionData.length == 0">
        <CJumbotron>
          <h1 class="display-3">No Active Election.</h1>
          <p class="lead">Click away</p>
        </CJumbotron>
      </CCardBody>
    </CCard>
  </div>
</template>

<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<script>
import Multiselect from 'vue-multiselect';
import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import * as headers from '@/helpers/headers';
export default {
  name: 'Election',
  components: { Multiselect },
  data() {
    return {
      area_fields: [
        { key: 'code', label: 'Code', _style: 'width :10%' },
        { key: 'name', label: 'Name', _style: 'width :20%' },
        {
          key: 'candidate_id',
          label: 'Winner',
          filter: false,
          _style: 'width :30%',
        },
        {
          key: 'official',
          label: 'Official',
          filter: false,
          _style: 'width :15%',
        },
        { key: 'action', label: 'Action', filter: false, _style: 'width :15%' },
      ],
      area_type: [
        { name: 'Parliament', value: 'parliament' },
        { name: 'State', value: 'state' },
      ],
      type: { name: 'Parliament', value: 'parliament' },
      selected_state: null,
    };
  },
  async created() {
    await this.$store.dispatch('getElectionStates').then(() => {
      if (this.electionState.general != true) {
        this.area_type.shift();
        this.type = this.area_type[0];
      }
      this.selected_state = this.electionState.states[0];
    });

    //this.selected_state = await this.electionState[0].id
    await this.$store.dispatch('getElectionData', {
      type: this.type.value,
      state: this.selected_state,
    });
  },
  computed: {
    electionData() {
      return this.$store.getters.getElectionData;
    },
    electionState() {
      return this.$store.getters.getElectionStates;
    },
    electionCandidates() {
      return this.$store.getters.getElectionCandidates;
    },
    loading() {
      return this.$store.getters.getElectionLoading;
    },
    hasElection() {
      return this.$store.getters.getHasElection;
    },
  },
  methods: {
    cancelEdit(row) {
      row.candidate = row.original_candidate;
      row.official = row.original_official;
      row.edit = false;
      row.candidate_list = [];
    },
    async confirmEdit(row) {
      let data = {
        type: this.type.value,
        areaId: row.id,
        candidateId: row.candidate ? row.candidate.id : null,
        official: row.candidate != undefined ? row.official : false,
      };
      await this.$store.dispatch('updateElectionData', data).then(() => {
        row.edit = false;
        row.original_candidate = row.candidate;
        row.original_official = row.official =
          row.candidate != undefined ? row.official : false;
        row.candidate_list = [];
        this.$swal({
          toast: true,
          position: 'top-end',
          showConfirmButton: false,
          showCancelButton: false,
          timer: 5000,
          icon: 'success',
          text: `${row.code} ${row.name} succesfully updated`,
        });
      });
    },
    async edit(row) {
      row.edit = true;
      row.loading = true;
      row.candidate_list = await this.fetchCandidate(this.type.value, row.id);
      row.loading = false;
    },
    async fetchCandidate(type, areaId) {
      let list = [];
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      await axios
        .get(
          process.env.VUE_APP_ELECTION_CANDIDATES +
            '?r=' +
            r +
            `&type=${type}&areaId=${areaId}`,
          headers.createHeaders(this.$store.getters.getUserToken)
        )
        .then((res) => {
          list = res.data;
        })
        .catch((error) => {
          alert('Something went wrong');
          console.log(error);
          Bugsnag.notify(error);
        });
      return list;
    },
    async updateType(item) {
      if (item.value == 'state' && this.electionState.states[1] !== undefined) {
        this.selected_state = this.electionState.states[1]; // to exclude ft
      }
      await this.$store.dispatch('getElectionData', {
        type: item.value,
        state: this.selected_state,
      });
    },
    async updateState(item) {
      await this.$store.dispatch('getElectionData', {
        type: this.type.value,
        state: item,
      });
    },
    async theButton() {
      this.$swal({
        text: 'Clear Cache and Update Image?',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes',
        cancelButtonText: 'Cancel',
        reverseButtons: true,
        buttonsStyling: true,
      }).then(async (isConfirm) => {
        if (isConfirm.value === true) {
          await this.$store.dispatch('clearCacheGroup', 'ge15');
          await this.$store.dispatch('rebuildSearchCache');
          await this.$store.dispatch('updateElectionImage', 'en');
          await this.$store.dispatch('updateElectionImage', 'ms');
          await this.$store.dispatch('updateElectionImage', 'zh');
        }
      });
    },
  },
};
</script>

<style>
.table-responsive {
  overflow: unset;
}
</style>

<style scoped>
td {
  vertical-align: middle !important;
}

.option__image {
  height: 25px;
  width: 40px;
  margin-right: 10px;
  object-fit: cover;
  display: inline-block;
  vertical-align: middle;
}

.option__desc {
  display: inline-block;
}
</style>
