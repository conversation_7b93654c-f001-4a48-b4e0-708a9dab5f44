<template>
  <div>
    <CRow>
      <CCol>Fields</CCol>
      <CCol>
        <CButton
          @click="show = !show"
          color="info"
          variant="outline"
          shape="pill"
          class="float-right"
        >
          <CIcon name="cil-plus" />
          Field
        </CButton>
      </CCol>
    </CRow>
    <CModal
      :show.sync="show"
      title="Add Lead Form Fields"
      :close-on-backdrop="false"
      centered
    >
      <div class="d-flex flex-column field-wrapper">
        <div
          class="d-flex flex-row align-items-center field-item"
          v-for="item in defaultFieldList"
          :key="item.key"
        >
          <div class="mr-auto">{{ item.label }}</div>
          <div>
            <CButton @click="addToField(item)" :disabled="once(item)">
              <CIcon name="cil-plus"></CIcon>
            </CButton>
          </div>
        </div>
      </div>
      <template slot="footer">
        <CButton color="secondary" @click="show = !show">Cancel</CButton>
      </template>
    </CModal>
    <CModal
      :show.sync="editShow"
      title="Add Lead Form Fields"
      :close-on-backdrop="false"
      centered
    >
      <CInput
        v-model="fieldValue.label"
        label="Field Name"
        :placeholder="fieldValue.placeholder"
        :readonly="
          fieldValue.type === 'name' ||
          fieldValue.type === 'email' ||
          fieldValue.type === 'tel'
        "
      />
      <div>
        <div
          class="form-check form-check-inline"
          v-for="(option, optionIndex) in yes_no_options"
          :key="(option.value % 1) + optionIndex + 'yesno_reader'"
        >
          <input
            class="form-check-input"
            type="radio"
            :id="(option.value % 1) + optionIndex + 'yesno_reader'"
            :value="option.value"
            v-model="fieldValue.required"
          />
          <label
            class="form-check-label"
            :for="(option.value % 1) + optionIndex + 'yesno_reader'"
            >{{ option.label }}</label
          >
        </div>
      </div>
      <template slot="footer">
        <CButton color="success" @click="submitToField" variant="outline"
          >Add Field</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
import slugify from 'slugify';
export default {
  name: 'AddLeadFieldsModal',
  props: {
    fields: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  data() {
    return {
      fieldValue: {
        label: '',
        placeholder: '',
        type: '',
        key: '',
        required: 0,
      },
      show: false,
      editShow: false,
      forEdit: false,
      defaultFieldList: [
        { key: 'name', label: 'Name', once: true, type: 'name' },
        {
          key: 'contact_number',
          label: 'Contact Number',
          once: true,
          type: 'tel',
        },
        { key: 'email', label: 'Email', once: true, type: 'email' },
        { key: 'textField', label: 'Text Field', type: 'text' },
        { key: 'numberField', label: 'Number Field', type: 'number' },
        // {key: 'radioField', label: 'Radio Field', type: 'single_select'},
        // {key: 'checkboxField', label: 'Checkbox Field', type: 'multiple_select'},
      ],
      yes_no_options: [
        { value: 1, label: 'Compulsory' },
        { value: 0, label: 'Optional' },
      ],
    };
  },
  methods: {
    once(item) {
      let index = this.fields.findIndex(
        (fi) => fi.name === item.type + '_' + item.key
      );
      return item.once && index !== -1;
    },
    addToField(item) {
      this.show = !this.show;
      this.setToEdit(item);
    },
    setToEdit(item) {
      let custom = ['name', 'email', 'tel'];
      this.fieldValue = {
        label: custom.includes(item.type) ? item.label : '',
        placeholder: item.label,
        name: custom.includes(item.type)
          ? slugify(item.type + ' ' + item.label.toLowerCase(), '_')
          : (item.name ?? item.key),
        type: item.type,
        required: item.required ?? 1,
      };
      this.forEdit = !this.forEdit;
      this.editShow = !this.editShow;
    },
    submitToField() {
      let pass = {};
      let custom = ['name', 'email', 'tel'];
      Object.entries(this.fieldValue).forEach(([prop, val]) => {
        if (prop !== 'placeholder') {
          if (prop === 'name') {
            if (!custom.includes(this.fieldValue.type)) {
              let idx = this.fields.length;
              pass[prop] = slugify(
                this.fieldValue.type +
                  ' ' +
                  this.fieldValue.label
                    .replace(/[^a-zA-Z0-9 ]/g, '')
                    .toLowerCase() +
                  ` ${idx + 1}`,
                '_'
              );
            } else {
              pass[prop] = val;
            }
          } else {
            pass[prop] = val;
          }
        }
      });
      this.$emit('appendField', { data: pass });
      this.resetFieldValue();
      this.editShow = !this.editShow;
      this.forEdit = !this.forEdit;
    },
    resetFieldValue() {
      this.fieldValue = {
        label: '',
        key: '',
        type: '',
        required: 0,
      };
    },
  },
};
</script>

<style scoped>
.field-item {
  border: solid 1px rgba(178, 178, 178, 0.5);
  padding-left: 20px;
  padding-right: 20px;
  border-radius: 5px;
  margin-bottom: 8px;
}
</style>
