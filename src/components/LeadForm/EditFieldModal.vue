<template>
  <div>
    <CButton @click="show = !show" v-c-tooltip="'Edit Field'">
      <CIcon name="cil-pencil"></CIcon>
    </CButton>
    <CModal
      :show.sync="show"
      title="Edit Lead Form Fields"
      :close-on-backdrop="false"
      centered
      size="lg"
      @update:show="onClose"
    >
      <CInput
        v-model="fieldValue.label"
        label="Field Name"
        :placeholder="fieldValue.placeholder"
        :readonly="
          fieldValue.type === 'name' ||
          fieldValue.type === 'email' ||
          fieldValue.type === 'tel'
        "
      />
      <div>
        <div
          class="form-check form-check-inline"
          v-for="(option, optionIndex) in yes_no_options"
          :key="(option.value % 1) + optionIndex + 'yesno_reader'"
        >
          <input
            class="form-check-input"
            type="radio"
            :id="(option.value % 1) + optionIndex + 'yesno_reader'"
            :value="option.value"
            v-model="fieldValue.required"
          />
          <label
            class="form-check-label"
            :for="(option.value % 1) + optionIndex + 'yesno_reader'"
            >{{ option.label }}</label
          >
        </div>
      </div>
      <template slot="footer">
        <CButton color="success" @click="submitToField" variant="outline"
          >Edit Field</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
export default {
  name: 'EditFieldModal',
  props: {
    field: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    idx: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      show: false,
      yes_no_options: [
        { value: 1, label: 'Compulsory' },
        { value: 0, label: 'Optional' },
      ],
      fieldValue: JSON.parse(JSON.stringify(this.field)),
    };
  },
  methods: {
    submitToField() {
      let pass = {};
      Object.entries(this.fieldValue).forEach(([prop, val]) => {
        if (prop !== 'placeholder') {
          pass[prop] = val;
        }
      });
      this.$emit('editField', { data: pass, index: this.idx });
      this.show = !this.show;
    },
    onClose() {
      this.fieldValue = JSON.parse(JSON.stringify(this.field));
    },
  },
};
</script>

<style scoped></style>
