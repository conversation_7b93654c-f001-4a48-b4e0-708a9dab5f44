<template>
  <div>
    <CButton
      class="float-right"
      variant="outline"
      color="info"
      @click="show = !show"
      >Choose from Existing Lead Form</CButton
    >
    <CModal :show.sync="show" title="Choose Lead Form" size="lg" centered>
      <CDataTable
        :sorter-value="{ column: 'created_at', asc: true }"
        :loading="leadLoading"
        sorter
        table-filter
        small
        :items="forms"
        :fields="lead_fields"
        pagination
      >
        <template #show_details="{ item }">
          <td>
            <CButton
              color="primary"
              variant="outline"
              @click="
                $emit('re-use', item);
                show = !show;
              "
              >Choose
            </CButton>
          </td>
        </template>
      </CDataTable>
      <CPagination
        @update:activePage="goToPage"
        :pages="pagination.last_page"
        :activePage.sync="pagination.current_page"
      />
      <template slot="footer">
        <CButton color="dark" variant="outline" @click="show = !show"
          >Cancel</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
export default {
  name: 'ExistingLeadForm',
  data() {
    return {
      show: false,
      lead_fields: [
        { key: 'created_at', sorter: true, label: 'Created Date' },
        { key: 'form_name', sorter: true, label: 'Lead Form Name' },
        { key: 'advertiserShow', sorter: true, label: 'Advertiser' },
        { key: 'form_title', sorter: true, label: 'Title' },
        { key: 'show_details', sorter: false, filter: false, label: 'Action' },
      ],
    };
  },
  computed: {
    ...mapGetters({
      allAdvertisers: 'allAdvertisers',
      leadLoading: 'getLeadLoading',
      forms: 'getLeadForms',
      pagination: 'getLeadPagination',
    }),
  },
  created() {
    this.listLeadForm({ id: '', currentPage: 1 });
  },
  methods: {
    ...mapActions({
      listLeadForm: 'listLeadForm',
    }),
    goToPage(page) {
      this.listLeadForm({
        id: '',
        currentPage: page,
      });
    },
  },
};
</script>

<style scoped></style>
