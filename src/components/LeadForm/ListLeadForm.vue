<template>
  <div>
    <CCard>
      <CCardHeader>Lead Form List</CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <div v-show="checkAction('lead_add')">
              <router-link :to="'lead-forms/create-lead-form'">
                <CButton
                  class="float-right mb-2"
                  variant="outline"
                  color="success"
                  shape="pill"
                >
                  Create Lead Form
                </CButton>
              </router-link>
            </div>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CDataTable
              :loading="leadLoading"
              :columnFilter="{ external: true }"
              :sorter="{ external: true, resetable: false }"
              small
              :items="forms"
              :fields="lead_fields"
              pagination
              @update:column-filter-value="onFilterChange"
              @update:sorter-value="onSorterChange"
            >
              <template #show_details="{ item }">
                <td>
                  <CButtonToolbar>
                    <div v-show="checkAction('lead_edit')">
                      <router-link
                        :to="'lead-forms/create-lead-form/' + item.id"
                      >
                        <CButton
                          color="warning"
                          variant="outline"
                          v-c-tooltip="'Edit Lead '"
                        >
                          <CIcon name="cil-pencil"></CIcon>
                        </CButton>
                      </router-link>
                    </div>
                    <div v-show="checkAction('lead_download')">
                      <CButton
                        color="info"
                        variant="outline"
                        v-c-tooltip="'Download Lead CSV'"
                        @click="createLeadCSV(item)"
                      >
                        <CIcon name="cil-arrow-thick-to-bottom"></CIcon>
                      </CButton>
                    </div>
                  </CButtonToolbar>
                </td>
              </template>
            </CDataTable>
            <CPagination
              @update:activePage="goToPage"
              :pages="pagination.last_page"
              :activePage.sync="pagination.current_page"
            />
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import { mapActions } from 'vuex';

export default {
  name: 'ListLeadForm',
  data() {
    return {
      lead_fields: [
        {
          key: 'created_at',
          sorter: true,
          filter: false,
          label: 'Created Date',
        },
        {
          key: 'form_name',
          sorter: true,
          filter: true,
          label: 'Lead Form Name',
        },
        {
          key: 'advertiserShow',
          sorter: false,
          filter: false,
          label: 'Advertiser',
        },
        { key: 'form_title', sorter: true, filter: true, label: 'Title' },
        { key: 'show_details', sorter: false, filter: false, label: 'Action' },
      ],
      filters: {},
      sorters: {},
      debounceTimer: null,
    };
  },
  created() {
    this.listLeadForm({ id: '', currentPage: 1 });
  },
  computed: {
    leadLoading() {
      return this.$store.getters.getLeadLoading;
    },
    forms() {
      return this.$store.getters.getLeadForms;
    },
    pagination() {
      return this.$store.getters.getLeadPagination;
    },
  },
  methods: {
    ...mapActions({
      listLeadForm: 'listLeadForm',
    }),
    async onFilterChange(updatedFilters) {
      clearTimeout(this.debounceTimer);

      this.filters = updatedFilters;
      this.debounceTimer = setTimeout(() => {
        let title = null;
        let name = null;
        let sortBy = null;
        let asc = null;
        console.log(this.filters);
        if (this.filters.form_name) {
          name = this.filters.form_name;
        }
        if (this.filters.form_title) {
          title = this.filters.form_title;
        }

        if (this.sorters.column != null && this.sorters.asc != null) {
          sortBy = this.sorters.column;
          asc = this.sorters.asc;
        }

        this.listLeadForm({
          id: '',
          name: name,
          title: title,
          sortBy: sortBy,
          asc: asc,
        });
      }, 1000);
    },
    async onSorterChange(updatedSorter) {
      this.sorters = updatedSorter;
      let sortBy = this.sorters.column;
      let asc = this.sorters.asc;
      let title = null;
      let name = null;
      if (this.filters.form_title) {
        title = this.filters.form_title;
      }
      if (this.filters.form_name) {
        name = this.filters.form_name;
      }

      this.listLeadForm({
        id: '',
        name: name,
        title: title,
        sortBy: sortBy,
        asc: asc,
      });
    },
    createLeadCSV(lead) {
      this.$store.commit('SET_LEAD_LOADING', true);
      this.$store
        .dispatch('downloadLeadCSV', lead)
        .then((res) => {
          let csv = '';
          let base = new Date();
          let yy = base.getFullYear();
          let mm = base.getMonth();
          if (mm < 10) {
            mm = '0' + mm;
          }
          let dd = base.getDate();
          if (dd < 10) {
            dd = '0' + dd;
          }
          res.forEach((item) => {
            csv += item.join(',') + '\n';
          });
          const anchor = document.createElement('a');
          let date = yy + mm + dd;
          let name = `${lead.advertiserShow}_${lead.form_name}_${date}.csv`;
          anchor.href =
            'data:text/csv;charset=utf-8,%EF%BB%BF' + encodeURIComponent(csv);
          anchor.target = '_blank';
          anchor.download = name;
          anchor.click();
          this.$store.commit('SET_LEAD_LOADING', false);
        })
        .catch((err) => {
          this.$swal.fire({
            icon: 'warning',
            text: err.response.data.message,
          });
          this.$store.commit('SET_GENERAL_LOADING', false);
        });
    },
    goToPage(page) {
      let title = null;
      let name = null;
      let sortBy = null;
      let asc = null;
      if (this.filters.form_name) {
        name = this.filters.form_name;
      }
      if (this.filters.form_title) {
        title = this.filters.form_title;
      }

      if (this.sorters.column != null && this.sorters.asc != null) {
        sortBy = this.sorters.column;
        asc = this.sorters.asc;
      }
      this.listLeadForm({
        name: name,
        title: title,
        sortBy: sortBy,
        asc: asc,
        id: '',
        currentPage: page,
      });
    },
  },
};
</script>

<style scoped></style>
