<template>
  <div>
    <CCard>
      <CCardHeader>{{
        edit ? 'Edit Lead Form' : 'Create Lead Form'
      }}</CCardHeader>
      <CCardBody>
        <BaseLeadForm
          :lead="lead"
          @setLogo="logo"
          @appendField="addField"
          @removeField="popField"
          @editField="addField"
          @appendImg="addImage"
          @removeImg="popImage"
          @setReUse="setToReUse"
        />
        <br />
        <CButton
          block
          variant="outline"
          color="success"
          :disabled="is_disabled"
          @click="createLead"
        >
          {{ edit ? 'Update' : 'Submit' }}</CButton
        >
        <OverlayLoader v-if="loading" />
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import BaseLeadForm from '@/components/LeadForm/BaseLeadForm';
import OverlayLoader from '@/components/views/OverlayLoader';
export default {
  name: 'CreateLeadForm',
  components: { OverlayLoader, BaseLeadForm },
  data() {
    return {
      lead: {
        form_name: '',
        form_title: '',
        advertiser: '',
        advertiser_logo: '',
        form_description: '',
        form_policy: '',
        form_website: '',
        fields: [],
        images: [],
        email_to: '',
      },
      edit: this.$route.params.id !== undefined,
    };
  },
  created() {
    if (this.$route.params.id !== undefined) {
      this.$store.commit('SET_GENERAL_LOADING', true);
      this.$store
        .dispatch('listLeadForm', { id: parseInt(this.$route.params.id) })
        .then((res) => {
          this.lead = res.data[0];
          this.$store.commit('SET_GENERAL_LOADING', false);
        })
        .catch(() => {
          alert('Something went wrong in retrieving data');
          this.$store.commit('SET_GENERAL_LOADING', false);
        });
    }
  },
  computed: {
    is_disabled() {
      let errors = [];
      let custom = [
        'fields',
        'images',
        'form_policy',
        'form_website',
        'email_to',
      ];
      Object.entries(this.lead).forEach(([prop, val]) => {
        if (!custom.includes(prop) && val === '') {
          errors.unshift(prop);
        } else if (
          (prop === 'fields' || prop === 'images') &&
          val.length === 0
        ) {
          errors.unshift(prop);
        } else if (
          (prop === 'form_policy' || prop === 'form_website') &&
          !val.includes('http')
        ) {
          errors.unshift(prop);
        }
      });
      return errors.length > 0;
    },
    loading() {
      return this.$store.getters.getLoading;
    },
  },
  methods: {
    logo(val) {
      this.lead.advertiser_logo = val;
    },
    addField(item) {
      let idx = item.index ?? -1;
      if (idx !== -1) {
        this.lead.fields.splice(idx, 1, item.data);
      } else {
        this.lead.fields.push(item.data);
      }
    },
    popField(idx) {
      this.lead.fields.splice(idx, 1);
    },
    addImage(link) {
      this.lead.images.push(link);
    },
    popImage(idx) {
      this.lead.images.splice(idx, 1);
    },
    setToReUse(item) {
      this.lead = item;
    },
    createLead() {
      let pass = {};
      Object.entries(this.lead).forEach(([prop, val]) => {
        pass[prop] = val;
      });
      if (this.edit) {
        this.$store
          .dispatch('editForm', pass)
          .then(() => {
            this.$router.push('/lead-forms');
          })
          .catch((err) => {
            console.log(err);
          });
      } else {
        this.$store
          .dispatch('createForm', pass)
          .then(() => {
            this.$router.push('/lead-forms');
          })
          .catch((err) => {
            console.log(err);
          });
      }
    },
  },
};
</script>

<style scoped></style>
