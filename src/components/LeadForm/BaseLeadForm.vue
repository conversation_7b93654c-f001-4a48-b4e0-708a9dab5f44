<template>
  <div>
    <CRow class="form-content">
      <CCol>
        <CRow>
          <CCol> <ExistingLeadForm @re-use="passReUse" /><br /> </CCol>
        </CRow>
        <br />
        <CInput label="Lead Form Name" v-model="lead.form_name" />
        <div>
          <label for="advertiserSelect">Advertiser</label>
          <multiselect
            id="advertiserSelect"
            :multiple="false"
            v-model="lead.advertiser"
            track-by="id"
            label="name"
            :options="advertisers"
          >
            <span slot="noResult"
              >Oops! No advertiser with the name. Consider changing the search
              query.</span
            >
          </multiselect>
          <br />
        </div>
        <CRow>
          <CCol><label for="adIconFile1">Advertiser Logo</label></CCol>
          <CCol>
            <label class="custom-file-upload float-right">
              <input
                type="file"
                id="adIconFile1"
                @change="onFileChange($event, 'icon')"
                ref="inputIcon"
                class="form-control-file"
                data-content="icon"
                accept=".png, .jpg, .jpeg"
              />
              <i class="fa fa-cloud-upload"></i> Upload
            </label>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <div class="d-flex flex-row justify-content-center">
              <img
                v-if="lead.advertiser_logo"
                :src="lead.advertiser_logo"
                alt=""
                class="logo_place"
              />
              <div v-else class="placeholder_img"></div>
            </div>
          </CCol>
        </CRow>
        <CInput label="Title" v-model="lead.form_title" />
        <CRow>
          <CCol>
            <CRow>
              <CCol>
                <label>Lead Form Images</label>
                <label class="custom-file-upload float-right">
                  <input
                    type="file"
                    id="leadImages"
                    @change="onFileChange($event, 'img')"
                    ref="inputIcon"
                    class="form-control-file"
                    data-content="icon"
                    accept=".png, .jpg, .jpeg"
                  />
                  <i class="fa fa-cloud-upload"></i> Upload
                </label>
              </CCol>
            </CRow>
            <br />
            <CRow>
              <CCol>
                <div class="d-flex flex-row flex-wrap align-items-center">
                  <div
                    v-for="(img, idx) in lead.images"
                    :key="idx"
                    style="margin-right: 5px; margin-top: 5px"
                  >
                    <div
                      class="d-flex flex-column"
                      style="border: 1px solid #b2b2b2; border-radius: 3px"
                    >
                      <div>
                        <img :src="img" alt="" width="240" class="img-items" />
                      </div>
                      <div class="btn-wrapper">
                        <CButton
                          class="float-right"
                          @click="$emit('removeImg', idx)"
                        >
                          <CIcon name="cil-trash"></CIcon>
                        </CButton>
                      </div>
                    </div>
                  </div>
                </div>
              </CCol>
            </CRow>
          </CCol>
        </CRow>
        <br />
        <CTextarea label="Description" v-model="lead.form_description" />
        <CRow>
          <CCol>
            <CRow>
              <CCol>
                <AddLeadFieldsModal
                  :fields="lead.fields"
                  @appendField="passField"
                />
              </CCol>
            </CRow>
            <br />
            <CRow>
              <CCol>
                <draggable
                  :list="lead.fields"
                  handle=".handle"
                  ghost-class="ghost"
                >
                  <div
                    class="d-flex flex-row align-items-center selected-field-list"
                    v-for="(field, idx) in lead.fields"
                    :key="field.name + idx + 'selected'"
                  >
                    <div class="mr-2">
                      <a
                        class="handle"
                        href="#"
                        style="text-decoration: none; color: #121212"
                      >
                        <CIcon name="cil-cursor-move"></CIcon>
                      </a>
                    </div>
                    <div class="mr-auto">
                      <div>
                        Label : <strong> {{ field.label }} </strong>
                      </div>
                    </div>
                    <div>
                      <CButtonToolbar>
                        <EditFieldModal
                          :field="field"
                          :idx="idx"
                          @editField="passEditField"
                        />
                        <CButton
                          v-c-tooltip="'Delete Field'"
                          @click="$emit('removeField', idx)"
                        >
                          <CIcon name="cil-trash"></CIcon>
                        </CButton>
                      </CButtonToolbar>
                    </div>
                  </div>
                </draggable>
              </CCol>
            </CRow>
            <br />
          </CCol>
        </CRow>
        <CInput
          label="Privacy Policy"
          v-model="lead.form_policy"
          :is-valid="urlValidator"
        />
        <CInput
          label="Website URL"
          v-model="lead.form_website"
          :is-valid="urlValidator"
        />
        <CInput label="Email Report To" v-model="lead.email_to" />
      </CCol>
      <CCol>
        <LeadFormPreview :lead="lead" />
      </CCol>
    </CRow>
  </div>
</template>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>

<script>
import { mapGetters, mapActions } from 'vuex';
import { Multiselect } from 'vue-multiselect';
import slugify from 'slugify';
import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import LeadFormPreview from '@/components/FormWizard/LeadForm/LeadFormPreview';
import AddLeadFieldsModal from '@/components/LeadForm/AddLeadFieldsModal';
import draggable from 'vuedraggable';
import EditFieldModal from '@/components/LeadForm/EditFieldModal';
import ExistingLeadForm from '@/components/LeadForm/ExistingLeadForm';

export default {
  name: 'BaseLeadForm',
  components: {
    ExistingLeadForm,
    EditFieldModal,
    AddLeadFieldsModal,
    LeadFormPreview,
    Multiselect,
    draggable,
  },
  data() {
    return {
      show: false,
      lead_fields: [
        { key: 'created_at', label: 'Created Date' },
        { key: 'form_name', label: 'Lead Form Name' },
        { key: 'advertiserShow', label: 'Advertiser' },
        { key: 'form_title', label: 'Title' },
        { key: 'show_details', sorter: false, filter: false, label: 'Action' },
      ],
    };
  },
  props: {
    lead: {
      type: Object,
      required: true,
    },
  },
  computed: {
    ...mapGetters({
      allAdvertisers: 'allAdvertisers',
      leadLoading: 'getLeadLoading',
      loading: 'getLoading',
      forms: 'getLeadForms',
    }),
    advertisers() {
      let data = [];
      this.allAdvertisers.forEach((item) => {
        let pass = {
          id: item.id,
          name: item.name,
        };
        data.push(pass);
      });
      data.sort((a, b) => a.name.localeCompare(b.name));
      return data;
    },
    logo_display() {
      return this.lead.advertiser_logo;
    },
  },
  created() {
    if (this.allAdvertisers.length === 0) {
      this.listAdvertiser();
    }
    if (this.forms.length === 0) {
      this.listLeadForm({ id: '', currentPage: 1 });
    }
  },
  methods: {
    ...mapActions({
      listAdvertiser: 'getAllAdvertisers',
      listLeadForm: 'listLeadForm',
    }),
    passField(item) {
      this.$emit('appendField', item);
    },
    passEditField(item) {
      this.$emit('editField', item);
    },
    passReUse(item) {
      this.$emit('setReUse', item);
    },
    urlValidator(val) {
      return (
        (val.includes('http://') || val.includes('https://')) && val !== ''
      );
    },
    onFileChange(event, type) {
      let input = event.target;
      if (input.files && input.files[0]) {
        let iSize = input.files[0].size / 1000 / 1000;
        if (type === 'icon') {
          if (iSize <= 0.1) {
            this.getIconDimension(input);
          } else {
            alert('File size exceeded. Max 100KB');
            this.$refs.inputIcon.value = null;
          }
        } else {
          if (iSize <= 0.5) {
            this.getCoverDimension(input);
          } else {
            alert('File size exceeded. Max 500KB for Image');
          }
        }
      }
    },
    getCoverDimension(input) {
      let file = input.files[0];
      if (!file || file.type.indexOf('image/') !== 0) {
        alert('Image not found');
      } else {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (evt) => {
          let img = new Image();
          img.onload = () => {
            let w = img.width;
            let h = img.height;
            let r = this.gcd(w, h);
            var error = 'Image must be in 16:9 ratio';
            var a1 = w / r;
            var a2 = h / r;
            if (a1 === 16 && a2 === 9) {
              this.googleUpload(input, 'img');
            } else {
              alert(error);
            }
          };
          img.src = evt.target.result;
        };
        reader.onerror = (evt) => {
          console.error(evt);
        };
      }
    },
    getIconDimension(input) {
      let file = input.files[0];
      if (!file || file.type.indexOf('image/') !== 0) {
        alert('image not found');
      } else {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (evt) => {
          let img = new Image();
          img.onload = () => {
            let w = img.width;
            let h = img.height;
            let r = this.gcd(w, h);

            var error = 'Logo must be in 1:1 ratio';
            var a1 = w / r;
            var a2 = h / r;
            if (a1 == 1 && a2 == 1) {
              this.googleUpload(input, 'logo');
            } else {
              alert(error);
              this.$refs.inputIcon.value = null;
            }
          };
          img.src = evt.target.result;
        };
        reader.onerror = (evt) => {
          console.error(evt);
        };
      }
    },
    gcd(a, b) {
      return b == 0 ? a : this.gcd(b, a % b);
    },
    googleUpload: function (input, type) {
      let fileName = slugify(input.files[0].name);
      let fileData = new FormData();
      fileData.append('path', 'boss-images/temp');
      fileData.append('file', input.files[0]);
      fileData.append('name', fileName);
      this.$store.commit('SET_GENERAL_LOADING', true);
      axios
        .post(process.env.VUE_APP_UPLOAD_FILE_TO_GCS, fileData)
        .then((res) => {
          if (type === 'logo') {
            this.$emit('setLogo', res.data.url);
          } else this.$emit('appendImg', res.data.url);
          this.$store.commit('SET_GENERAL_LOADING', false);
        })
        .catch((error) => {
          this.$store.commit('SET_GENERAL_LOADING', false);
          alert('Something went wrong with uploading media');
          console.log('----Error----');
          console.log(error);
          Bugsnag.notify(error);
        });
    },
  },
};
</script>

<style scoped>
input[type='file'] {
  display: none;
}

.custom-file-upload {
  border: 1px solid #ccc;
  display: inline-block;
  padding: 6px 12px;
  cursor: pointer;
}

.selected-field-list {
  border: solid 1px rgba(178, 178, 178, 0.5);
  background-color: #ffffff;
  padding-left: 20px;
  padding-right: 20px;
  border-radius: 5px;
  margin-bottom: 8px;
}

.placeholder_img {
  height: 120px;
  width: 120px;
  background-color: lightblue;
  border-radius: 50%;
  border: 1px solid #b2b2b2;
}

.logo_place {
  border-radius: 50%;
  border: 1px solid #b2b2b2;
  width: 120px;
  height: 120px;
}

.handle {
  float: left;
  padding-top: 8px;
  padding-bottom: 8px;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

@media (max-width: 900px) {
  .form-content {
    display: flex;
    flex-direction: column;
  }
}
</style>
