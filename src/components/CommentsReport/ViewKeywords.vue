<template>
  <div>
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader> Auto Report Keywords</CCardHeader>
          <CCardBody>
            <!-- <CSpinner size="lg" color="info" /> -->
            <div v-if="!loading">
              <CRow>
                <CCol>
                  <div>
                    <CButton
                      @click="collapse = !collapse"
                      color="success"
                      class="mb-2"
                      style="display: block"
                      v-show="checkAction('keyword_list_update')"
                    >
                      Edit
                    </CButton>
                    <CCollapse :show="collapse">
                      <CCard body-wrapper>
                        <div>
                          <CTextarea
                            v-model="hide"
                            label="Prohibited Keyword"
                            rows="7"
                          ></CTextarea>

                          <CTextarea
                            v-model="report"
                            label="Sensitive Keyword"
                            rows="7"
                          ></CTextarea>
                        </div>
                        <CButton
                          v-show="checkAction('keyword_list_update')"
                          class="float-right mx-auto"
                          variant="outline"
                          color="success"
                          @click="update"
                        >
                          Update
                        </CButton>
                      </CCard>
                    </CCollapse>
                  </div>
                </CCol>
              </CRow>
              <hr />
              <CRow style="height: 600px">
                <CCol class="h-100">
                  <div class="h-100">
                    Prohibited Keyword List
                    <br />
                    <ul id="report" class="overflow-auto" style="height: 580px">
                      <li v-for="(item, index) in hideKeywords" :key="index">
                        {{ item }}
                      </li>
                    </ul>
                  </div>
                </CCol>
                <CCol class="h-100">
                  <div class="h-100">
                    Sensitive Keyword List

                    <br />
                    <ul id="report" class="overflow-auto" style="height: 580px">
                      <li v-for="(item, index) in reportKeywords" :key="index">
                        {{ item }}
                      </li>
                    </ul>
                  </div>
                </CCol>
              </CRow>
            </div>
            <CElementCover :opacity="0.8" v-else />
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
export default {
  name: 'ViewKeywords',
  data() {
    return {
      report: '',
      hide: '',
      collapse: false,
    };
  },
  computed: {
    ...mapGetters({
      reportKeywords: 'getReportKeywords',
      hideKeywords: 'getHideKeywords',
      loading: 'getReportsLoading',
    }),
  },
  created() {
    this.listKeywords().then(() => {
      this.report = this.reportKeywords.join(',');
      this.hide = this.hideKeywords.join(',');
    });
  },
  methods: {
    ...mapActions({
      listKeywords: 'getServiceKeywords',
      updateKeywords: 'updateServiceKeywords',
    }),
    update() {
      this.$swal({
        title: 'Are you sure?',
        text: 'Update the comment report keywords',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes',
        cancelButtonText: 'Cancel',
        buttonsStyling: true,
      }).then((isConfirm) => {
        if (isConfirm.value === true) {
          const toReport = this.report.split(',');
          const toHide = this.hide.split(',');
          this.updateKeywords({ report: toReport, hide: toHide }).then(() => {
            this.report = this.reportKeywords.join(',');
            this.hide = this.hideKeywords.join(',');
          });
        }
      });
    },
  },
};
</script>

<style scoped>
.slot {
  background-color: #aaa;
  padding: 0.5rem;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-black {
  color: #000;
}
</style>
