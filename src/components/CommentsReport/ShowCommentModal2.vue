<template>
  <div>
    <CButton
      class="float-right"
      variant="outline"
      color="primary"
      @click="show = true"
      v-c-tooltip="'View Comment'"
      ><CIcon name="cil-speech"></CIcon
    ></CButton>
    <CModal
      title="Comment Details"
      color="default"
      size="lg"
      :show.sync="show"
      centered
    >
      <CRow>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Comment By</small><br />
            <strong class="h4"
              >{{ comment.user.name }} ({{ comment.user.id }})</strong
            >
          </CCallout>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Comment</small><br />
            <strong class="h6" v-html="formatMessage(comment)"></strong>
          </CCallout>
        </CCol>
      </CRow>
      <template slot="footer">
        <div v-if="checkAction('comment_action')">
          <CButton
            v-if="comment.statusField.hidden === false"
            :id="comment.id"
            @click="updateCommentStatus(comment._id, 'hide')"
            target="_blank"
            color="danger"
            variant="outline"
            v-c-tooltip="'Hide'"
            ><font-awesome-icon icon="eye-slash" />
          </CButton>
          <CButton
            v-else
            :id="comment.id"
            @click="updateCommentStatus(comment._id, 'unhide')"
            target="_blank"
            color="danger"
            variant="outline"
            v-c-tooltip="'Unhide'"
            ><font-awesome-icon icon="eye" />
          </CButton>
          <CButton
            v-if="comment.statusField.pinned === false"
            :id="comment.id"
            @click="updateCommentStatus(comment._id, 'pin')"
            target="_blank"
            color="danger"
            variant="outline"
            v-c-tooltip="'Pin'"
            ><font-awesome-icon icon="thumbtack" />
          </CButton>
          <CButton
            v-else
            :id="comment.id"
            @click="updateCommentStatus(comment._id, 'unpin')"
            target="_blank"
            color="danger"
            variant="outline"
            v-c-tooltip="'Unpin'"
            ><font-awesome-icon icon="times-circle" />
          </CButton>
        </div>
      </template>
    </CModal>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import commentMixin from '../../mixins/comment';
export default {
  name: 'ShowCommentModal',
  mixins: [commentMixin],
  components: '',
  props: {
    comment: {
      type: Object,
      required: true,
    },
    section: {
      type: Boolean,
    },
    dateRange: {
      type: Object,
      required: true,
    },
    page: {
      type: Number,
      require: true,
    },
    content: {
      require: true,
    },
    commentby: {
      require: true,
    },
    source: {
      require: true,
    },
    type: {
      require: true,
    },
  },
  data() {
    return {
      show: false,
    };
  },
  methods: {
    ...mapActions({
      updateComment: 'updateServiceComment',
    }),
    updateCommentStatus(id, action) {
      const payload = {
        hidden: false,
        pinned: false,
      };
      let status = '';
      switch (action) {
        case 'hide':
          payload.hidden = true;
          status = 'hidden';
          break;
        case 'unhide':
          payload.hidden = false;
          status = 'unhidden';
          break;
        case 'pin':
          payload.pinned = true;
          status = 'pinned';
          break;
        case 'unpin':
          payload.pinned = false;
          status = 'unpinned';
          break;
        default:
          break;
      }
      this.$swal({
        icon: 'question',
        title: 'Are you sure?',
        text: `This will ${action} the comment. Proceed?`,
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes',
        cancelButtonText: 'Cancel',
        buttonsStyling: true,
      }).then((isConfirm) => {
        if (isConfirm.value === true) {
          this.updateComment({ id, payload, status });
        }
      });
    },
  },
};
</script>

<style scoped></style>
