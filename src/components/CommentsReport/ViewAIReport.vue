<template>
  <div>
    <CRow>
      <CCol>
        <CCard>
          <date-range-picker
            v-model="dateRange"
            class="float-left"
            :ranges="false"
            :locale-data="{ firstDay: 1, format: 'yyyy/mm/dd' }"
            @update="updateDate"
          >
          </date-range-picker>
        </CCard>
      </CCol>
    </CRow>
    <CRow>
      <CCol>
        <CDataTable
          :items="reporteds"
          :fields="fields"
          hover
          :columnFilter="{ external: true }"
          :sorter="{ external: true, resetable: false }"
          pagination
          :loading="loading"
          @update:column-filter-value="onFilterChange"
          @update:sorter-value="onSorterChange"
        >
          <template #comment.message="{ item }">
            <td
              v-html="
                truncate(formatMessageByUsername(item.comment), 100, '...')
              "
            ></td>
          </template>
          <template #action="{ item }">
            <td>
              <CButtonToolbar>
                <ShowCommentModal
                  :comment="item"
                  :section="true"
                  :dateRange="dateRange"
                  :isAI="true"
                />
                <div v-if="checkAction('report_comment_action')">
                  <div v-if="showWhichButtons(item)">
                    <CButton
                      :id="item._id"
                      @click="updateReportedComment(item._id, 'hide')"
                      target="_blank"
                      color="danger"
                      variant="outline"
                      v-c-tooltip="'Hide/Unhide'"
                      ><font-awesome-icon icon="eye-slash"
                    /></CButton>
                    <CButton
                      :id="item._id"
                      @click="updateReportedComment(item._id, 'ignore')"
                      target="_blank"
                      color="warning"
                      variant="outline"
                      v-c-tooltip="'Ignore'"
                      ><font-awesome-icon icon="check"
                    /></CButton>
                  </div>
                  <CButton
                    v-else
                    :id="item._id"
                    @click="updateReportedComment(item._id, 'unhide')"
                    target="_blank"
                    color="danger"
                    variant="outline"
                    v-c-tooltip="'Hide/Unhide'"
                    ><font-awesome-icon icon="eye"
                  /></CButton>
                </div>
              </CButtonToolbar>
            </td>
          </template>
        </CDataTable>
        <CPagination
          @update:activePage="goToPage"
          :pages="pagination.last_page"
          :activePage.sync="pagination.current_page"
        />
      </CCol>
    </CRow>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import DateRangePicker from 'vue2-daterange-picker';
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';
import ShowCommentModal from './ShowCommentModal';
import commentMixin from '../../mixins/comment';

export default {
  name: 'ReportCommentTable',
  mixins: [commentMixin],
  components: { DateRangePicker, ShowCommentModal },
  data() {
    let startDate = new Date();
    let endDate = new Date();
    startDate.setDate(startDate.getDate() - 0);
    endDate.setDate(endDate.getDate() - 0);
    return {
      fields: [
        { key: 'createdAt', sorter: true, filter: false, label: 'Reported At' },
        {
          key: 'comment.message',
          sorter: false,
          filter: true,
          label: 'Comment',
        },
        { key: 'userInfo', sorter: true, filter: true, label: 'Commented By' },
        {
          key: 'reportCount',
          sorter: false,
          filter: false,
          label: 'Report Count',
        },
        { key: 'source', sorter: true, filter: true, label: 'Source' },
        { key: 'reviewType', sorter: true, filter: false, label: 'Status' },
        { key: 'reviewer', sorter: true, filter: true, label: 'Reviewed By' },
        {
          key: 'reviewedAt',
          sorter: true,
          filter: false,
          label: 'Reviewed On',
        },
        {
          key: 'action',
          sorter: true,
          label: 'Actions',
          filter: false,
          _style: { width: '10px' },
        },
      ],
      dateRange: { startDate, endDate },
      currentPage: 1,
      filters: {},
      sorters: {},
      columnMapping: {
        createdAt: 'createdAt',
        userInfo: 'comment.user.name',
        source: 'comment.contentId',
        reviewType: 'reviewType',
        reviewer: 'reviewer.userName',
        reviewedAt: 'reviewedAt',
      },
    };
  },
  filters: {
    truncate: function (text, length, suffix) {
      if (text.length > length) {
        return text.substring(0, length) + suffix;
      } else {
        return text;
      }
    },
  },
  computed: {
    ...mapGetters({
      reporteds: 'getAIReports',
      pagination: 'getAiCommentsPagination',
      loading: 'getAIReportsLoading',
      user: 'getUser',
    }),
  },
  created() {
    const startDate = new Date(
      new Date(this.dateRange.startDate).setHours(0, 0, 0, 0)
    ).toISOString();
    const endDate = new Date(
      new Date(this.dateRange.endDate).setHours(23, 59, 59, 999)
    ).toISOString();
    this.listReported({ startDate, endDate, currentPage: this.currentPage });
  },
  methods: {
    ...mapActions({
      listReported: 'getServiceAIReports',
      markHide: 'hideReported',
      markUnhide: 'unhideReported',
      markIgnore: 'ignoreReported',
    }),
    async onFilterChange(updatedFilters) {
      clearTimeout(this.debounceTimer);
      this.filters = updatedFilters;
      this.currentPage = 1;
      const startDate = new Date(
        new Date(this.dateRange.startDate).setHours(0, 0, 0, 0)
      ).toISOString();
      const endDate = new Date(
        new Date(this.dateRange.endDate).setHours(23, 59, 59, 999)
      ).toISOString();

      this.debounceTimer = setTimeout(() => {
        let comment = null;
        let user = null;
        let source = null;
        let reviewer = null;
        let sortBy = null;
        let asc = null;
        let mappedSortBy = null;

        if (this.filters['comment.message']) {
          comment = this.filters['comment.message'];
        }
        if (this.filters.userInfo) {
          user = this.filters.userInfo;
        }

        if (this.filters.source) {
          source = this.filters.source;
        }

        if (this.filters.reviewer) {
          reviewer = this.filters.reviewer;
        }

        if (this.sorters.column != null && this.sorters.asc != null) {
          sortBy = this.sorters.column;
          asc = this.sorters.asc;
          mappedSortBy = this.columnMapping[sortBy] || null;
        }

        this.listReported({
          startDate,
          endDate,
          comment: comment,
          user: user,
          source: source,
          reviewer: reviewer,
          sortBy: mappedSortBy,
          asc: asc,
        });
      }, 1000);
    },
    async onSorterChange(updatedSorter) {
      this.sorters = updatedSorter;
      let sortBy = this.sorters.column;
      let asc = this.sorters.asc;
      this.currentPage = 1;
      const startDate = new Date(
        new Date(this.dateRange.startDate).setHours(0, 0, 0, 0)
      ).toISOString();
      const endDate = new Date(
        new Date(this.dateRange.endDate).setHours(23, 59, 59, 999)
      ).toISOString();
      let comment = null;
      let user = null;
      let source = null;
      let reviewer = null;

      if (this.filters['comment.message']) {
        comment = this.filters['comment.message'];
      }

      if (this.filters.userInfo) {
        user = this.filters.userInfo;
      }

      if (this.filters.source) {
        source = this.filters.source;
      }

      if (this.filters.reviewer) {
        reviewer = this.filters.reviewer;
      }
      const mappedSortBy = this.columnMapping[sortBy] || null;
      this.listReported({
        startDate,
        endDate,
        comment: comment,
        user: user,
        source: source,
        reviewer: reviewer,
        sortBy: mappedSortBy,
        asc: asc,
      });
    },
    showWhichButtons(reported) {
      const checkTypes = ['unhidden', 'ignored', '-'];
      const reviewType = reported.reviewType.toLowerCase() ?? '-';
      return checkTypes.includes(reviewType);
    },
    updateDate(data) {
      const startDate = new Date(
        new Date(data.startDate).setHours(0, 0, 0, 0)
      ).toISOString();
      const endDate = new Date(
        new Date(data.endDate).setHours(23, 59, 59, 999)
      ).toISOString();
      let comment = null;
      let user = null;
      let source = null;
      let reviewer = null;
      let sortBy = null;
      let asc = null;
      let mappedSortBy = null;

      if (this.filters['comment.message']) {
        comment = this.filters['comment.message'];
      }

      if (this.filters.userInfo) {
        user = this.filters.userInfo;
      }

      if (this.filters.source) {
        source = this.filters.source;
      }

      if (this.filters.reviewer) {
        reviewer = this.filters.reviewer;
      }

      if (this.sorters.column != null && this.sorters.asc != null) {
        sortBy = this.sorters.column;
        asc = this.sorters.asc;
        mappedSortBy = this.columnMapping[sortBy] || null;
      }

      this.listReported({
        startDate,
        endDate,
        comment: comment,
        user: user,
        source: source,
        reviewer: reviewer,
        sortBy: mappedSortBy,
        asc: asc,
      });
    },
    updateReportedComment(comment, status) {
      this.$swal({
        icon: 'question',
        title: 'Are you sure?',
        text: `This will ${status} the comment. Proceed?`,
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes',
        cancelButtonText: 'Cancel',
        buttonsStyling: true,
      }).then(async (isConfirm) => {
        if (isConfirm.value === true) {
          if (status === 'hide') {
            await this.markHide(comment);
          } else if (status === 'unhide') {
            await this.markUnhide(comment);
          } else if (status === 'ignore') {
            await this.markIgnore(comment);
          }
        }
      });
    },
    goToPage(page) {
      this.currentPage = page;
      const startDate = new Date(
        new Date(this.dateRange.startDate).setHours(0, 0, 0, 0)
      ).toISOString();
      const endDate = new Date(
        new Date(this.dateRange.endDate).setHours(23, 59, 59, 999)
      ).toISOString();
      let comment = null;
      let user = null;
      let source = null;
      let reviewer = null;
      let sortBy = null;
      let asc = null;
      let mappedSortBy = null;

      if (this.filters['comment.message']) {
        comment = this.filters['comment.message'];
      }

      if (this.filters.userInfo) {
        user = this.filters.userInfo;
      }

      if (this.filters.source) {
        source = this.filters.source;
      }

      if (this.filters.reviewer) {
        reviewer = this.filters.reviewer;
      }

      if (this.sorters.column != null && this.sorters.asc != null) {
        sortBy = this.sorters.column;
        asc = this.sorters.asc;
        mappedSortBy = this.columnMapping[sortBy] || null;
      }

      this.listReported({
        startDate,
        endDate,
        comment: comment,
        user: user,
        source: source,
        reviewer: reviewer,
        sortBy: mappedSortBy,
        asc: asc,
        currentPage: this.currentPage,
      });
    },
  },
};
</script>

<style scoped>
.slot {
  background-color: #aaa;
  padding: 0.5rem;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-black {
  color: #000;
}
</style>
