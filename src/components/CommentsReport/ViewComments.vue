<template>
  <div>
    <CRow>
      <CCol>
        <CCard>
          <date-range-picker
            v-model="dateRange"
            class="float-left"
            :ranges="false"
            :locale-data="{ firstDay: 1, format: 'yyyy/mm/dd' }"
            @update="updateDate"
          >
          </date-range-picker>
        </CCard>
      </CCol>
    </CRow>
    <CRow>
      <CCol>
        <CDataTable
          :key="random"
          :items="comments"
          :fields="fields"
          column-filter
          hover
          sorter
          :loading="loading"
          :columnFilter="{ external: true }"
          @update:column-filter-value="debouncedQuery"
          @filtered-items-changes="comments"
        >
          <template #contentType-filter>
            <CSelect
              :options="typeoptions"
              :value.sync="type"
              style="margin-bottom: 0px"
              @update:value="updateType"
            />
          </template>
          <template #createdDate="{ item }">
            <td>
              {{ item.createdAt | date_format }}
            </td>
          </template>
          <template #message="{ item }">
            <td>
              <div v-html="truncate(formatMessage(item), 100, '...')"></div>
            </td>
          </template>
          <template #contentType="{ item }">
            <td style="text-transform: capitalize">
              {{ item.contentType }}
            </td>
          </template>
          <template #contentId="{ item }">
            <td>
              {{ item.contentId }}
            </td>
          </template>
          <template #user.name="{ item }">
            <td>{{ item.user.name }} ({{ item.user.id }})</td>
          </template>
          <template #action="{ item }">
            <td>
              <CButtonToolbar>
                <ShowCommentModal
                  :comment="item"
                  :section="true"
                  :dateRange="dateRange"
                  :page="page"
                  :content="comment"
                  :commentby="commentby"
                  :source="source"
                  :type="type"
                />
                <div v-if="checkAction('all_comment_action')">
                  <CButton
                    v-if="item.statusField.hidden === false"
                    :key="item.id"
                    @click="updateCommentStatus(item._id, 'hide')"
                    target="_blank"
                    color="danger"
                    variant="outline"
                    v-c-tooltip="'Hide/Unhide'"
                    ><font-awesome-icon icon="eye-slash"
                  /></CButton>
                  <CButton
                    v-else
                    :key="item.id"
                    @click="updateCommentStatus(item._id, 'unhide')"
                    target="_blank"
                    color="danger"
                    variant="outline"
                    v-c-tooltip="'Hide/Unhide'"
                    ><font-awesome-icon icon="eye"
                  /></CButton>
                  <CButton
                    v-if="item.statusField.pinned === false"
                    :key="item.id"
                    @click="updateCommentStatus(item._id, 'pin')"
                    target="_blank"
                    color="danger"
                    variant="outline"
                    v-c-tooltip="'Pin/Unpin'"
                    ><font-awesome-icon icon="thumbtack"
                  /></CButton>
                  <CButton
                    v-else
                    :key="item.id"
                    @click="updateCommentStatus(item._id, 'unpin')"
                    target="_blank"
                    color="danger"
                    variant="outline"
                    v-c-tooltip="'Pin/Unpin'"
                    ><font-awesome-icon icon="times-circle"
                  /></CButton>
                </div>
              </CButtonToolbar>
            </td>
          </template>
        </CDataTable>
        <CRow>
          <CCol>
            <CButton
              color="primary"
              class="m-2 float-left"
              :disabled="prevPage === null"
              @click="changeNewPage(prevPage, false)"
            >
              Previous Page
            </CButton>
          </CCol>
          <CCol class="align-middle text-center h-100">
            <b
              ><span class="align-middle"
                >Page: {{ pageSettings.page }}</span
              ></b
            >
          </CCol>
          <CCol>
            <CButton
              color="primary"
              class="m-2 float-right"
              :disabled="nextPage === null"
              @click="changeNewPage(nextPage)"
            >
              Next Page
            </CButton>
          </CCol>
        </CRow>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions } from 'vuex';
import DateRangePicker from 'vue2-daterange-picker';
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';
import ShowCommentModal from './ShowCommentModal2';
import commentMixin from '../../mixins/comment';
import _ from 'lodash';
import { parseISO, format } from 'date-fns';
export default {
  name: 'ReportCommentTable',
  mixins: [commentMixin],
  components: { DateRangePicker, ShowCommentModal },
  data() {
    let startDate = new Date();
    let endDate = new Date();
    startDate.setDate(startDate.getDate() - 0);
    endDate.setDate(endDate.getDate() - 0);
    return {
      filters: {
        createdAt: {
          $gt: new Date(new Date(startDate).setHours(0, 0, 0, 0)).toISOString(),
          $lte: new Date(
            new Date(endDate).setHours(23, 59, 59, 999)
          ).toISOString(),
        },
      },
      sort: [{ field: 'createdAt', order: 'desc' }],
      pageSettings: {
        page: 1,
      },
      columnFilterValues: {},
      fields: [
        {
          key: 'createdDate',
          sorter: false,
          filter: false,
          label: 'Commented At',
        },
        { key: 'message', sorter: false, label: 'Comment' },
        { key: 'user.name', sorter: false, label: 'Commented By' },
        { key: 'contentType', sorter: false, label: 'Type' },
        { key: 'contentId', sorter: false, label: 'Source' },
        { key: 'reportStatus', sorter: false, filter: false, label: 'Status' },
        {
          key: 'action',
          sorter: false,
          label: 'Actions',
          filter: false,
          _style: { width: '10px' },
        },
      ],
      typeoptions: [
        { value: '', label: 'All' },
        { value: 'article', label: 'Articles' },
        { value: 'video', label: 'Videos' },
        { value: 'podcast', label: 'Podcasts' },
        { value: 'feed', label: 'Feeds' },
      ],
      dateRange: { startDate, endDate },
      page: 1,
      comment: false,
      commentby: false,
      source: false,
      type: false,
    };
  },
  filters: {
    date_format(val) {
      return format(parseISO(val), 'yyyy-MM-dd hh:mm:ss');
    },
    date(val) {
      return val ? val.toLocaleString() : '';
    },
  },
  computed: {
    ...mapGetters({
      currentFilters: 'getFilters',
      currentPageSettings: 'getPageSettings',
      currentColumnFilters: 'getColumnFilters',
      currentSort: 'getSort',
      nextPage: 'getNextPageUrl',
      prevPage: 'getPrevPageUrl',
      comments: 'getComments',
      loading: 'getReportsLoading',
      random: 'getRandom',
    }),
  },
  created() {
    this.setFilters(this.filters);
    this.setColumnFilters(this.columnFilterValues);
    this.setSort(this.sort);
    this.listComments({ sort: this.currentSort, filters: this.currentFilters });
  },
  methods: {
    ...mapActions({
      listComments: 'getServiceComments',
      updateComment: 'updateServiceComment',
      listCommentsByPage: 'getServiceCommentsByPage',
    }),
    ...mapMutations({
      setFilters: 'SET_FILTERS',
      setPageSettings: 'SET_PAGE_SETTINGS',
      setColumnFilters: 'SET_COLUMN_FILTERS',
      setSort: 'SET_SORT',
    }),
    updateDate(data) {
      this.filters.createdAt = {
        $gt: new Date(
          new Date(data.startDate).setHours(0, 0, 0, 0)
        ).toISOString(),
        $lt: new Date(
          new Date(data.endDate).setHours(23, 59, 59, 999)
        ).toISOString(),
      };
      this.newServiceFilter(this.currentColumnFilters);
    },
    updateType(data) {
      const payload = {
        ...this.currentColumnFilters,
        contentType: data,
      };
      this.columnFilterValues['contentType'] = data;
      this.newServiceFilter(payload);
    },
    debouncedQuery: _.debounce(function (payload) {
      this.newServiceFilter(payload);
    }, 800),
    newServiceFilter(data) {
      this.pageSettings.page = 1;
      this.setPageSettings(this.pageSettings);
      this.mapAsFilters(data);
      this.listComments({
        sort: this.currentSort,
        filters: this.currentFilters,
      });
    },
    mapAsFilters(filterBy) {
      Object.entries(filterBy).forEach(([prop, val]) => {
        this.createFilterQuery(prop, val);
      });
      this.setColumnFilters(this.columnFilterValues);
      this.setFilters(this.filters);
    },
    changeNewPage(url, next = true) {
      if (next) {
        this.pageSettings.page += 1;
      } else {
        this.pageSettings.page -= 1;
      }
      this.listCommentsByPage(url);
    },
    updateCommentStatus(id, action) {
      const payload = {
        hidden: false,
        pinned: false,
      };
      let status = '';
      switch (action) {
        case 'hide':
          payload.hidden = true;
          status = 'hidden';
          break;
        case 'unhide':
          payload.hidden = false;
          status = 'unhidden';
          break;
        case 'pin':
          payload.pinned = true;
          status = 'pinned';
          break;
        case 'unpin':
          payload.pinned = false;
          status = 'unpinned';
          break;
        default:
          break;
      }
      this.$swal({
        icon: 'question',
        title: 'Are you sure?',
        text: `This will ${action} the comment. Proceed?`,
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes',
        cancelButtonText: 'Cancel',
        buttonsStyling: true,
      }).then((isConfirm) => {
        if (isConfirm.value === true) {
          this.updateComment({ id, payload, status });
        }
      });
    },
    createFilterQuery(prop, val) {
      const numeric = /^\d+$/;
      const nameId = /(.+) ?\((.+)\)$/;
      const textRegex = ['message', 'user.name'];
      const eqFilter = ['contentId', 'contentType'];
      if (textRegex.includes(prop)) {
        if (prop === 'user.name') {
          if (val.length > 0) {
            if (val.match(numeric)) {
              this.filters['user.id'] = { $eq: val };
              this.columnFilterValues['user.id'] = val;
              delete this.columnFilterValues['user.name'];
              delete this.filters['user.name'];
            } else if (val.match(nameId)) {
              const name = val.replace(nameId, '$1').trim();
              const id = val.replace(nameId, '$2');
              this.filters['user.name'] = { $regex: name, $options: 'i' };
              this.filters['user.id'] = { $eq: id };
              this.columnFilterValues['user.name'] = name;
              this.columnFilterValues['user.id'] = id;
            } else {
              this.filters[prop] = { $regex: val, $options: 'i' };
              this.columnFilterValues[prop] = val;
              delete this.filters['user.id'];
              delete this.columnFilterValues['user.id'];
            }
          } else {
            delete this.filters['user.id'];
            delete this.filters['user.name'];
            delete this.columnFilterValues['user.id'];
            delete this.columnFilterValues['user.name'];
          }
        } else {
          if (val.length > 0) {
            this.filters[prop] = { $regex: val, $options: 'i' };
            this.columnFilterValues[prop] = val;
          } else {
            delete this.filters[prop];
            delete this.columnFilterValues[prop];
          }
        }
      }
      if (eqFilter.includes(prop)) {
        if (val.length > 0) {
          this.filters[prop] = { $eq: val };
          this.columnFilterValues[prop] = val;
        } else {
          delete this.filters[prop];
          delete this.columnFilterValues[prop];
        }
      }
    },
  },
};
</script>

<style scoped>
.slot {
  background-color: #aaa;
  padding: 0.5rem;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-black {
  color: #000;
}
</style>
