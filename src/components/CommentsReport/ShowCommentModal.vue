<template>
  <div>
    <CButton
      class="float-right"
      variant="outline"
      color="primary"
      @click="show = true"
      v-c-tooltip="'View Comment'"
      ><CIcon name="cil-speech"></CIcon
    ></CButton>
    <CModal
      title="Comment Details"
      color="default"
      size="lg"
      :show.sync="show"
      centered
    >
      <CRow>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Comment By</small><br />
            <strong class="h4">{{ comment.userInfo }}</strong>
          </CCallout>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Comment</small><br />
            <strong
              class="h6"
              v-html="formatMessageByUsername(comment.comment)"
            ></strong>
          </CCallout>
        </CCol>
      </CRow>
      <CRow v-if="isAI">
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Tones</small><br />
            <strong class="h6" v-html="formatTones(aiData)"></strong>
          </CCallout>
        </CCol>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Approval Rating</small><br />
            <strong class="h6" v-html="formatApproval(aiData)"></strong>
          </CCallout>
        </CCol>
      </CRow>
      <CRow v-if="isAI">
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Reason</small><br />
            <strong class="h6" v-html="formatReason(aiData)"></strong>
          </CCallout>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CDataTable
            :sorter-value="{ column: 'reportedAt', asc: false }"
            :items="comment.reportedDataHistory"
            :fields="fields"
            hover
            sorter
          >
            <template #reportedAt="{ item }">
              <td>
                {{ item.reportedAt | dateTime }}
              </td>
            </template>
            <template #reportReason="{ item }">
              {{ item.reportReason }}
              <p v-if="item.keywords.length > 0">
                Matched keywords: {{ item.keywords.join(',') }}
              </p>
            </template>
          </CDataTable>
        </CCol>
      </CRow>
      <template slot="footer">
        <div v-if="checkAction('comment_action')">
          <div v-if="showWhichButtons(comment)">
            <CButton
              :id="comment.id"
              @click="updateReportedComment(comment._id, 'hide')"
              target="_blank"
              color="danger"
              variant="outline"
              v-c-tooltip="'Toggle Hide/Unhide'"
              ><font-awesome-icon icon="eye-slash"
            /></CButton>
            <CButton
              :id="comment.id"
              @click="updateReportedComment(comment._id, 'ignore')"
              target="_blank"
              color="warning"
              variant="outline"
              v-c-tooltip="'Ignore'"
              ><font-awesome-icon icon="check"
            /></CButton>
          </div>
          <CButton
            v-else
            :id="comment.id"
            @click="updateReportedComment(comment._id, 'unhide')"
            target="_blank"
            color="danger"
            variant="outline"
            v-c-tooltip="'Toggle Hide/Unhide'"
            ><font-awesome-icon icon="eye"
          /></CButton>
        </div>
      </template>
    </CModal>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import { format } from 'date-fns';
import commentMixin from '../../mixins/comment';
export default {
  name: 'ShowCommentModal',
  mixins: [commentMixin],
  components: '',
  props: {
    comment: {
      type: Object,
      required: true,
    },
    section: {
      type: Boolean,
    },
    dateRange: {
      type: Object,
      required: true,
    },
    isAI: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      fields: [
        { key: 'reportedAt', sorter: true, label: 'Report Date' },
        { key: 'reportReason', sorter: true, label: 'Report Reason' },
      ],
      show: false,
    };
  },
  filters: {
    dateTime(val) {
      return format(new Date(val), 'yyyy-MM-dd HH:mm:ss');
    },
  },
  computed: {
    aiData() {
      return this.comment.reportedDataHistory.find((i) => i.reason === -4);
    },
  },
  methods: {
    ...mapActions({
      listReported: 'getServiceReports',
      markHide: 'hideReported',
      markUnhide: 'unhideReported',
      markIgnore: 'ignoreReported',
    }),
    showWhichButtons(reported) {
      const checkTypes = ['unhidden', 'ignored', '-'];
      const reviewType = reported.reviewType.toLowerCase() ?? '-';
      return checkTypes.includes(reviewType);
    },
    updateReportedComment(comment, status) {
      this.$swal({
        icon: 'question',
        title: 'Are you sure?',
        text: `This will ${status} the comment. Proceed?`,
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes',
        cancelButtonText: 'Cancel',
        buttonsStyling: true,
      }).then(async (isConfirm) => {
        if (isConfirm.value === true) {
          if (status === 'hide') {
            await this.markHide(comment);
          } else if (status === 'unhide') {
            await this.markUnhide(comment);
          } else if (status === 'ignore') {
            await this.markIgnore(comment);
          }
        }
      });
    },
  },
};
</script>

<style scoped></style>
