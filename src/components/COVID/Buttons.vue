<template>
  <div>
    <CCard>
      <CCardHeader>Download Data For Malaysia / Global</CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <CCardBody>COVID Malaysia</CCardBody>
            <CButton
              @click="downloadData('msia', 'en')"
              style="margin: 5px"
              color="success"
              >EN</CButton
            >
            <CButton
              @click="downloadData('msia', 'ms')"
              style="margin: 5px"
              color="success"
              >MS</CButton
            >
            <CButton
              @click="downloadData('msia', 'zh')"
              style="margin: 5px"
              color="success"
              >ZH</CButton
            >
          </CCol>

          <CCol>
            <CCardBody>COVID Global</CCardBody>
            <CButton
              @click="downloadData('glob', 'en')"
              style="margin: 5px"
              color="success"
              >EN</CButton
            >
            <CButton
              @click="downloadData('glob', 'ms')"
              style="margin: 5px"
              color="success"
              >MS</CButton
            >
            <CButton
              @click="downloadData('glob', 'zh')"
              style="margin: 5px"
              color="success"
              >ZH</CButton
            >
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
export default {
  name: 'VerifyButtons',
  methods: {
    downloadData(region, language) {
      this.$store.dispatch('downloadData', { region, language });
    },
  },
};
</script>

<style scoped></style>
