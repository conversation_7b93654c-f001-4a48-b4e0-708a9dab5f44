<template>
  <div>
    <CCard>
      <CCardHeader>IOS PUSH NOTIFICATIONS</CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <CInput v-model="ios_en.uniqueID" label="COVID Article" />
            <CInput v-model="ios_en.language" label="Language" />
            <CInput v-model="ios_en.platform" label="Platform" />
            <CInput v-model="ios_en.ct" label="Custom Title" />
            <CInput v-model="ios_en.cm" label="Custom Message" />
            <CButton @click="pushIOS(ios_en)" color="success">Push</CButton>
          </CCol>
          <CCol>
            <CInput v-model="ios_ms.uniqueID" label="COVID Article" />
            <CInput v-model="ios_ms.language" label="Language" />
            <CInput v-model="ios_ms.platform" label="Platform" />
            <CInput v-model="ios_ms.ct" label="Custom Title" />
            <CInput v-model="ios_ms.cm" label="Custom Message" />
            <CButton @click="pushIOS(ios_ms)" color="success">Push</CButton>
          </CCol>
          <CCol>
            <CInput v-model="ios_zh.uniqueID" label="COVID Article" />
            <CInput v-model="ios_zh.language" label="Language" />
            <CInput v-model="ios_zh.platform" label="Platform" />
            <CInput v-model="ios_zh.ct" label="Custom Title" />
            <CInput v-model="ios_zh.cm" label="Custom Message" />
            <CButton @click="pushIOS(ios_zh)" color="success">Push</CButton>
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import axios from 'axios';
import * as headers from '@/helpers/headers';
import Bugsnag from '@bugsnag/js';
export default {
  name: 'IOSPush',
  data() {
    return {
      ios_en: {
        language: 'en',
        ct: '',
        cm: '',
        uniqueID: 'covid19__en',
        platform: 'ios',
      },
      ios_ms: {
        language: 'ms',
        ct: '',
        cm: '',
        uniqueID: 'covid19__ms',
        platform: 'ios',
      },
      ios_zh: {
        language: 'zh',
        ct: '',
        cm: '',
        uniqueID: 'covid19__zh',
        platform: 'ios',
      },
      config: {
        headers: {
          hash: process.env.VUE_APP_ADMIN_TOKEN,
        },
      },
    };
  },
  methods: {
    pushIOS(object) {
      let msg = 'This will send the push notification. Continue?';
      if (confirm(msg)) {
        let fileData = new FormData();
        fileData.append('language', object.language);
        fileData.append('ct', object.ct);
        fileData.append('cm', object.cm);
        fileData.append('platform', object.platform);
        fileData.append('uniqueID', object.uniqueID);
        axios
          .post(
            process.env.VUE_APP_SEND_PN,
            fileData,
            headers.createHeaders(
              this.$store.getters.getUserToken,
              process.env.VUE_APP_ADMIN_TOKEN
            )
          )
          .then((res) => {
            alert('Success');
            console.log(res);
          })
          .catch((err) => {
            console.log(err.response.data);
            let message = 'Check console for more info';
            let title = 'Error';
            if (err.response.status == 401) {
              message = 'Unauthorized. Check Console';
              title = 'Unauthorized';
            }
            this.$notify({
              group: 'error',
              title: title,
              type: 'error',
              text: `${err.response.status} : ${message}`,
              duration: -1,
              position: 'bottom right',
              closeOnClick: true,
            });
            console.log(err);
            Bugsnag.notify(err);
          });
      }
    },
  },
};
</script>

<style scoped></style>
