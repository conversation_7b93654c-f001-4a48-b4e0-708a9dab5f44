<template>
  <div>
    <CCard>
      <CCardHeader>SCALE SERVERS</CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <CWidgetIcon
              v-bind:header="
                new_api_loading
                  ? 'Loading...'
                  : new_api_data.status.currentReplicas +
                    '/' +
                    new_api_data.spec.maxReplicas
              "
              text="New API Pods Running"
              color="success"
              :icon-padding="false"
            >
              <CIcon name="cil-speedometer" width="24" />
            </CWidgetIcon>
            <CInput
              v-model="new_api"
              placeholder="e.g : 300"
              label="Scale New API"
              :is-valid="validatorNew"
              :invalid-feedback="`Number must be between 1 to ${new_api_data.spec.maxReplicas}`"
            />
            <CButton color="info" @click="upNewAPI">Scale</CButton> &nbsp;&nbsp;
            <CBadge
              v-if="new_api_state.length > 0"
              :color="getColor(new_api_state)"
              >{{ new_api_state | uppercase }}
            </CBadge>
          </CCol>
          <CCol>
            <CWidgetIcon
              v-bind:header="
                track_api_loading
                  ? 'Loading...'
                  : track_api_data.status.currentReplicas +
                    '/' +
                    track_api_data.spec.maxReplicas
              "
              text="Track API Pods Running"
              color="success"
              :icon-padding="false"
            >
              <CIcon name="cil-speedometer" width="24" />
            </CWidgetIcon>
            <CInput
              v-model="track_api"
              placeholder="e.g : 60"
              label="Scale Track API"
              :is-valid="validatorTrack"
              :invalid-feedback="`Number must be between 1 to ${track_api_data.spec.maxReplicas}`"
            />
            <CButton color="info" @click="upTrackAPI">Scale</CButton>
            &nbsp;&nbsp;
            <CBadge
              v-if="track_api_state.length > 0"
              :color="getColor(track_api_state)"
            >
              {{ track_api_state | uppercase }}
            </CBadge>
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
export default {
  name: 'ScaleServers',
  data() {
    return {
      new_api: '300',
      track_api: '400',
      counterInterval: null,
    };
  },
  filters: {
    uppercase(val) {
      return val.toUpperCase();
    },
  },
  computed: {
    new_api_loading() {
      return this.$store.getters.getNewAPILoading;
    },
    track_api_loading() {
      return this.$store.getters.getTrackAPILoading;
    },
    new_api_data() {
      return this.$store.getters.getNewAPIData;
    },
    track_api_data() {
      return this.$store.getters.getTrackAPIData;
    },
    new_api_state() {
      return this.$store.getters.getNewAPIState;
    },
    track_api_state() {
      return this.$store.getters.getTrackAPIState;
    },
  },
  created() {
    this.getServerStatus();
    this.counterInterval = setInterval(() => this.getServerStatus(), 15000);
  },
  beforeDestroy() {
    clearInterval(this.counterInterval);
  },
  methods: {
    getServerStatus() {
      this.$store.dispatch('newAPI');
      this.$store.dispatch('trackAPI');
    },
    validatorNew(val) {
      return val
        ? val !== '' && val <= this.new_api_data.spec.maxReplicas
        : false;
    },
    validatorTrack(val) {
      return val
        ? val !== '' && val <= this.track_api_data.spec.maxReplicas
        : false;
    },
    getColor(state) {
      return state === 'pass' ? 'success' : 'danger';
    },
    upNewAPI() {
      this.$store.dispatch('scaleNewAPI', this.new_api);
    },
    upTrackAPI() {
      this.$store.dispatch('scaleTrackAPI', this.track_api);
    },
  },
};
</script>

<style scoped></style>
