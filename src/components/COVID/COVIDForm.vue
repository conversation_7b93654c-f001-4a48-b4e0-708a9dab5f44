<template>
  <div>
    <!-- <CRow>
      <CCol>
        <ScaleServers/>
      </CCol>
    </CRow> -->
    <CRow>
      <CCol>
        <CoronaArticle />
      </CCol>
    </CRow>
    <CRow>
      <CCol>
        <CovidPushNotification />
      </CCol>
    </CRow>

    <CRow>
      <CCol>
        <Buttons />
      </CCol>
    </CRow>
  </div>
</template>

<script>
// import ScaleServers from "./ScaleServers";
import CoronaArticle from './CoronaArticle';
import Buttons from './Buttons';
import CovidPushNotification from './PushNotification';

export default {
  name: 'COVIDForm',
  // components: {CoronaArticle, ScaleServers,Buttons, CovidPushNotification},
  components: { CoronaArticle, Buttons, CovidPushNotification },
};
</script>

<style scoped></style>
