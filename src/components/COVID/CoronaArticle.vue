<template>
  <div>
    <CCard>
      <CCardHeader>UPDATE COVID ARTICLE</CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <CInput v-model="corona.dateToUse" type="date" label="Date" />
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CInput
              v-model="corona.todayConfirmed"
              label="Today Confirmed"
              class="required"
            />
          </CCol>
          <CCol>
            <CInput
              v-model="corona.totalConfirmed"
              label="Total Confirmed"
              class="required"
            />
          </CCol>
        </CRow>

        <CRow>
          <CCol>
            <CInput
              v-model="corona.todayRecovered"
              label="Today Recovered"
              class="required"
            />
          </CCol>
          <CCol>
            <CInput
              v-model="corona.totalRecovered"
              label="Total Recovered"
              class="required"
            />
          </CCol>
        </CRow>

        <CRow>
          <CCol>
            <CInput
              v-model="corona.todayDeath"
              label="Today Death"
              class="required"
            />
          </CCol>
          <CCol>
            <CInput
              v-model="corona.totalDeath"
              label="Total Death"
              class="required"
            />
          </CCol>
        </CRow>

        <CRow>
          <CCol>
            <CInput
              v-model="corona.todayActive"
              label="Today Active"
              class="required"
            />
          </CCol>
          <CCol>
            <CInput
              v-model="corona.todayBID"
              label="Today BID"
              class="required"
            />
          </CCol>
        </CRow>

        <CRow>
          <CCol>
            <CInput v-model="corona.img1" label="1st Image" />
          </CCol>
          <CCol>
            <CInput v-model="corona.img2" label="2nd Image" />
          </CCol>
        </CRow>

        <CRow>
          <CCol>
            <CInput v-model="corona.img3" label="3rd Image" />
          </CCol>
          <CCol>
            <CInput v-model="corona.img4" label="4th Image" />
          </CCol>
        </CRow>

        <CRow class="mb-4">
          <CCol>
            <CTextarea
              v-model="corona.enInput"
              label="EN Article Input"
              :class="['mb-1', { 'textarea-inactive': !customInput.en }]"
            />
            <CInputCheckbox
              :checked="customInput.en"
              @update:checked="customInput.en = !customInput.en"
              label="Include in article"
            />
          </CCol>
          <CCol>
            <CTextarea
              v-model="corona.zhInput"
              label="ZH Article Input"
              :class="['mb-1', { 'textarea-inactive': !customInput.zh }]"
            />
            <CInputCheckbox
              :checked="customInput.zh"
              @update:checked="customInput.zh = !customInput.zh"
              label="Include in article"
            />
          </CCol>
        </CRow>

        <CRow>
          <CCol>
            <CTextarea
              v-model="corona.msInput"
              label="MS Article Input"
              :class="['mb-1', { 'textarea-inactive': !customInput.ms }]"
            />
            <CInputCheckbox
              :checked="customInput.ms"
              @update:checked="customInput.ms = !customInput.ms"
              label="Include in article"
            />
          </CCol>
          <CCol></CCol>
        </CRow>
        <CRow>
          <CCol class="d-flex justify-content-end flex-row align-content-end">
            <div class="mt-2 mr-2">
              <CInputCheckbox
                :checked="corona.shouldUpdatePublishedDate"
                @update:checked="
                  corona.shouldUpdatePublishedDate =
                    !corona.shouldUpdatePublishedDate
                "
                label="Update article published time"
              />
            </div>
            <CButton @click="updateCoronaArticle" color="success"
              >Update Article</CButton
            >
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import Bugsnag from '@bugsnag/js';
import axios from 'axios';
import * as headers from '@/helpers/headers';
import format from 'date-fns/format';
import subDays from 'date-fns/subDays';
export default {
  name: 'CoronaArticle',
  data() {
    return {
      corona: {
        dateToUse: '',
        totalConfirmed: '',
        todayConfirmed: '',
        totalRecovered: '',
        todayRecovered: '',
        totalDeath: '',
        todayDeath: '',
        todayActive: '',
        todayBID: '',
        enInput: '',
        zhInput: '',
        msInput: '',
        img1: '',
        img2: '',
        img3: '',
        img4: '',
        shouldUpdatePublishedDate: false,
      },
      validation: {
        dateToUse: { required: true },
        totalConfirmed: { required: true },
        todayConfirmed: { required: true },
        totalRecovered: { required: true },
        todayRecovered: { required: true },
        totalDeath: { required: true },
        todayDeath: { required: true },
        todayActive: { required: true },
        todayBID: { required: true },
        enInput: { required: false },
        zhInput: { required: false },
        msInput: { required: false },
        img1: { required: false },
        img2: { required: false },
        img3: { required: false },
        img4: { required: false },
      },
      config: {
        headers: {
          hash: process.env.VUE_APP_ADMIN_TOKEN,
        },
      },
      customInput: {
        en: false,
        zh: false,
        ms: false,
      },
    };
  },
  mounted() {
    this.getData();
  },
  computed: {
    covidImagesHistory() {
      return this.$store.state.covid.covid_images_history;
    },
  },
  watch: {
    covidImagesHistory(val) {
      this.$nextTick(() => {
        this.corona = {
          ...this.corona,
          ...val,
        };
      });
    },
  },
  methods: {
    async updateCoronaArticle() {
      let msg = `Article published date ${!this.corona.shouldUpdatePublishedDate ? '<b class="text-danger">will NOT be</b>' : '<b class="text-success">will be</b>'} updated, confirm?`;
      if (this.validateValue(this.corona)) {
        const { isConfirmed } = await this.$swal({
          title: 'Please review',
          html: msg,
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Confirm',
          cancelButtonText: 'Cancel',
          buttonsStyling: true,
        });

        if (isConfirmed) {
          let fileData = new FormData();
          const draft = {
            enInput: this.corona.enInput,
            zhInput: this.corona.zhInput,
            msInput: this.corona.msInput,
          };
          fileData.append('dateToUse', this.corona.dateToUse);
          fileData.append('totalConfirmed', this.corona.totalConfirmed);
          fileData.append('todayConfirmed', this.corona.todayConfirmed);
          fileData.append('totalRecovered', this.corona.totalRecovered);
          fileData.append('todayRecovered', this.corona.todayRecovered);
          fileData.append('totalDeath', this.corona.totalDeath);
          fileData.append('todayDeath', this.corona.todayDeath);
          fileData.append('todayActive', this.corona.todayActive);
          fileData.append('todayBID', this.corona.todayBID);
          fileData.append(
            'enInput',
            this.customInput.en ? this.corona.enInput : ''
          );
          fileData.append(
            'zhInput',
            this.customInput.zh ? this.corona.zhInput : ''
          );
          fileData.append(
            'msInput',
            this.customInput.ms ? this.corona.msInput : ''
          );
          fileData.append('img1', this.corona.img1);
          fileData.append('img2', this.corona.img2);
          fileData.append('img3', this.corona.img3);
          fileData.append('img4', this.corona.img4);
          fileData.append('customInputDraft', JSON.stringify(draft));
          fileData.append(
            'shouldUpdatePublishedDate',
            this.corona.shouldUpdatePublishedDate
          );
          axios
            .post(
              process.env.VUE_APP_UPDATE_CORONA_ARTICLE,
              fileData,
              headers.createHeaders(
                this.$store.getters.getUserToken,
                process.env.VUE_APP_ADMIN_TOKEN
              )
            )
            .then((res) => {
              console.log(res);
              alert('Article successfully updated!');
            })
            .catch((error) => {
              console.log(error.response.data);
              let message = 'Check console for more info';
              let title = 'Error';
              if (error.response.status == 401) {
                message = 'Unauthorized. Check Console';
                title = 'Unauthorized';
              }
              this.$notify({
                group: 'error',
                title: title,
                type: 'error',
                text: `${error.response.status} : ${message}`,
                duration: -1,
                position: 'bottom right',
                closeOnClick: true,
              });
              console.log(error);
              Bugsnag.notify(error);
            });
        }
      }
    },
    validateValue(object) {
      let pass = false;
      const validation = this.validation;
      pass = Object.entries(object).every(function ([key, val]) {
        const hasFailed = validation[key]?.required == true && val == '';
        return !hasFailed;
      });
      if (!pass) {
        alert('Please fill up all required fields');
      }
      return pass;
    },
    async getData() {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      axios
        .get(
          `${process.env.VUE_APP_GET_COVID_ARTICLE_HISTORY}?type=article&r=${r}`,
          headers.createHeaders(this.$store.getters.getUserToken)
        )
        .then((res) => {
          let data = res.data;
          const customInputDraft = JSON.parse(data.customInputDraft);
          this.customInput.en = !!data.enInput;
          this.customInput.zh = !!data.zhInput;
          this.customInput.ms = !!data.msInput;

          data.enInput = data.enInput || customInputDraft?.enInput || '';
          data.zhInput = data.zhInput || customInputDraft?.zhInput || '';
          data.msInput = data.msInput || customInputDraft?.msInput || '';
          this.corona = { ...this.corona, ...data };
          this.corona.dateToUse = format(subDays(new Date(), 1), 'yyyy-MM-dd');
        })
        .catch((error) => {
          alert('Something went wrong');
          console.log(error);
          Bugsnag.notify(error);
        });
      await this.$store.dispatch('getCovidImagesHistory');
    },
  },
};
</script>

<style>
.required label::after {
  content: '*';
  color: red;
}
.textarea-inactive textarea {
  background: rgba(0, 0, 0, 0.05);
}
</style>
