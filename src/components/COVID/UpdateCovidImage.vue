<template>
  <div>
    <CCard>
      <CCardHeader
        >UPDATE COVID IMAGES
        <font-awesome-icon
          icon="info-circle"
          v-c-tooltip="{
            content: 'Update image without updating published date',
            placement: 'right',
          }"
        />
      </CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <CInput v-model="images.img1" label="1st Image" />
          </CCol>
          <CCol>
            <CInput v-model="images.img2" label="2nd Image" />
          </CCol>
        </CRow>

        <CRow>
          <CCol>
            <CInput v-model="images.img3" label="3rd Image" />
          </CCol>
          <CCol>
            <CInput v-model="images.img4" label="4th Image" />
          </CCol>
        </CRow>
        <CButton class="float-right" @click="submit" color="success"
          >Update Image</CButton
        >
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import Bugsnag from '@bugsnag/js';
import axios from 'axios';
import * as headers from '@/helpers/headers';
export default {
  name: 'UpdateCovidImage',
  data() {
    return {
      images: {
        img1: '',
        img2: '',
        img3: '',
        img4: '',
      },
      config: {
        headers: {
          hash: process.env.VUE_APP_ADMIN_TOKEN,
        },
      },
    };
  },
  mounted() {
    this.getData();
  },
  computed: {
    covidImagesHistory() {
      return this.$store.state.covid.covid_images_history;
    },
  },
  watch: {
    covidImagesHistory(val) {
      this.$nextTick(() => {
        this.images = val;
      });
    },
  },
  methods: {
    submit() {
      let msg = 'This will update Covid article image(s). Continue?';
      if (this.validateValue(this.images)) {
        if (confirm(msg)) {
          let fileData = new FormData();
          fileData.append('img1', this.images.img1 || '');
          fileData.append('img2', this.images.img2 || '');
          fileData.append('img3', this.images.img3 || '');
          fileData.append('img4', this.images.img4 || '');
          axios
            .post(
              process.env.VUE_APP_UPDATE_COVID_ARTICLE_IMAGES,
              fileData,
              headers.createHeaders(
                this.$store.getters.getUserToken,
                process.env.VUE_APP_ADMIN_TOKEN
              )
            )
            .then((res) => {
              console.log(res);
              this.getData();
              alert('Article successfully updated!');
            })
            .catch((error) => {
              console.log(error.response.data);
              // general error
              let message =
                'Failed to fetch image(s), please check if image(s) is accessible and try again';
              let title = 'Error';
              if (error.response.status == 401) {
                message = 'Unauthorized. Please refresh to try again';
                title = 'Unauthorized';
              }
              this.$notify({
                group: 'error',
                title: title,
                type: 'error',
                text: `${error.response.status} : ${message}`,
                duration: -1,
                position: 'bottom right',
                closeOnClick: true,
              });
              console.log(error);
              Bugsnag.notify(error);
            });
        }
      }
    },
    validateValue(object) {
      let pass = false;
      // at least has 1 image
      pass = Object.values(object).some(function (val) {
        return val != '';
      });
      if (!pass) {
        alert('At least 1 image is required');
      }
      return pass;
    },
    async getData() {
      await this.$store.dispatch('getCovidImagesHistory');
    },
  },
};
</script>

<style scoped></style>
