<template>
  <div>
    <CCard>
      <CCardHeader>SEND COVID PNS</CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <CInput
              v-model="en.articleId"
              label="Article ID"
              :disabled="true"
              class="required"
            />
            <CInput
              v-model="en.language"
              label="Language"
              :disabled="true"
              class="required"
            />
            <div class="mb-3">
              <CInput
                v-model="en.title"
                label="PN Title"
                class="required mb-0"
                placeholder="35 w/desc, 120 w/o desc"
              />
              <span
                class="form-text text-muted"
                v-text="en.title.length + ' Characters'"
              />
            </div>
            <div class="mb-3">
              <CTextarea
                v-model="en.body"
                label="PN Desc"
                class="required mb-0"
              />
              <span
                class="form-text text-muted"
                v-text="en.body.length + ' Characters'"
              />
            </div>
            <CButton @click="onPush(en)" color="success">Send</CButton>
          </CCol>
          <CCol>
            <CInput
              v-model="zh.articleId"
              label="Article ID"
              :disabled="true"
              class="required"
            />
            <CInput
              v-model="zh.language"
              label="Language"
              :disabled="true"
              class="required"
            />
            <div class="mb-3">
              <CInput
                v-model="zh.title"
                label="PN Title"
                class="required mb-0"
                placeholder="15 w/desc, 45 w/o desc"
              />
              <span
                class="form-text text-muted"
                v-text="zh.title.length + ' Characters'"
              />
            </div>
            <div class="mb-3">
              <CTextarea
                v-model="zh.body"
                label="PN Desc"
                class="required mb-0"
              />
              <span
                class="form-text text-muted"
                v-text="zh.body.length + ' Characters'"
              />
            </div>
            <CButton @click="onPush(zh)" color="success">Send</CButton>
          </CCol>
          <CCol>
            <CInput
              v-model="ms.articleId"
              label="Article ID"
              :disabled="true"
              class="required"
            />
            <CInput
              v-model="ms.language"
              label="Language"
              :disabled="true"
              class="required"
            />
            <div class="mb-3">
              <CInput
                v-model="ms.title"
                label="PN Title"
                class="required mb-0"
                placeholder="35 w/desc, 120 w/o desc"
              />
              <span
                class="form-text text-muted"
                v-text="ms.title.length + ' Characters'"
              />
            </div>
            <div class="mb-3">
              <CTextarea
                v-model="ms.body"
                label="PN Desc"
                class="required mb-0"
              />
              <span
                class="form-text text-muted"
                v-text="ms.body.length + ' Characters'"
              />
            </div>
            <CButton @click="onPush(ms)" color="success">Send</CButton>
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import Bugsnag from '@bugsnag/js';
import axios from 'axios';
import * as headers from '@/helpers/headers';

export default {
  name: 'CovidPushNotification',
  data() {
    return {
      en: {
        language: 'en',
        title: '',
        body: '',
        articleId: 'covid19__en',
      },
      ms: {
        language: 'ms',
        title: '',
        body: '',
        articleId: 'covid19__ms',
      },
      zh: {
        language: 'zh',
        title: '',
        body: '',
        articleId: 'covid19__zh',
      },
      config: {
        headers: {
          hash: process.env.VUE_APP_ADMIN_TOKEN,
        },
      },
    };
  },
  methods: {
    onPush(object) {
      let msg = 'This will send the push notification. Continue?';
      if (this.validate(object) && confirm(msg)) {
        let postData = { ...object };
        // instant & skipimage by default for covid PN
        postData.instant = 1;
        postData.skipImage = 1;
        postData.postfix = this.generatePostfix();
        axios
          .post(
            process.env.VUE_APP_SEND_PN,
            postData,
            headers.createHeaders(
              this.$store.getters.getUserToken,
              process.env.VUE_APP_ADMIN_TOKEN
            )
          )
          .then(() => {
            alert('Success');
          })
          .catch((error) => {
            console.log(error.response.data);
            let message = 'Check console for more info';
            let title = 'Error';
            if (error.response.status == 401) {
              message = 'Unauthorized. Check Console';
              title = 'Unauthorized';
            }
            this.$notify({
              group: 'error',
              title: title,
              type: 'error',
              text: `${error.response.status} : ${message}`,
              duration: -1,
              position: 'bottom right',
              closeOnClick: true,
            });
            console.log(error);
            Bugsnag.notify(error);
          });
      }
    },
    validate(object) {
      // all fields are required
      const isValid = Object.values(object).every((val) => val);
      if (!isValid) {
        alert('Please fill up all required fields');
      }
      return isValid;
    },
    generatePostfix() {
      let randomChars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      while (!/^(?=.*[A-Z])(?=.*\d).{6,}$/.test(result)) {
        result = '';
        for (let i = 0; i < 6; i++) {
          result += randomChars.charAt(
            Math.floor(Math.random() * randomChars.length)
          );
        }
      }
      return result;
    },
  },
};
</script>
