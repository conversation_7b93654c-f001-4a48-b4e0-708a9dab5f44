<template>
  <div>
    <CRow>
      <CCol>
        <CForm @submit.prevent="sendPodPN">
          <CCard>
            <CCardHeader>Podcast Push Notification</CCardHeader>
            <CCardBody>
              <BasePushNotificationForm :pn="pn" :set="'podcast'" />
              <br /><br />
              <CButton
                block
                color="success"
                variant="outline"
                type="submit"
                :disabled="is_disabled"
                >Send</CButton
              >
            </CCardBody>
          </CCard>
        </CForm>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import BasePushNotificationForm from '@/components/PushNotification/BasePushNotificationForm';

export default {
  name: 'PNPodcast',
  components: { BasePushNotificationForm },
  data() {
    return {
      pn: {
        podcastId: '',
        title: '',
        body: '',
        postfix: '',
        platform: '',
        includeSubLanguage: '0',
        language: ' ',
        forceLang: '',
        allLangs: '',
        pnPeriod: ['1', '2', '3'],
        // skipUserFeedView: '0',
        nonPeriodic: '1',
        // dryMode : '1'
        has_title: '1',
      },
    };
  },
  computed: {
    is_disabled() {
      let disabled = [];
      Object.entries(this.pn).forEach(([prop, val]) => {
        if (prop === 'podcastId' && val === '') {
          disabled.push('podcastId');
        }
        if (prop === 'pnPeriod' && val.length === 0) {
          disabled.push('pnPeriod');
        }
      });
      return disabled.length > 0;
    },
  },
  methods: {
    async sendPodPN() {
      this.$swal
        .fire({
          icon: 'question',
          text: 'This will send a notification with the current settings. Continue?',
        })
        .then(async (res) => {
          if (res.isConfirmed) {
            if (this.pn.has_title === '0') {
              this.pn.title = ' ';
            }
            if (this.pn.language !== '') {
              this.pn.forceLang = this.pn.language;
              this.pn.allLangs = '';
            } else {
              this.pn.allLangs = '1';
              this.pn.forceLang = '';
            }
            let newPn = {};
            Object.entries(this.pn).forEach(([prop, val]) => {
              if (
                val !== '' &&
                val !== '0' &&
                prop !== 'has_title' &&
                prop !== 'language'
              ) {
                newPn[prop] = val;
              }
            });
            newPn.postfix = await this.generatePostfix();
            newPn.pnPeriod = newPn.pnPeriod.sort().join();
            let check = {
              articleId: newPn.podcastId,
              platform: newPn.platform ? newPn.platform : 'all',
              language: newPn.forceLang ? newPn.forceLang : 'all',
              pnPeriod: newPn.pnPeriod,
              type: 'Podcast',
            };
            this.$store
              .dispatch('checkPushHistory', check)
              .then(() => {
                this.callPodPush(newPn);
              })
              .catch((err) => {
                let msg = '<p>' + err.response.data.msg + '</p><p>Re-push?</p>';
                this.$swal
                  .fire({
                    icon: 'warning',
                    html: msg,
                    confirmButtonText: 'Yes',
                  })
                  .then((res) => {
                    if (res.isConfirmed) {
                      this.callPodPush(newPn);
                    }
                  });
                console.log(err);
              });
          }
        });
    },
    callPodPush(pn) {
      this.$store
        .dispatch('sendPodcastPN', pn)
        .then(() => {
          // let log = {
          //   title : pn.title,
          //   body : pn.body,
          //   articleId : pn.podcastId,
          //   platform : pn.platform ? pn.platform : 'all',
          //   language : pn.forceLang ? pn.forceLang : 'all',
          //   pnPeriod : pn.pnPeriod,
          //   matchSubLang : pn.includeSubLanguage,
          //   nonPeriodic : 0,
          //   feed : 0,
          //   matchTopic : 0,
          //   skipReadUser : 0
          // }
          // this.logVidPush(log)
          this.$swal
            .fire({
              icon: 'success',
              text: `Podcast ID ${pn.podcastId} is successfully sent`,
              showCancelButton: false,
            })
            .then(() => {
              this.resetPNSettings();
            });
        })
        .catch((err) => {
          this.$swal.fire({
            icon: 'error',
            text: 'API returns failure. Please retry & do contact tech team if the issue persists',
            showCancelButton: false,
          });
          console.log(err);
        });
    },
    logVidPush(pn) {
      this.$store.dispatch('logPush', pn);
    },
    generatePostfix() {
      let randomChars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      while (!/^(?=.*[A-Z])(?=.*\d).{6,}$/.test(result)) {
        result = '';
        for (let i = 0; i < 6; i++) {
          result += randomChars.charAt(
            Math.floor(Math.random() * randomChars.length)
          );
        }
      }
      return result;
    },
    resetPNSettings() {
      this.pn = {
        podcastId: '',
        title: '',
        body: '',
        postfix: '',
        platform: '',
        includeSubLanguage: '0',
        language: ' ',
        forceLang: '',
        allLangs: '',
        pnPeriod: ['1', '2', '3'],
        nonPeriodic: '1',
        has_title: '1',
      };
    },
  },
};
</script>

<style scoped></style>
