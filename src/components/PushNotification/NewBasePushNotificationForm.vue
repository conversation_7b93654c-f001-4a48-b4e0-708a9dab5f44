<template>
  <div>
    <OverlayLoader v-if="pn_loading" />
    <div>
      <CInput
        v-model="pn.articleId"
        label="Article ID"
        :is-valid="hasValue"
        invalid-feedback="Article ID is required"
        @input="onArticleIdInput"
      />

      <CButton
        v-if="titleSuggestions && titleSuggestions.length > 0 && descriptionSuggestions.length > 0"
        color="info"
        shape="pill"
        class="refresh-button"
        size="sm"
        @click="getPnTitleAndDescriptionSuggestions"
        :disabled="pn_loading"
      >
        <CIcon name="cil-sync" /> Refresh Suggestions
      </CButton>
      <br />
      <CRow>
        <CCol>PN Title</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in yesno_options"
            :key="optionIndex"
          >
            <input
              type="radio"
              class="form-check-input"
              :id="option.value + optionIndex + 'title'"
              :value="option.value"
              v-model="pn.usePnTitle"
            />
            <label
              :for="option.value + optionIndex + 'title'"
              class="form-check-label"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
      <br />
      <div class="form-group" v-if="pn.usePnTitle === 1">
        <input
          v-model="pn.customTitle"
          id="customTitle"
          class="form-control"
          :class="{ 'is-invalid': !isTitleValid }"
          :maxlength="title_max"
          placeholder="EN: 35 w/desc, 120 w/o desc; ZH 15 w/desc, 45 w/o desc"
        />
        <span id="characterLimit" class="form-text text-muted">
          {{ pn.customTitle.length }} / {{ title_max }} Characters
        </span>
        <div v-if="!isTitleValid" class="invalid-feedback">
          Title is invalid. Backquote character("`") is not allowed.
        </div>
        <div class="title-suggestions mt-2" v-if="titleSuggestions">
          <div class="d-flex flex-wrap">
            <CBadge
              v-for="(suggestion, index) in titleSuggestions"
              :key="'title-' + index"
              color="info"
              class="suggestion-badge mr-2 mb-2"
              @click="fillTitle(suggestion)"
            >
              {{ suggestion }}
            </CBadge>
          </div>
        </div>
      </div>
      <div class="form-group">
        <label for="description">PN Description</label>
        <textarea
          :maxlength="body_max"
          v-model="pn.customBody"
          id="description"
          class="form-control"
          :class="{ 'is-invalid': !isDescriptionValid }"
        />
        <span
          id="bodyLimit"
          class="form-text text-muted"
          v-text="pn.customBody.length + ' Characters'"
        />
        <div v-if="!isDescriptionValid" class="invalid-feedback">
          Description is invalid. Backquote character("`") is not allowed.
        </div>
        <div class="description-suggestions mt-2" v-if="descriptionSuggestions">
          <div class="d-flex flex-wrap">
            <CBadge
              v-for="(suggestion, index) in descriptionSuggestions"
              :key="'desc-' + index"
              color="info"
              class="suggestion-badge mr-2 mb-2"
              @click="fillDescription(suggestion)"
            >
              {{ suggestion }}
            </CBadge>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import OverlayLoader from '../views/OverlayLoader';
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';

export default {
  name: 'NewBasePushNotificationForm',
  components: { OverlayLoader },
  props: {
    test: {
      type: Boolean,
      default: false,
    },
    pn: {
      type: Object,
      required: true,
    },
    set: {
      type: String,
    },
  },
  data() {
    return {
      base_en_max: 35,
      en_title_max: 120,
      body_max: 255,
      suggestionTimer: null,
      yesno_options: [
        { value: 1, label: 'Yes' },
        { value: 0, label: 'No' },
      ],
    };
  },
  mounted() {
    this.$store.dispatch('listFeed');
    this.resetSuggestions();
  },
  computed: {
    pn_loading() {
      return this.$store.getters.getPNLoading;
    },
    article_loading() {
      return this.$store.getters.getArticleLoading;
    },
    feed_option_computed() {
      return this.$store.getters.getFeedOptions;
    },
    title_max() {
      return this.pn.customBody.length > 0
        ? this.base_en_max
        : this.en_title_max;
    },
    isTitleValid() {
      return !this.pn.customTitle.includes('`');
    },
    isDescriptionValid() {
      return !this.pn.customBody.includes('`');
    },
    titleSuggestions() {
      return this.$store.getters.getTitleSuggestions;
    },
    descriptionSuggestions() {
      return this.$store.getters.getDescriptionSuggestions;
    },
  },
  methods: {
    resetSuggestions() {
      this.$store.commit('SET_TITLE_SUGGESTIONS', []);
      this.$store.commit('SET_DESCRIPTION_SUGGESTIONS', []);

      if (this.suggestionTimer) {
        clearTimeout(this.suggestionTimer);
        this.suggestionTimer = null;
      }
    },
    getArticle() {
      this.$store
        .dispatch('getArticleData', this.pn.articleId)
        .then((res) => {
          let article = res.data;
          this.pn.customTitle = article.title;
          this.pn.customBody = article.description;
        })
        .catch((err) => {
          alert(err.data.message);
        });
    },
    hasValue(val) {
      return val ? val.length > 0 : false;
    },
    fillTitle(suggestion) {
      this.pn.customTitle = suggestion;
    },
    fillDescription(suggestion) {
      this.pn.customBody = suggestion;
    },
    onArticleIdInput() {
      if (this.suggestionTimer) {
        clearTimeout(this.suggestionTimer);
      }
      if (this.pn.articleId.length === 0) {
        this.resetSuggestions();
        return;
      }
      this.suggestionTimer = setTimeout(() => {
        if (this.pn.articleId.length === 0) {
          this.$store.commit('SET_TITLE_SUGGESTIONS', []);
          this.$store.commit('SET_DESCRIPTION_SUGGESTIONS', []);
          return;
        }
        this.getPnTitleAndDescriptionSuggestions();
      }, 1000);
    },
    getPnTitleAndDescriptionSuggestions() {
      this.$store.dispatch(
        'getTitleAndDescriptionSuggestions',
        this.pn.articleId
      );
    },
  },
};
</script>

<style scoped>
@media (max-width: 750px) {
  .col {
    display: flex;
    justify-content: space-between;
  }
  .multi.col {
    flex-direction: column;
  }
  .choices {
    margin-bottom: 8px;
  }
}

.suggestion-badge {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.suggestion-badge:hover {
  opacity: 0.8;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.refresh-button {
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: bold;
}
</style>
