<template>
  <div>
    <CButton
      variant="outline"
      color="warning"
      v-c-tooltip="'Edit'"
      @click="show = !show"
      ><CIcon name="cil-pencil"></CIcon
    ></CButton>
    <CModal
      :show.sync="show"
      title="Edit Scheduled PN"
      centered
      size="lg"
      :close-on-backdrop="false"
      @update:show="reset"
    >
      <BaseScheduledPNForm :pn="scheduled_pn" />
      <template slot="footer">
        <CButton color="success" variant="outline" @click="editPN"
          >Submit</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
import BaseScheduledPNForm from '@/components/PushNotification/BaseScheduledPNForm';
export default {
  name: 'EditScheduledPNModal',
  components: { BaseScheduledPNForm },
  props: {
    pn: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      show: false,
      scheduled_pn: JSON.parse(JSON.stringify(this.pn)),
    };
  },
  methods: {
    editPN() {
      this.$swal
        .fire({
          icon: 'question',
          text: 'This will update the scheduled PN',
        })
        .then((res) => {
          if (res.isConfirmed) {
            let data = JSON.parse(JSON.stringify(this.scheduled_pn));
            let passObj = {};
            let payload = {};
            let exclude = ['has_title'];
            Object.entries(data).forEach(([prop, val]) => {
              if (prop === 'payload') {
                Object.entries(data.payload).forEach(([prop, val]) => {
                  if (val !== '' && val !== '0' && !exclude.includes(prop)) {
                    payload[prop] = val;
                  }
                });
              }
              passObj[prop] = val;
            });
            passObj.payload = payload;
            passObj.payload.pnPeriod = passObj.payload.pnPeriod
              .sort()
              .join(',');
            this.$store
              .dispatch('updateScheduledPN', passObj)
              .then(() => {
                this.show = !this.show;
              })
              .catch(() => {});
          }
        });
    },
    reset() {
      this.scheduled_pn = JSON.parse(JSON.stringify(this.pn));
    },
  },
};
</script>

<style scoped></style>
