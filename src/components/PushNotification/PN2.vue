<template>
  <div>
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader>Article Push Notification</CCardHeader>
          <CCardBody>
            <CTabs v-model="activeTab">
              <CTab title="Manual PN" :active="activeTab === 0">
                <CCardBody>
                  <CForm @submit.prevent="prePushCheck">
                    <NewBasePushNotificationForm :pn="pn" :set="'article'" />
                    <br /><br />
                    <CButton
                      block
                      color="warning"
                      variant="outline"
                      type="button"
                      @click="resetPNSettings"
                      >Reset</CButton
                    >
                    <CButton
                      block
                      color="success"
                      variant="outline"
                      type="submit"
                      :disabled="is_disabled"
                      >Send</CButton
                    >
                  </CForm>
                </CCardBody>
              </CTab>
              <CTab title="AI PN Configurations" :active="activeTab === 1">
                <CCardBody>
                  <ListAiPnConfigurations />
                </CCardBody>
              </CTab>
            </CTabs>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import NewBasePushNotificationForm from './NewBasePushNotificationForm';
import ListAiPnConfigurations from './ListAiPushNotificationConfigurations';
import { getPushText } from '@/helpers/pn';
import axios from 'axios';
import * as headers from '@/helpers/headers';

export default {
  name: 'PN2',
  components: { NewBasePushNotificationForm, ListAiPnConfigurations },
  created() {
    if (this.$route.query.tab === 'ai-config') {
      this.activeTab = 1;
    }
  },
  data() {
    return {
      activeTab: 0,
      pn: {
        articleId: '',
        customTitle: '',
        customBody: '',
        usePnTitle: 1,
      },
    };
  },
  computed: {
    is_disabled() {
      let errors = [];
      Object.entries(this.pn).forEach(([prop, val]) => {
        if (prop === 'articleId' && val === '') {
          errors.push('articleID');
        }
        if (prop === 'profileId' && val === '') {
          errors.push('profileID');
        }
        if (prop === 'customTitle' && val.includes('`')) {
          errors.push('customTitle');
        }
        if (prop === 'customBody' && val.includes('`')) {
          errors.push('customBody');
        }
      });
      return errors.length > 0;
    },
  },
  methods: {
    async prePushCheck() {
      try {
        const { data } = await axios.get(
          process.env.VUE_APP_GET_RESOURCE_BY_UNIQUE_ID + this.pn.articleId,
          headers.createHeaders(
            this.$store.getters.getUserToken,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        );

        if (data.isTranslated === true) {
          await this.sendPush();
          return;
        }
      } catch (error) {
        this.$swal.fire({
          icon: 'error',
          text: 'Something went wrong in the article ID!',
        });
        return;
      }

      await this.$swal
        .fire({
          html: `<b>${this.pn.articleId}</b> is not translated! <br>Do you want to proceed?`,
          icon: 'question',
          showCancelButton: true,
          confirmButtonText: 'Proceed',
        })
        .then((result) => {
          if (result.isConfirmed === true) {
            this.sendPush();
          }
        });
    },
    async sendPush() {
      this.$swal
        .fire({
          icon: 'question',
          text: 'This will send a notification with the current settings. Continue?',
          confirmButtonText: 'Yes',
        })
        .then(async (res) => {
          if (res.isConfirmed) {
            if (this.pn.usePnTitle === 0) {
              this.pn.customTitle = ' ';
            }
            this.callPush(this.pn);
          }
        });
    },
    generatePostfix() {
      let randomChars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      while (!/^(?=.*[A-Z])(?=.*\d).{6,}$/.test(result)) {
        result = '';
        for (let i = 0; i < 6; i++) {
          result += randomChars.charAt(
            Math.floor(Math.random() * randomChars.length)
          );
        }
      }
      return result;
    },
    callPush(pn) {
      this.$store
        .dispatch('sendPush2', {
          article_id: this.pn.articleId.trim(),
          custom_title: this.pn.customTitle.trim(),
          custom_body: this.pn.customBody.trim(),
          use_pn_title: this.pn.usePnTitle,
          sent_by: this.$store.getters.getUser.name,
        })
        .then(() => {
          const text = getPushText(pn);
          this.$swal
            .fire({
              icon: 'success',
              text: text,
              showCancelButton: false,
            })
            .then(() => {
              this.resetPNSettings();
            });
        })
        .catch((err) => {
          let text =
            'API returns failure. Please retry & do contact tech team if the issue persists';
          if (err.response.data.error !== '') {
            text = err.response.data.error;
          }
          this.$swal.fire({
            icon: 'error',
            text: text,
            showCancelButton: false,
          });
        });
    },
    savePush(pn) {
      this.$store.dispatch('logPush', pn);
    },
    resetPNSettings() {
      this.pn = {
        articleId: '',
        customTitle: '',
        customBody: '',
        usePnTitle: 1,
      };
      this.$store.commit('SET_TITLE_SUGGESTIONS', []);
      this.$store.commit('SET_DESCRIPTION_SUGGESTIONS', []);
    },
    mapAutoToModel(item) {
      Object.entries(item).forEach(([prop, val]) => {
        this.pn[prop] = val;
      });
    },
  },
};
</script>
