<template>
  <div>
    <div v-show="checkAction('pn_auto')">
      <CButton color="warning" shape="pill" @click="sendAutoPN('interest')"
        >Auto PN-Interest</CButton
      >
      <CButton color="warning" shape="pill" @click="sendAutoPN('views')"
        >Auto PN-Views</CButton
      >
    </div>
  </div>
</template>

<script>
export default {
  name: 'PNAuto',
  methods: {
    async sendAutoPN(type) {
      let pn = await this.createPNObj(type);
      this.$emit('map-auto', pn);
      // let pn_type = type === 'interest' ? 'I' : 'V'
      // let textType = pn_type + type.slice(1)
      // this.$swal.fire({
      //   icon: 'question',
      //   text: `Set Auto PN-${textType}?`,
      //   confirmButtonText : 'Yes'
      // })
      // .then(async (res) => {
      // let pn_type = type === 'interest' ? 'I' : 'V'
      // let textType = pn_type + type.slice(1)
      // if(res.isConfirmed){
      //   let pn = await this.createPNObj(type)
      // this.$store.dispatch('checkPeriod')
      //   .then(async () => {
      //     this.$store.dispatch('triggerAutoPN', pn)
      //     .then(() =>{
      //       this.$swal.fire({
      //         icon : 'success',
      //         text : `Auto PN-${textType} is successfully triggered`,
      //         showCancelButton : false
      //       })
      //     })
      //     .catch(() => {
      //       this.$swal.fire({
      //         icon : 'warning',
      //         text : `Something went wrong in triggering Auto PN-${textType}`,
      //         showCancelButton : false
      //       })
      //     })
      // })
      // .catch(() => {
      //   this.$swal.fire({
      //     icon : 'warning',
      //     title : 'Failed',
      //     text : `Auto PN Was Sent Before In This Period`,
      //     showCancelButton : false
      //   })
      // })
      // }
      // })
    },
    async createPNObj(type) {
      let pn_type = type === 'interest' ? 'I' : 'V';
      let date = new Date();
      let yy = new Intl.DateTimeFormat('en', { year: '2-digit' }).format(date);
      let mm = new Intl.DateTimeFormat('en', { month: '2-digit' }).format(date);
      let dd = new Intl.DateTimeFormat('en', { day: '2-digit' }).format(date);
      let period = await this.getPeriod();
      return {
        articleId: `AT${pn_type}_${yy}_${mm}_${dd}_p_${period}`,
        platform: '',
        language: '',
        pnPeriod: [],
        feed: 0,
        matchTopic: 0,
        matchSubLang: 0,
        skipReadUser: 0,
        replace_period: 0,
        is_instant: 0,
        nonActiveUsers: 0,
        matchInterest: 0,
        pn_type: type,
      };
    },
    getPeriod() {
      let date = new Date();
      let hh = date.getHours();
      if (hh >= 8 && hh <= 11) {
        return 1;
      } else if (hh >= 12 && hh <= 14) {
        return 2;
      } else if (hh >= 15 && hh <= 6) {
        return 3;
      } else {
        return 4;
      }
    },
  },
};
</script>

<style scoped></style>
