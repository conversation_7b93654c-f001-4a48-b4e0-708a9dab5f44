<template>
  <CCard>
    <CCardHeader>Configuration List</CCardHeader>
    <CCardBody>
      <CRow>
        <CCol>
          <router-link to="/ai-pn-configuration/upsert">
            <CButton
              class="float-right mb-2"
              variant="outline"
              color="success"
              shape="pill"
            >
              Add Configuration
            </CButton>
          </router-link>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CDataTable
            :loading="$store.getters.getPnLoading"
            :key="mappedConfigurationList.id"
            small
            :sorter-value="sorters"
            :fields="list_fields"
            :items="mappedConfigurationList"
          >
            <template #name="{ item }">
              <td>
                {{ item.name || '-' }}
              </td>
            </template>
            <template #threshold="{ item }">
              <td>
                {{ getThresholdValue(item.criteria_value) }}
              </td>
            </template>
            <template #keywords="{ item }">
              <td>
                <div v-if="item.criteria_type === 'AI_PN_KEYWORDS'">
                  <div
                    v-if="hasKeywords(item.criteria_value)"
                    class="d-flex flex-wrap"
                  >
                    <CBadge
                      v-for="(keyword, index) in getKeywordsArray(
                        item.criteria_value
                      )"
                      :key="index"
                      color="success"
                      class="mr-1 mb-1 badge-larger"
                    >
                      {{ keyword }}
                    </CBadge>
                  </div>
                  <span v-else>-</span>
                </div>
                <span v-else>-</span>
              </td>
            </template>
            <template #excluded_keywords="{ item }">
              <td>
                <div v-if="item.criteria_type === 'AI_PN_KEYWORDS'">
                  <div
                    v-if="hasExcludedKeywords(item.criteria_value)"
                    class="d-flex flex-wrap"
                  >
                    <CBadge
                      v-for="(
                        excludedKeyword, index
                      ) in getExcludedKeywordsArray(item.criteria_value)"
                      :key="index"
                      color="success"
                      class="mr-1 mb-1 badge-larger"
                    >
                      {{ excludedKeyword }}
                    </CBadge>
                  </div>
                  <span v-else>-</span>
                </div>
                <span v-else>-</span>
              </td>
            </template>
            <template #included_publisher_names="{ item }">
              <td>
                <div v-if="hasIncludedPublishers(item.criteria_value)">
                  <div class="d-flex flex-wrap">
                    <CBadge
                      v-for="(
                        includedPublisher, index
                      ) in getIncludedPublishersArray(item.criteria_value)"
                      :key="index"
                      color="success"
                      class="mr-1 mb-1 badge-larger"
                    >
                      {{ includedPublisher }}
                    </CBadge>
                  </div>
                </div>
                <span v-else>-</span>
              </td>
            </template>
            <template #is_enabled="{ item }">
              <td>
                {{
                  item.is_enabled === 1 || item.is_enabled === true
                    ? 'Yes'
                    : 'No'
                }}
              </td>
            </template>
            <template #send_pn_enabled="{ item }">
              <td>
                {{
                  item.send_pn_enabled === 1 || item.send_pn_enabled === true
                    ? 'Yes'
                    : 'No'
                }}
              </td>
            </template>
            <template #updated_by="{ item }">
              <td>
                {{ item.updated_by || '-' }}
              </td>
            </template>
            <template #show_details="{ item }">
              <td>
                <CButtonToolbar>
                  <router-link :to="'ai-pn-configuration/upsert/' + item.id">
                    <CButton
                      color="warning"
                      v-c-tooltip="'Edit Configuration'"
                      variant="outline"
                      ><CIcon name="cil-pencil"></CIcon
                    ></CButton>
                  </router-link>
                  <CButton
                    v-if="item.criteria_type == 'AI_PN_KEYWORDS'"
                    color="danger"
                    v-c-tooltip="'Delete Configuration'"
                    variant="outline"
                    @click="removeConfig(item.id)"
                  >
                    <CIcon name="cil-trash"></CIcon>
                  </CButton>
                </CButtonToolbar>
              </td>
            </template>
          </CDataTable>
        </CCol>
      </CRow>
    </CCardBody>
  </CCard>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
export default {
  name: 'ListAiPnConfigurations',
  data() {
    return {
      list_fields: [
        { key: 'id', label: 'ID' },
        { key: 'name', label: 'Name' },
        { key: 'criteria_type', sorter: true, label: 'Criteria Type' },
        { key: 'threshold', sorter: true, label: 'Threshold' },
        { key: 'keywords', sorter: false, label: 'Keywords' },
        { key: 'excluded_keywords', sorter: false, label: 'Excluded Keywords' },
        {
          key: 'included_publisher_names',
          sorter: false,
          label: 'Included Publishers',
        },
        { key: 'slack_channel_name', sorter: true, label: 'Slack Channel' },
        { key: 'is_enabled', sorter: true, label: 'Is Active' },
        { key: 'send_pn_enabled', sorter: true, label: 'Send PN' },
        { key: 'created_at', sorter: false, label: 'Created At' },
        { key: 'created_by', sorter: true, label: 'Created By' },
        { key: 'updated_at', sorter: true, label: 'Updated At' },
        {
          key: 'updated_by',
          sorter: false,
          filter: false,
          label: 'Updated By',
        },
        { key: 'show_details', label: 'Actions', sorter: false, filter: false },
      ],
      sorters: { column: 'id', asc: true },
    };
  },
  computed: {
    ...mapGetters({
      configurationList: 'getAiPnConfigurations',
      slackChannels: 'getSlackChannels',
    }),
    mappedConfigurationList() {
      return this.configurationList.map((config) => {
        const slackChannel = this.slackChannels.find(
          (ch) => ch.id === config.slack_channel_id
        );
        return {
          ...config,
          slack_channel_name: slackChannel ? slackChannel.name : '-',
        };
      });
    },
  },
  async created() {
    this.getSlackChannels();
    this.listAiPushNotificationConfigurations();
  },
  methods: {
    ...mapActions({
      getSlackChannels: 'getSlackChannels',
      listAiPushNotificationConfigurations:
        'listAiPushNotificationConfigurations',
      removeAiPushNotificationConfiguration:
        'removeAiPushNotificationConfiguration',
      listPublisherById: 'listPublisherById',
    }),

    getThresholdValue(value) {
      if (!value) {
        return '-';
      }

      try {
        if (typeof value === 'object' && value !== null) {
          return value.threshold || '-';
        }

        if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value);
            return parsed.threshold || '-';
          } catch (e) {
            return value;
          }
        }

        return value;
      } catch (e) {
        console.error('Error parsing threshold value:', e);
        return '-';
      }
    },
    hasKeywords(value) {
      if (!value) {
        return false;
      }

      try {
        if (typeof value === 'object' && value !== null) {
          return (
            value.keywords &&
            Array.isArray(value.keywords) &&
            value.keywords.length > 0
          );
        }

        if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value);
            return (
              parsed.keywords &&
              Array.isArray(parsed.keywords) &&
              parsed.keywords.length > 0
            );
          } catch (e) {
            return value.includes(',') || value.trim() !== '';
          }
        }

        return false;
      } catch (e) {
        console.error('Error checking for keywords:', e);
        return false;
      }
    },

    hasExcludedKeywords(value) {
      if (!value) {
        return false;
      }

      try {
        if (typeof value === 'object' && value !== null) {
          return (
            value.excluded_keywords &&
            Array.isArray(value.excluded_keywords) &&
            value.excluded_keywords.length > 0
          );
        }

        if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value);
            return (
              parsed.excluded_keywords &&
              Array.isArray(parsed.excluded_keywords) &&
              parsed.excluded_keywords.length > 0
            );
          } catch (e) {
            return value.includes(',') || value.trim() !== '';
          }
        }

        return false;
      } catch (e) {
        console.error('Error checking for excluded_keywords:', e);
        return false;
      }
    },
    hasIncludedPublishers(value) {
      if (!value) {
        return false;
      }

      try {
        if (typeof value === 'object' && value !== null) {
          return (
            value.included_publisher_names &&
            Array.isArray(value.included_publisher_names) &&
            value.included_publisher_names.length > 0
          );
        }

        if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value);
            return (
              parsed.included_publisher_names &&
              Array.isArray(parsed.included_publisher_names) &&
              parsed.included_publisher_names.length > 0
            );
          } catch (e) {
            return value.includes(',') || value.trim() !== '';
          }
        }

        return false;
      } catch (e) {
        console.error('Error checking for included_publisher_names:', e);
        return false;
      }
    },
    getKeywordsArray(value) {
      if (!value) {
        return [];
      }

      try {
        if (typeof value === 'object' && value !== null) {
          if (value.keywords && Array.isArray(value.keywords)) {
            return value.keywords;
          }
          return [];
        }

        if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value);
            if (parsed.keywords && Array.isArray(parsed.keywords)) {
              return parsed.keywords;
            }
            if (Array.isArray(parsed)) {
              return parsed;
            }
            return value
              .split(',')
              .map((keyword) => keyword.trim())
              .filter((k) => k);
          } catch (e) {
            return value
              .split(',')
              .map((keyword) => keyword.trim())
              .filter((k) => k);
          }
        }

        if (Array.isArray(value)) {
          return value;
        }

        return [];
      } catch (e) {
        console.error('Error parsing keywords:', e);
        return [];
      }
    },
    getExcludedKeywordsArray(value) {
      if (!value) {
        return [];
      }

      try {
        if (typeof value === 'object' && value !== null) {
          if (
            value.excluded_keywords &&
            Array.isArray(value.excluded_keywords)
          ) {
            return value.excluded_keywords;
          }
          return [];
        }

        if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value);
            if (
              parsed.excluded_keywords &&
              Array.isArray(parsed.excluded_keywords)
            ) {
              return parsed.excluded_keywords;
            }
            if (Array.isArray(parsed)) {
              return parsed;
            }
            return value
              .split(',')
              .map((keyword) => keyword.trim())
              .filter((k) => k);
          } catch (e) {
            return value
              .split(',')
              .map((keyword) => keyword.trim())
              .filter((k) => k);
          }
        }

        if (Array.isArray(value)) {
          return value;
        }

        return [];
      } catch (e) {
        console.error('Error parsing keywords:', e);
        return [];
      }
    },
    getIncludedPublishersArray(value) {
      if (!value) {
        return [];
      }

      try {
        let publishers = [];

        if (typeof value === 'object' && value !== null) {
          if (
            value.included_publisher_names &&
            Array.isArray(value.included_publisher_names)
          ) {
            publishers = value.included_publisher_names;
          }
        } else if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value);
            if (
              parsed.included_publisher_names &&
              Array.isArray(parsed.included_publisher_names)
            ) {
              publishers = parsed.included_publisher_names;
            }
          } catch (e) {
            console.error('Error parsing JSON:', e);
            return [];
          }
        }

        return publishers;
      } catch (e) {
        console.error('Error parsing included publishers:', e);
        return [];
      }
    },
    async removeConfig(id) {
      this.$swal
        .fire({
          icon: 'question',
          text: 'Configuration will be deleted. Continue?',
        })
        .then(async (res) => {
          if (res.isConfirmed) {
            await this.removeAiPushNotificationConfiguration(id);
            await this.listAiPushNotificationConfigurations();
          }
        });
    },
  },
};
</script>

<style scoped>
.badge-larger {
  font-size: 0.8rem;
  padding: 0.4rem 0.6rem;
}
</style>
