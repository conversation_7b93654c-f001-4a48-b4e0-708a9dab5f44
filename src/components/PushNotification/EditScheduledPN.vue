<template>
  <div>
    <CCard>
      <CCardHeader>Edit Scheduled PN</CCardHeader>
      <CCardBody>
        <BaseScheduledPNForm :pn="scheduled_pn" />
        <CButton
          block
          color="success"
          variant="outline"
          @click="editPN"
          :disabled="is_disabled"
          >Submit</CButton
        >
      </CCardBody>
      <OverlayLoader v-if="loading" />
    </CCard>
  </div>
</template>

<script>
import BaseScheduledPNForm from '@/components/PushNotification/BaseScheduledPNForm';
import OverlayLoader from '@/components/views/OverlayLoader';
export default {
  name: 'EditScheduledPN',
  components: { OverlayLoader, BaseScheduledPNForm },
  data() {
    return {
      show: false,
      scheduled_pn: {
        payload: {
          articleId: '',
          title: '',
          body: '',
          platform: '',
          postfix: '',
          matchSubLang: '',
          has_title: '0',
          language: '',
          ignoreSegment: '',
          pnPeriod: [],
          skipUserFeedView: '',
          nonPreiodic: '',
          pnTopicId: '',
          nonActiveUsers: '',
          matchInterest: '',
        },
        scheduledPnTime: '',
      },
    };
  },
  created() {
    this.$store.commit('SET_GENERAL_LOADING', true);
    this.$store
      .dispatch('listScheduledPN', parseInt(this.$route.params.id))
      .then((res) => {
        this.scheduled_pn = res.data[0];
        this.$store.commit('SET_GENERAL_LOADING', false);
      })
      .catch((err) => {
        console.log(err);
        alert('Something went wrong in retrieving data');
        this.$store.commit('SET_GENERAL_LOADING', false);
      });
  },
  computed: {
    loading() {
      return this.$store.getters.getLoading;
    },
    is_disabled() {
      let errors = [];
      Object.entries(this.scheduled_pn.payload).forEach(([prop, val]) => {
        if (prop === 'articleId' && val === '') {
          errors.push('articleID');
        }
        if (prop === 'pnPeriod' && val.length === 0) {
          errors.push('pnPeriod');
        }
        if (prop === 'title' && val.includes('`')) {
          errors.push('title');
        }
        if (prop === 'body' && val.includes('`')) {
          errors.push('body');
        }
      });
      return errors.length > 0;
    },
  },
  methods: {
    editPN() {
      this.$swal
        .fire({
          icon: 'question',
          text: 'This will update the scheduled PN',
        })
        .then((res) => {
          if (res.isConfirmed) {
            let data = JSON.parse(JSON.stringify(this.scheduled_pn));
            if (data.payload.has_title === '0') {
              data.payload.title = ' ';
            }
            let passObj = {};
            let payload = {};
            let exclude = ['has_title'];
            Object.entries(data).forEach(([prop, val]) => {
              if (prop === 'payload') {
                Object.entries(data.payload).forEach(([prop, val]) => {
                  if (val !== '' && val !== '0' && !exclude.includes(prop)) {
                    payload[prop] = val;
                  }
                });
              }
              passObj[prop] = val;
            });
            if (passObj.payload.language === 'all') {
              delete passObj.payload.language;
            }
            passObj.payload = payload;
            if (passObj.payload.pnPeriod.length > 0) {
              passObj.payload.pnPeriod = passObj.payload.pnPeriod
                .sort()
                .join(',');
            } else {
              delete passObj.payload.pnPeriod;
            }
            this.$store
              .dispatch('updateScheduledPN', passObj)
              .then(() => {
                this.$router.go(-1);
              })
              .catch(() => {});
          }
        });
    },
    reset() {
      this.scheduled_pn = JSON.parse(JSON.stringify(this.pn));
    },
  },
};
</script>

<style scoped></style>
