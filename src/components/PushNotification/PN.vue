<template>
  <div>
    <CRow>
      <CCol>
        <CButtonToolbar class="float-right">
          <PNAuto @map-auto="mapAutoToModel" />
        </CButtonToolbar>
      </CCol>
    </CRow>
    <br />
    <CRow>
      <CCol>
        <CForm @submit.prevent="prePushCheck">
          <CCard>
            <CCardHeader>Article/Poll Push Notification</CCardHeader>
            <CCardBody>
              <BasePushNotificationForm
                :pn="pn"
                :set="'article'"
                @set-instant="prefillInstant"
              />
              <br /><br />
              <!-- <CButton block color="warning" variant="outline" type="button" @click='resetPNSettings'>Reset</CButton> -->
              <CButton
                block
                color="success"
                variant="outline"
                type="submit"
                :disabled="is_disabled"
                >Send</CButton
              >
            </CCardBody>
          </CCard>
        </CForm>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import BasePushNotificationForm from './BasePushNotificationForm';
import PNAuto from '@/components/PushNotification/PNAuto';
import { getPushText } from '@/helpers/pn';
import { isAutoPn } from '@/helpers/pn';
import axios from 'axios';
import * as headers from '@/helpers/headers';

export default {
  name: 'PN',
  components: { PNAuto, BasePushNotificationForm },
  data() {
    return {
      pn: {
        articleId: '',
        title: '',
        body: '',
        platform: '',
        postfix: '',
        matchSubLang: '0',
        language: '',
        ignoreSegment: '1',
        pnPeriod: ['1', '2', '3'],
        skipUserFeedView: '0',
        instant: '0',
        nonPreiodic: '1',
        pnTopicId: '',
        nonActiveUsers: '0',
        // dryMode: '1',
        has_title: '1',
        matchInterest: '0',
        scheduled: '0',
        scheduledPnTime: '',
      },
    };
  },
  computed: {
    is_disabled() {
      let errors = [];
      if (
        this.pn.articleId.startsWith('ATI') ||
        this.pn.articleId.startsWith('ATV')
      ) {
        Object.entries(this.pn).forEach(([prop, val]) => {
          if (prop === 'articleId' && val === '') {
            errors.push('articleID');
          }
          if (prop === 'scheduled' && val === '1') {
            Object.entries(this.pn).forEach(([prop2, val2]) => {
              if (prop2 === 'scheduledPnTime' && val2 === '') {
                errors.push('scheduledPnTime');
              }
            });
          }
          if (prop === 'title' && val.includes('`')) {
            errors.push('title');
          }
          if (prop === 'body' && val.includes('`')) {
            errors.push('body');
          }
        });
      } else {
        Object.entries(this.pn).forEach(([prop, val]) => {
          if (prop === 'articleId' && val === '') {
            errors.push('articleID');
          }
          if (prop === 'profileId' && val === '') {
            errors.push('profileID');
          }
          if (prop === 'pnPeriod' && val.length === 0) {
            errors.push('pnPeriod');
          }
          if (prop === 'language' && val === '') {
            errors.push('language');
          }
          if (prop === 'scheduled' && val === '1') {
            Object.entries(this.pn).forEach(([prop2, val2]) => {
              if (prop2 === 'scheduledPnTime' && val2 === '') {
                errors.push('scheduledPnTime');
              }
            });
          }
          if (prop === 'title' && val.includes('`')) {
            errors.push('title');
          }
          if (prop === 'body' && val.includes('`')) {
            errors.push('body');
          }
        });
      }
      return errors.length > 0;
    },
  },
  methods: {
    async prePushCheck() {
      // If is auto pn then straight proceed to send
      if (isAutoPn(this.pn.articleId) === true) {
        await this.sendPush();
        return;
      }

      // If pn language is different to article language and the article is not translated yet
      const { data } = await axios.get(
        process.env.VUE_APP_GET_RESOURCE_BY_UNIQUE_ID + this.pn.articleId,
        headers.createHeaders(
          this.$store.getters.getUserToken,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      );
      if (
        this.pn.language === data.language ||
        (this.pn.language !== data.language && data.isTranslated === true)
      ) {
        await this.sendPush();
        return;
      }

      await this.$swal
        .fire({
          html: `<b>${this.pn.articleId}</b> is not translated! <br>Do you want to proceed?`,
          icon: 'question',
          showCancelButton: true,
          confirmButtonText: 'Proceed',
        })
        .then((result) => {
          if (result.isConfirmed === true) {
            this.sendPush();
          }
        });
    },
    async sendPush() {
      this.$swal
        .fire({
          icon: 'question',
          text: 'This will send a notification with the current settings. Continue?',
          confirmButtonText: 'Yes',
        })
        .then(async (res) => {
          if (res.isConfirmed) {
            if (this.pn.has_title === '0') {
              this.pn.title = ' ';
            }
            if (this.pn.scheduled === '0') {
              delete this.pn.scheduledPnTime;
            }
            let newPn = {};
            let exclude = ['has_title', 'scheduled'];
            Object.entries(this.pn).forEach(([prop, val]) => {
              if (val !== '' && val !== '0' && !exclude.includes(prop)) {
                newPn[prop] = val;
              }
            });
            newPn.postfix = await this.generatePostfix();
            if (newPn.pnPeriod.length > 0) {
              newPn.pnPeriod = newPn.pnPeriod.sort().join();
            } else {
              delete newPn.pnPeriod;
            }
            this.$store
              .dispatch('checkPushHistory', newPn)
              .then(() => {
                let schedule = '0';
                if (newPn?.scheduledPnTime !== undefined) {
                  schedule = '1';
                }
                this.callPush(newPn, schedule);
              })
              .catch((err) => {
                let msg = '<p>' + err.response.data.msg + '</p><p>Re-push?</p>';
                this.$swal
                  .fire({
                    icon: 'warning',
                    html: msg,
                    confirmButtonText: 'Yes',
                  })
                  .then((res) => {
                    if (res.isConfirmed) {
                      this.callPush(newPn);
                    }
                  });
              });
          }
        });
    },
    generatePostfix() {
      let randomChars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      while (!/^(?=.*[A-Z])(?=.*\d).{6,}$/.test(result)) {
        result = '';
        for (let i = 0; i < 6; i++) {
          result += randomChars.charAt(
            Math.floor(Math.random() * randomChars.length)
          );
        }
      }
      return result;
    },
    callPush(pn, scheduled) {
      this.$store
        .dispatch('sendPush', pn)
        .then(() => {
          const text = getPushText(pn, scheduled);
          this.$swal
            .fire({
              icon: 'success',
              text: text,
              showCancelButton: false,
            })
            .then(() => {
              this.resetPNSettings();
            });
        })
        .catch((err) => {
          let text =
            'API returns failure. Please retry & do contact tech team if the issue persists';
          if (err.response.data.error !== '') {
            text = err.response.data.error;
          }
          this.$swal.fire({
            icon: 'error',
            text: text,
            showCancelButton: false,
          });
        });
    },
    savePush(pn) {
      this.$store.dispatch('logPush', pn);
    },
    prefillInstant() {
      this.pn.platform = '';
      this.pn.nonPreiodic = '1';
      this.pn.pnTopicId = '';
      this.pn.skipUserFeedView = '0';
      this.pn.matchSubLang = '0';
      this.pn.ignoreSegment = '1';
    },
    resetPNSettings() {
      this.pn = {
        articleId: '',
        title: '',
        body: '',
        platform: '',
        postfix: '',
        matchSubLang: '0',
        language: '',
        ignoreSegment: '1',
        pnPeriod: ['1', '2', '3'],
        skipUserFeedView: '0',
        instant: '0',
        nonPreiodic: '1',
        pnTopicId: '',
        nonActiveUsers: '0',
        has_title: '1',
        matchInterest: '0',
        scheduled: '0',
        scheduledPnTime: '',
      };
    },
    mapAutoToModel(item) {
      Object.entries(item).forEach(([prop, val]) => {
        this.pn[prop] = val;
      });
    },
  },
};
</script>

<style scoped></style>
