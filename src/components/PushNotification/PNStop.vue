<template>
  <div>
    <div v-show="checkAction('pn_cancel')">
      <CRow>
        <CCol>
          <CForm @submit.prevent="sendStopPN">
            <CCard>
              <CCardHeader>Cancel Push Notification</CCardHeader>
              <CCardBody>
                <CInput
                  v-model="stopPN.unique_id"
                  label="Unique ID"
                  :is-valid="stopPN.unique_id.length > 0"
                  invalid-feedback="Unique ID is required"
                />
                <CButton
                  block
                  color="warning"
                  type="submit"
                  variant="outline"
                  :disabled="is_disabled_stop"
                  >Submit</CButton
                >
              </CCardBody>
            </CCard>
          </CForm>
        </CCol>
      </CRow>
    </div>
    <CRow>
      <CCol sm="12" lg="6" v-show="checkAction('pn_delete')">
        <CForm @submit.prevent="sendDeletePN">
          <CCard>
            <CCardHeader>Delete Push Notification</CCardHeader>
            <CCardBody>
              <CInput
                v-model="deletePN.uniqueId"
                label="Unique ID"
                :is-valid="deletePN.uniqueId.length > 0"
                invalid-feedback="Unique ID is required"
              />
              <!-- <CInput v-model="deletePN.newArticleId" label="New Article Unique ID"/>
              <div class="form-group">
                <label for="title">PN Title</label>
                <input v-model="deletePN.title" id="title" class="form-control"
                       placeholder="EN: 35 w/desc, 120 w/o desc; ZH 15 w/desc, 45 w/o desc"/>
                <span id="characterLimit" class="form-text text-muted"
                      v-text="deletePN.title.length + ' Characters'"/>
              </div>
              <div class="form-group">
                <label for="description">PN Description</label>
                <textarea :maxlength="body_max" v-model="deletePN.body" id="description" class="form-control"/>
                <span id="bodyLimit" class="form-text text-muted"
                      v-text="deletePN.body.length + ' Characters'"/>
              </div>
              <CRow>
                <CCol>Language</CCol>
                <CCol>
                  <div class="form-check form-check-inline" v-for="(option, optionIndex) in language_options"
                       :key="option.value+optionIndex+'vid_lang'">
                    <input class="form-check-input" type="radio" :id="option.value+optionIndex+'vid_lang'" :value="option.value"
                           v-model="deletePN.language">
                    <label class="form-check-label" :for="option.value+optionIndex+'vid_lang'">{{ option.label }}</label>
                  </div>
                </CCol>
              </CRow>
              <div>
                <br>
                <CRow>
                  <CCol>PN Frequency</CCol>
                  <CCol>
                    <div class="form-check form-check-inline" v-for="(option, optionIndex) in frequency_options"
                         :for="option.value+optionIndex+'pn'">
                      <input class="form-check-input" type="checkbox" :value="option.value" :id="option.value+optionIndex+'pn'"
                             v-model="deletePN.pnPeriod">
                      <label class="form-check-label" :for="option.value+optionIndex+'pn'">{{ option.label }}</label>
                    </div>
                  </CCol>
                </CRow>
                <br>
              </div> -->
              <CButton
                block
                color="warning"
                type="submit"
                variant="outline"
                :disabled="is_disabled_del"
                >Submit</CButton
              >
            </CCardBody>
          </CCard>
        </CForm>
      </CCol>
      <CCol sm="12" lg="6" v-show="checkAction('pn_delete_test')">
        <CForm @submit.prevent="sendDeleteTestPN">
          <CCard>
            <CCardHeader>Delete Test Push Notification</CCardHeader>
            <CCardBody>
              <CInput
                v-model="deleteTestPN.uniqueId"
                label="Unique ID"
                :is-valid="deleteTestPN.uniqueId.length > 0"
                invalid-feedback="Unique ID is required"
              />
              <CInput
                v-model="deleteTestPN.newArticleId"
                label="New Article Unique ID"
              />
              <div>
                <CRow>
                  <CCol> OS </CCol>
                  <CCol>
                    <div
                      class="form-check form-check-inline"
                      v-for="(option, optionIndex) in test_os_options"
                      :key="option.value + optionIndex + '_test_os'"
                    >
                      <input
                        class="form-check-input"
                        type="radio"
                        :id="option.value + optionIndex + '_test_os'"
                        :value="option.value"
                        v-model="deleteTestPN.platform"
                      />
                      <label
                        class="form-check-label"
                        :for="option.value + optionIndex + '_test_os'"
                        >{{ option.label }}</label
                      >
                    </div>
                  </CCol>
                </CRow>
                <br />
              </div>
              <CInput
                v-model="deleteTestPN.profileId"
                label="Profile-ID"
                :is-valid="deleteTestPN.profileId.length > 0"
                invalid-feedback="Profile ID is required"
              />
              <!-- <div class="form-group">
                <label for="titleTest">PN Title</label>
                <input v-model="deleteTestPN.title" id="titleTest" class="form-control"
                       placeholder="EN: 35 w/desc, 120 w/o desc; ZH 15 w/desc, 45 w/o desc"/>
                <span id="characterLimitTest" class="form-text text-muted"
                      v-text="deleteTestPN.title.length + ' Characters'"/>
              </div>
              <div class="form-group">
                <label for="descriptionTest">PN Description</label>
                <textarea :maxlength="body_max" v-model="deleteTestPN.body" id="descriptionTest" class="form-control"/>
                <span id="bodyLimitTest" class="form-text text-muted"
                      v-text="deleteTestPN.body.length + ' Characters'"/>
              </div>
              <CRow>
                <CCol>Language</CCol>
                <CCol>
                  <div class="form-check form-check-inline" v-for="(option, optionIndex) in language_options"
                       :key="option.value+optionIndex+'vid_lang'">
                    <input class="form-check-input" type="radio" :id="option.value+optionIndex+'vid_lang'" :value="option.value"
                           v-model="deleteTestPN.language">
                    <label class="form-check-label" :for="option.value+optionIndex+'vid_lang'">{{ option.label }}</label>
                  </div>
                </CCol>
              </CRow>
              <div>
                <br>
                <CRow>
                  <CCol>PN Frequency</CCol>
                  <CCol>
                    <div class="form-check form-check-inline" v-for="(option, optionIndex) in frequency_options"
                         :for="option.value+optionIndex+'pn'">
                      <input class="form-check-input" type="checkbox" :value="option.value" :id="option.value+optionIndex+'pn'"
                             v-model="deleteTestPN.pnPeriod">
                      <label class="form-check-label" :for="option.value+optionIndex+'pn'">{{ option.label }}</label>
                    </div>
                  </CCol>
                </CRow>
                <br>
              </div> -->
              <CButton
                block
                color="warning"
                type="submit"
                variant="outline"
                :disabled="is_disabled_del_test"
                >Submit</CButton
              >
            </CCardBody>
          </CCard>
        </CForm>
      </CCol>
    </CRow>
  </div>
</template>

<script>
export default {
  name: 'PNCancel',
  data() {
    return {
      body_max: 170,
      stopPN: {
        unique_id: '',
      },
      language_options: [
        { value: 'en', label: 'EN' },
        { value: 'ms', label: 'MS' },
        { value: 'zh', label: 'ZH' },
      ],
      deletePN: {
        uniqueId: '',
        // newArticleId : '',
        // title : '',
        // language : '',
        // body : '',
        // pnPeriod : ['1', '2', '3']
      },
      deleteTestPN: {
        uniqueId: '',
        platform: '',
        profileId: '',
        // newArticleId : '',
        // title : '',
        // body : '',
        // language : '',
        // pnPeriod : ['1', '2', '3']
      },
      test_os_options: [
        { value: 'a', label: 'Android' },
        { value: 'i', label: 'iOS' },
      ],
      frequency_options: [
        { value: '1', label: 'Low' },
        { value: '2', label: 'Medium' },
        { value: '3', label: 'High' },
      ],
    };
  },
  computed: {
    is_disabled_del_test() {
      let errors = [];
      Object.entries(this.deleteTestPN).forEach(([prop, val]) => {
        if (prop === 'uniqueId' && val === '') {
          errors.unshift('uniqueId');
        }
        if (prop === 'profileId' && val === '') {
          errors.unshift('profileId');
        }
        if (prop === 'platform' && val === '') {
          errors.unshift('platform');
        }
      });
      return errors.length > 0;
    },
    is_disabled_del() {
      let errors = [];
      Object.entries(this.deletePN).forEach(([prop, val]) => {
        if (prop === 'uniqueId' && val === '') {
          errors.unshift('uniqueId');
        }
      });
      return errors.length > 0;
    },
    is_disabled_stop() {
      let errors = [];
      Object.entries(this.stopPN).forEach(([prop, val]) => {
        if (prop === 'unique_id' && val === '') {
          errors.unshift('unique_id');
        }
      });
      return errors.length > 0;
    },
  },
  methods: {
    sendStopPN() {
      this.$swal
        .fire({
          icon: 'question',
          text: `Sending Cancel PN for Resource ${this.stopPN.unique_id}`,
        })
        .then(async (res) => {
          if (res.isConfirmed) {
            let newObj = {};
            Object.entries(this.stopPN).forEach(([prop, val]) => {
              if (prop === 'unique_id') {
                newObj[prop] = val.trim();
              } else {
                newObj[prop] = val;
              }
            });
            newObj.passId = newObj.unique_id;
            newObj.unique_id = `CA_${newObj.unique_id}`;
            newObj.action = 'cancel';
            newObj.postfix = await this.generatePostfix();
            await this.$store.dispatch('stopSendingPN', newObj);
          }
        });
    },
    sendDeletePN() {
      this.$swal
        .fire({
          icon: 'question',
          text: `Sending Delete PN for Resource ${this.deletePN.uniqueId}`,
        })
        .then(async (res) => {
          if (res.isConfirmed) {
            let newObj = {};
            Object.entries(this.deletePN).forEach(([prop, val]) => {
              if (prop === 'uniqueId') {
                newObj[prop] = val.trim();
              } else {
                newObj[prop] = val;
              }
            });
            // newObj.pnPeriod = newObj.pnPeriod.sort().join()
            newObj.passId = newObj.uniqueId;
            newObj.uniqueId = `DE_${newObj.uniqueId}`;
            newObj.action = 'delete';
            newObj.postfix = await this.generatePostfix();
            await this.$store.dispatch('deletePN', newObj);
          }
        });
    },
    sendDeleteTestPN() {
      this.$swal
        .fire({
          icon: 'question',
          text: `This will delete the notification for article ${this.deleteTestPN.uniqueId}. Continue?`,
        })
        .then(async (res) => {
          if (res.isConfirmed) {
            let newObj = {};
            Object.entries(this.deleteTestPN).forEach(([prop, val]) => {
              newObj[prop] = val;
            });
            newObj.profileId = newObj.platform + '_' + newObj.profileId;
            newObj.postfix = await this.generatePostfix();
            // newObj.pnPeriod = newObj.pnPeriod.sort().join()
            await this.$store.dispatch('deleteTestPN', newObj);
          }
        });
    },
    generatePostfix() {
      let randomChars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      while (!/^(?=.*[A-Z])(?=.*\d).{6,}$/.test(result)) {
        result = '';
        for (let i = 0; i < 6; i++) {
          result += randomChars.charAt(
            Math.floor(Math.random() * randomChars.length)
          );
        }
      }
      return result;
    },
  },
};
</script>

<style scoped></style>
