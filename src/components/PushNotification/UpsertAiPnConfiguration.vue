<template>
  <div>
    <CRow>
      <CCol>
        <CButton
          variant="outline"
          shape="pill"
          color="primary"
          @click="goBack()"
          >Back</CButton
        >
      </CCol>
    </CRow>
    <br />
    <CCard>
      <CCardHeader>
        {{
          isEdit
            ? 'Edit AI Push Notification Configuration'
            : 'Create AI Push Notification Configuration'
        }}
      </CCardHeader>
      <CCardBody>
        <CForm @submit.prevent="saveConfiguration">
          <CRow>
            <CCol>
              <CInput
                v-model="config.name"
                label="Configuration Name"
                placeholder="Enter a name for this configuration (optional)"
              />
            </CCol>
          </CRow>

          <CRow class="mt-3">
            <CCol>
              <CSelect
                v-model="config.criteria_type"
                label="Criteria Type"
                :options="criteriaTypeOptions"
                :is-valid="config.criteria_type !== ''"
                invalid-feedback="Criteria type is required"
                required
                @change="onCriteriaTypeChange"
              />
            </CCol>
          </CRow>

          <CRow class="mt-3" v-if="config.criteria_type">
            <CCol>
              <CInput
                v-model.number="config.criteria_value.threshold"
                label="Breaking Score Value"
                type="number"
                min="1"
                placeholder="Enter breaking score threshold"
                :is-valid="isBreakingScoreValid"
                invalid-feedback="Breaking score must be a number between 0 to 10"
                required
              />
            </CCol>
          </CRow>
          <CRow class="mt-3" v-if="config.criteria_type === 'AI_PN_KEYWORDS'">
            <CCol>
              <label>Keywords</label>
              <div class="keyword-input-container p-2 border rounded">
                <div class="d-flex flex-wrap align-items-center">
                  <CBadge
                    v-for="(keyword, index) in config.criteria_value &&
                    config.criteria_value.keywords
                      ? config.criteria_value.keywords
                      : []"
                    :key="index"
                    color="success"
                    class="mr-1 mb-1 badge-larger d-flex align-items-center"
                  >
                    {{ keyword }}
                    <CButton
                      class="ml-1 p-0 badge-remove-btn"
                      @click="removeKeyword(index)"
                    >
                      <span>&times;</span>
                    </CButton>
                  </CBadge>
                  <input
                    ref="keywordInput"
                    v-model="currentKeyword"
                    class="keyword-input flex-grow-1 border-0"
                    placeholder="Type a keyword and press Enter"
                    @keydown.enter.prevent="addKeyword"
                    @keydown.backspace="handleBackspace"
                    @keydown="handleComma"
                    @blur="onInputBlur"
                  />
                </div>
              </div>
              <div
                v-if="
                  !isKeywordsValid &&
                  (!config.criteria_value ||
                    !config.criteria_value.keywords ||
                    config.criteria_value.keywords.length === 0)
                "
                class="invalid-feedback d-block"
              >
                At least one keyword is required
              </div>
              <small class="form-text text-muted">
                Type a keyword and press Enter or comma to add it. Click × to
                remove a keyword.
              </small>
            </CCol>
            <CCol>
              <label>Excluded Keywords</label>
              <div class="keyword-input-container p-2 border rounded">
                <div class="d-flex flex-wrap align-items-center">
                  <CBadge
                    v-for="(keyword, index) in config.criteria_value &&
                    config.criteria_value.excludedKeywords
                      ? config.criteria_value.excludedKeywords
                      : []"
                    :key="index"
                    color="success"
                    class="mr-1 mb-1 badge-larger d-flex align-items-center"
                  >
                    {{ keyword }}
                    <CButton
                      class="ml-1 p-0 badge-remove-btn"
                      @click="removeExclduedKeyword(index)"
                    >
                      <span>&times;</span>
                    </CButton>
                  </CBadge>
                  <input
                    ref="excludedKeywordInput"
                    v-model="currentExcludedKeyword"
                    class="keyword-input flex-grow-1 border-0"
                    placeholder="Type an excluded keyword and press Enter"
                    @keydown.enter.prevent="addExcludedKeyword"
                    @keydown.backspace="handleExcludedBackspace"
                    @keydown="handleExcludedComma"
                    @blur="onExcludedInputBlur"
                  />
                </div>
              </div>
              <small class="form-text text-muted">
                Type an excluded keyword and press Enter or comma to add it.
                Click × to remove a keyword.
              </small>
            </CCol>
          </CRow>

          <CRow class="mt-3" v-if="config.criteria_type === 'AI_PN_KEYWORDS'">
            <CCol>
              <label>Included Publishers</label>
              <div class="mb-2">
                <multiselect
                  ref="includedPublishersSelect"
                  v-model="selectedIncludedPublisher"
                  :options="includedPublisherOptions"
                  :multiple="false"
                  track-by="id"
                  label="name"
                  :allow-empty="true"
                  :searchable="true"
                  :loading="includedPublishersLoading"
                  :internal-search="false"
                  placeholder="Search and select publishers to include"
                  @search-change="searchIncludedPublishers"
                  @input="addIncludedPublisher"
                >
                  <span slot="noResult">
                    No publishers found. Try searching for a publisher name.
                  </span>
                  <span slot="noOptions"> Type to search for publishers </span>
                </multiselect>
              </div>
              <div class="keyword-input-container p-2 border rounded">
                <div class="d-flex flex-wrap align-items-center">
                  <CBadge
                    v-for="(publisherName, index) in config.criteria_value &&
                    config.criteria_value.includedPublisherNames
                      ? config.criteria_value.includedPublisherNames
                      : []"
                    :key="'included-' + index"
                    color="info"
                    class="mr-1 mb-1 badge-larger d-flex align-items-center"
                  >
                    {{ publisherName }}
                    <CButton
                      class="ml-1 p-0 badge-remove-btn"
                      @click="removeIncludedPublisher(index)"
                    >
                      <span>&times;</span>
                    </CButton>
                  </CBadge>
                  <span
                    v-if="
                      !config.criteria_value ||
                      !config.criteria_value.includedPublishers ||
                      config.criteria_value.includedPublishers.length === 0
                    "
                    class="text-muted"
                  >
                    No publishers included
                  </span>
                </div>
              </div>
              <small class="form-text text-muted">
                Search and select publishers to include. Click × to remove a
                publisher.
              </small>
              <div
                v-if="
                  !isPublishersValid &&
                  config.criteria_type === 'AI_PN_KEYWORDS'
                "
                class="invalid-feedback d-block"
              >
                At least one publisher is required
              </div>
            </CCol>
          </CRow>

          <CRow class="mt-3">
            <CCol>
              <CSelect
                v-model="config.slack_channel_id"
                label="Slack Channel"
                :options="slackChannelOptions"
                :is-valid="config.slack_channel_id !== ''"
                invalid-feedback="Slack channel is required"
                required
              />
            </CCol>
          </CRow>

          <CRow class="mt-3">
            <CCol>
              <CSwitch
                shape="pill"
                color="success"
                :checked="config.is_enabled"
                @update:checked="updateIsEnabled($event)"
                label="Is Active"
                class="mr-1"
              />
              <label class="ml-2">Is Active</label>
            </CCol>
          </CRow>

          <!--<CRow class="mt-3">
            <CCol>
              <CSwitch
                shape="pill"
                color="success"
                :checked="config.send_pn_enabled"
                @update:checked="updateSendPnEnabled($event)"
                label="Enable Push Notifications"
                class="mr-1"
              />
              <label class="ml-2">Enable Push Notifications</label>
            </CCol>
          </CRow>-->

          <CRow class="mt-4">
            <CCol>
              <CButton color="primary" type="submit" :disabled="!isFormValid">
                {{ isEdit ? 'Update' : 'Create' }}
              </CButton>
            </CCol>
          </CRow>
        </CForm>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import Multiselect from 'vue-multiselect';
import {
  AI_PN_KEYWORDS_CRITERIA_TYPE,
  BREAKING_SCORE_CRITERIA_TYPE,
} from '@/constant/constants';

export default {
  name: 'UpsertAiPnConfiguration',
  components: {
    Multiselect,
  },
  props: {
    id: {
      type: [String, Number],
      default: null,
    },
  },
  data() {
    return {
      config: {
        name: '',
        criteria_type: '',
        criteria_value: {
          threshold: 1,
        },
        slack_channel_id: '',
        is_enabled: true,
        send_pn_enabled: false,
      },
      currentKeyword: '',
      currentExcludedKeyword: '',
      selectedIncludedPublisher: null,
      publishers: [],
      includedPublisherOptions: [],
      includedPublishersLoading: false,
      searchTimeout: null,
      criteriaTypeOptions: [
        { value: '', label: 'Select criteria type', disabled: true },
        { value: 'BREAKING_SCORE', label: 'Breaking Score' },
        { value: 'AI_PN_KEYWORDS', label: 'AI PN Keywords' },
      ],
      isLoading: false,
    };
  },
  computed: {
    ...mapGetters({
      slackChannels: 'getSlackChannels',
      allPublishers: 'getAllPublisher',
    }),
    slackChannelOptions() {
      const options = [
        { value: '', label: 'Select slack channel', disabled: true },
      ];

      if (this.slackChannels && this.slackChannels.length > 0) {
        options.push(
          ...this.slackChannels.map((channel) => ({
            value: channel.id,
            label: channel.name,
          }))
        );
      }

      return options;
    },

    isEdit() {
      return !!this.id;
    },

    isBreakingScoreValid() {
      if (
        !this.config.criteria_value ||
        this.config.criteria_value.threshold === undefined
      ) {
        return false;
      }
      const score = Number(this.config.criteria_value.threshold);
      return !isNaN(score) && score >= 0 && score <= 10;
    },
    isKeywordsValid() {
      if (this.config.criteria_type !== AI_PN_KEYWORDS_CRITERIA_TYPE) {
        return true;
      }
      return (
        this.config.criteria_value &&
        this.config.criteria_value.keywords &&
        this.config.criteria_value.keywords.length > 0
      );
    },
    isPublishersValid() {
      if (this.config.criteria_type !== AI_PN_KEYWORDS_CRITERIA_TYPE) {
        return true;
      }
      return (
        this.config.criteria_value &&
        this.config.criteria_value.includedPublishers &&
        this.config.criteria_value.includedPublishers.length > 0
      );
    },
    isFormValid() {
      const hasType = this.config.criteria_type !== '';
      const hasSlackChannel = this.config.slack_channel_id !== '';

      if (!this.config.criteria_value) {
        return false;
      }

      if (this.config.criteria_type === BREAKING_SCORE_CRITERIA_TYPE) {
        return hasType && hasSlackChannel && this.isBreakingScoreValid;
      } else if (this.config.criteria_type === AI_PN_KEYWORDS_CRITERIA_TYPE) {
        return (
          hasType &&
          hasSlackChannel &&
          this.isKeywordsValid &&
          this.isBreakingScoreValid &&
          this.isPublishersValid
        );
      }

      return hasType && hasSlackChannel;
    },
  },
  async created() {
    this.getSlackChannels();
    await this.loadPublishers();
    if (this.isEdit) {
      this.isLoading = true;
      try {
        const response = await this.listAiPushNotificationConfigurations(
          this.id
        );
        if (response && response.data) {
          const {
            name,
            criteria_type,
            criteria_value,
            slack_channel_id,
            is_enabled,
            send_pn_enabled,
          } = response.data;

          let parsedCriteriaValue = {
            threshold: 1,
          };

          if (typeof criteria_value === 'string') {
            try {
              const parsed = JSON.parse(criteria_value);

              if (criteria_type === AI_PN_KEYWORDS_CRITERIA_TYPE) {
                const {
                  excluded_keywords,
                  included_publishers,
                  included_publisher_names,
                  ...rest
                } = parsed;
                parsedCriteriaValue = {
                  ...rest,
                  excludedKeywords: excluded_keywords || [],
                  keywords: parsed.keywords || [],
                  includedPublishers: included_publishers || [],
                  includedPublisherNames: included_publisher_names || [],
                };
              } else {
                parsedCriteriaValue = {
                  ...parsed,
                };
              }
            } catch (e) {
              console.error('Failed to parse criteria_value JSON:', e);
            }
          } else if (
            typeof criteria_value === 'object' &&
            criteria_value !== null
          ) {
            if (criteria_type === AI_PN_KEYWORDS_CRITERIA_TYPE) {
              const {
                excluded_keywords,
                included_publishers,
                included_publisher_names,
                ...rest
              } = criteria_value;
              parsedCriteriaValue = {
                ...rest,
                threshold: criteria_value.threshold || 1,
                excludedKeywords: excluded_keywords || [],
                keywords: criteria_value.keywords || [],
                includedPublishers: included_publishers || [],
                includedPublisherNames: included_publisher_names || [],
              };
            } else {
              parsedCriteriaValue = {
                ...criteria_value,
                threshold: criteria_value.threshold || 1,
              };
            }
          }

          this.config = {
            name,
            criteria_type,
            slack_channel_id,
            is_enabled: is_enabled === true,
            send_pn_enabled: send_pn_enabled === true,
          };

          this.$set(this.config, 'criteria_value', parsedCriteriaValue);
          this.refreshDropdownOptions();
        }
      } catch (error) {
        console.error('Error loading configuration:', error);
        this.$swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to load configuration details',
        });
      } finally {
        this.isLoading = false;
      }
    }
  },
  methods: {
    ...mapActions({
      getSlackChannels: 'getSlackChannels',
      createAiPnConfiguration: 'createAiPnConfiguration',
      updateAiPnConfiguration: 'updateAiPnConfiguration',
      listAiPushNotificationConfigurations:
        'listAiPushNotificationConfigurations',
      listAllPublishers: 'listAllPublishers',
      listPublisherById: 'listPublisherById',
    }),
    onCriteriaTypeChange() {
      if (this.config.criteria_type === BREAKING_SCORE_CRITERIA_TYPE) {
        this.$set(this.config, 'criteria_value', {
          threshold: 1,
        });
      } else if (this.config.criteria_type === AI_PN_KEYWORDS_CRITERIA_TYPE) {
        this.$set(this.config, 'criteria_value', {
          threshold: 1,
          keywords: [],
          excludedKeywords: [],
          includedPublishers: [],
          includedPublisherNames: [],
        });
      }
      this.currentKeyword = '';
      this.currentExcludedKeyword = '';
      this.selectedIncludedPublisher = null;
      this.refreshDropdownOptions();
    },
    addKeyword() {
      const keyword = this.currentKeyword.trim();
      if (!this.config.criteria_value) {
        this.$set(this.config, 'criteria_value', {
          threshold: 1,
          keywords: [],
          excludedKeywords: [],
          includedPublishers: [],
          includedPublisherNames: [],
        });
      }
      if (!this.config.criteria_value.keywords) {
        this.$set(this.config.criteria_value, 'keywords', []);
      }

      if (keyword && !this.config.criteria_value.keywords.includes(keyword)) {
        this.config.criteria_value.keywords.push(keyword);
      }
      this.currentKeyword = '';
    },
    addExcludedKeyword() {
      const excludedKeyword = this.currentExcludedKeyword.trim();
      if (!this.config.criteria_value) {
        this.$set(this.config, 'criteria_value', {
          threshold: 1,
          keywords: [],
          excludedKeywords: [],
          includedPublishers: [],
          includedPublisherNames: [],
        });
      }
      if (!this.config.criteria_value.excludedKeywords) {
        this.$set(this.config.criteria_value, 'excludedKeywords', []);
      }
      if (
        excludedKeyword &&
        !this.config.criteria_value.excludedKeywords.includes(excludedKeyword)
      ) {
        this.config.criteria_value.excludedKeywords.push(excludedKeyword);
      }
      this.currentExcludedKeyword = '';
    },
    removeKeyword(index) {
      if (!this.config.criteria_value || !this.config.criteria_value.keywords) {
        return;
      }
      this.config.criteria_value.keywords.splice(index, 1);
    },
    removeExclduedKeyword(index) {
      if (
        !this.config.criteria_value ||
        !this.config.criteria_value.excludedKeywords
      ) {
        return;
      }
      this.config.criteria_value.excludedKeywords.splice(index, 1);
    },
    handleBackspace(event) {
      if (
        this.currentKeyword === '' &&
        this.config.criteria_value &&
        this.config.criteria_value.keywords &&
        this.config.criteria_value.keywords.length > 0 &&
        event.target.selectionStart === 0
      ) {
        this.config.criteria_value.keywords.pop();
      }
    },
    handleExcludedBackspace(event) {
      if (
        this.currentExcludedKeyword === '' &&
        this.config.criteria_value &&
        this.config.criteria_value.excludedKeywords &&
        this.config.criteria_value.excludedKeywords.length > 0 &&
        event.target.selectionStart === 0
      ) {
        this.config.criteria_value.excludedKeywords.pop();
      }
    },
    handleComma(event) {
      if (event.key === ',') {
        event.preventDefault();
        this.addKeyword();
      }
    },
    handleExcludedComma(event) {
      if (event.key === ',') {
        event.preventDefault();
        this.addExcludedKeyword();
      }
    },
    onInputBlur() {
      if (this.currentKeyword.trim()) {
        this.addKeyword();
      }
    },
    onExcludedInputBlur() {
      if (this.currentExcludedKeyword.trim()) {
        this.addExcludedKeyword();
      }
    },
    async loadPublishers() {
      try {
        const publishers = await this.listAllPublishers({ limit: 50 });
        this.publishers = publishers || [];

        this.includedPublisherOptions =
          this.filterAvailablePublishers(publishers);
      } catch (error) {
        console.error('Error loading publishers:', error);
        this.publishers = [];
        this.includedPublisherOptions = [];
      }
    },
    filterAvailablePublishers(publishers) {
      if (!publishers) return [];

      const includedIds = this.config.criteria_value?.includedPublishers || [];

      let unavailableIds = [];
      unavailableIds = [...includedIds];

      return publishers.filter(
        (publisher) => !unavailableIds.includes(publisher.id)
      );
    },
    async searchPublishers(query) {
      if (!query || query.length < 2) {
        this.includedPublisherOptions = this.filterAvailablePublishers(
          this.publishers
        );
        return;
      }

      try {
        const searchResults = await this.listAllPublishers({
          name: query,
          limit: 20,
        });

        const mergedPublishers = [...this.publishers];
        if (searchResults && searchResults.length > 0) {
          searchResults.forEach((newPublisher) => {
            const existingIndex = mergedPublishers.findIndex(
              (p) => p.id === newPublisher.id
            );
            if (existingIndex === -1) {
              mergedPublishers.push(newPublisher);
            } else {
              mergedPublishers[existingIndex] = newPublisher;
            }
          });
        }
        this.publishers = mergedPublishers;

        const filteredResults = this.filterAvailablePublishers(searchResults);
        this.includedPublisherOptions = filteredResults;
      } catch (error) {
        console.error('Error searching publishers:', error);
        this.includedPublisherOptions = this.filterAvailablePublishers(
          this.publishers
        );
      }
    },
    searchIncludedPublishers(query) {
      this.includedPublishersLoading = true;

      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }

      this.searchTimeout = setTimeout(async () => {
        await this.searchPublishers(query, 'included');
        this.includedPublishersLoading = false;
      }, 300);
    },

    addIncludedPublisher(publisher) {
      if (!publisher) return;

      if (!this.config.criteria_value) {
        this.$set(this.config, 'criteria_value', {
          threshold: 1,
          keywords: [],
          excludedKeywords: [],
          includedPublishers: [],
          includedPublisherNames: [],
        });
      }
      if (!this.config.criteria_value.includedPublishers) {
        this.$set(this.config.criteria_value, 'includedPublishers', []);
      }
      if (!this.config.criteria_value.includedPublisherNames) {
        this.$set(this.config.criteria_value, 'includedPublisherNames', []);
      }

      if (
        !this.config.criteria_value.includedPublishers.includes(publisher.id)
      ) {
        this.config.criteria_value.includedPublishers.push(publisher.id);
        this.config.criteria_value.includedPublisherNames.push(publisher.name);

        this.sortPublisherArrays();
      }
      this.selectedIncludedPublisher = null;

      this.refreshDropdownOptions();
    },
    removeIncludedPublisher(index) {
      if (
        !this.config.criteria_value ||
        !this.config.criteria_value.includedPublishers
      ) {
        return;
      }
      this.config.criteria_value.includedPublishers.splice(index, 1);
      this.config.criteria_value.includedPublisherNames.splice(index, 1);

      this.refreshDropdownOptions();
    },
    refreshDropdownOptions() {
      this.includedPublisherOptions = this.filterAvailablePublishers(
        this.publishers
      );
    },
    sortPublisherArrays() {
      if (this.config.criteria_value?.includedPublishers?.length > 0) {
        const pairs = this.config.criteria_value.includedPublishers.map(
          (id, index) => ({
            id,
            name: this.config.criteria_value.includedPublisherNames[index],
          })
        );

        pairs.sort((a, b) => a.id - b.id);

        this.config.criteria_value.includedPublishers = pairs.map(
          (pair) => pair.id
        );
        this.config.criteria_value.includedPublisherNames = pairs.map(
          (pair) => pair.name
        );
      }
    },
    updateIsEnabled(value) {
      this.$set(this.config, 'is_enabled', value);
    },
    updateSendPnEnabled(value) {
      this.$set(this.config, 'send_pn_enabled', value);
    },
    async saveConfiguration() {
      if (!this.isFormValid) {
        return;
      }

      let criteriaValue = {};

      if (!this.config.criteria_value) {
        if (this.config.criteria_type === BREAKING_SCORE_CRITERIA_TYPE) {
          this.$set(this.config, 'criteria_value', {
            threshold: 1,
          });
        } else {
          this.$set(this.config, 'criteria_value', {
            threshold: 1,
            keywords: [],
            excludedKeywords: [],
            includedPublishers: [],
            includedPublisherNames: [],
          });
        }
      }

      if (this.config.criteria_type === BREAKING_SCORE_CRITERIA_TYPE) {
        criteriaValue = {
          threshold: this.config.criteria_value.threshold || 1,
        };
      } else {
        const sortedPublisherIds = [
          ...(this.config.criteria_value.includedPublishers || []),
        ].sort((a, b) => a - b);

        criteriaValue = {
          threshold: this.config.criteria_value.threshold || 1,
          keywords: this.config.criteria_value.keywords || [],
          excluded_keywords: this.config.criteria_value.excludedKeywords || [],
          included_publishers: sortedPublisherIds,
        };
      }

      const configData = {
        criteria_type: this.config.criteria_type,
        criteria_value: JSON.stringify(criteriaValue),
        slack_channel_id: this.config.slack_channel_id,
        is_enabled: this.config.is_enabled ? 1 : 0,
        send_pn_enabled: this.config.send_pn_enabled ? 1 : 0,
      };

      this.config.name?.trim()
        ? (configData.name = this.config.name.trim())
        : delete configData.name;

      this.isEdit
        ? (configData.updated_by = `${this.$store.getters.getUser.name} (${this.$store.getters.getUser.id})`)
        : (configData.created_by = `${this.$store.getters.getUser.name} (${this.$store.getters.getUser.id})`);
      try {
        if (this.isEdit) {
          await this.updateAiPnConfiguration({
            id: this.id,
            config: configData,
          });
          this.$swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'Configuration updated successfully',
          });
        } else {
          await this.createAiPnConfiguration(configData);
          this.$swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'Configuration created successfully',
          });
        }
        this.goBack();
      } catch (error) {
        console.error('Error saving configuration:', error);
        this.$swal.fire({
          icon: 'error',
          title: 'Error',
          text: error,
        });
      }
    },
    goBack() {
      this.$router.push({
        path: '/pn-dashboard-2',
        query: { tab: 'ai-config' },
      });
    },
  },
  beforeDestroy() {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  },
};
</script>

<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>

<style scoped>
.badge-larger {
  font-size: 0.8rem;
  padding: 0.4rem 0.6rem;
}

.keyword-input-container {
  min-height: 100px;
  background-color: #fff;
}

.keyword-input {
  min-width: 200px;
  outline: none;
  padding: 0.4rem;
}

.badge-remove-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  line-height: 1;
  opacity: 0.7;
}

.badge-remove-btn:hover {
  opacity: 1;
}
</style>
