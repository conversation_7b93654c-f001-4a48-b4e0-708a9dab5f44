<template>
  <div>
    <CInput
      v-model="pn.payload.articleId"
      label="Article ID"
      :is-valid="pn.payload.articleId !== ''"
      invalid-feedback="Article ID is required"
    />
    <CRow v-if="!isAuto">
      <CCol>Instant PN</CCol>
      <CCol>
        <div
          class="form-check form-check-inline"
          v-for="(option, optionIndex) in yesno_options"
          :key="option.value + optionIndex + 'instant'"
        >
          <input
            class="form-check-input"
            type="radio"
            :id="option.value + optionIndex + 'instant'"
            :value="option.value"
            v-model="pn.payload.instant"
            @change="$emit('set-instant')"
          />
          <label
            class="form-check-label"
            :for="option.value + optionIndex + 'instant'"
            >{{ option.label }}</label
          >
        </div>
      </CCol>
    </CRow>
    <br v-if="!isAuto" />
    <CRow v-if="!isAuto">
      <CCol>PN Title</CCol>
      <CCol>
        <div
          class="form-check form-check-inline"
          v-for="(option, optionIndex) in yesno_options"
          :key="option.value + optionIndex + 'desc'"
        >
          <input
            type="radio"
            class="form-check-input"
            :id="option.value + optionIndex + 'desc'"
            :value="option.value"
            v-model="pn.payload.has_title"
          />
          <label
            :for="option.value + optionIndex + 'desc'"
            class="form-check-label"
            >{{ option.label }}</label
          >
        </div>
      </CCol>
    </CRow>
    <br v-if="!isAuto" />
    <div class="form-group" v-if="pn.payload.has_title === '1' && !isAuto">
      <input
        v-model="pn.payload.title"
        id="title"
        class="form-control"
        :class="{ 'is-invalid': !isTitleValid }"
        placeholder="EN: 35 w/desc, 120 w/o desc; ZH 15 w/desc, 45 w/o desc"
      />
      <span
        id="characterLimit"
        class="form-text text-muted"
        v-text="pn.payload.title.length + ' Characters'"
      />
      <div v-if="!isTitleValid" class="invalid-feedback">
        Title is invalid. Backquote character("`") is not allowed.
      </div>
    </div>
    <div class="form-group" v-if="!isAuto">
      <label for="description">PN Description</label>
      <textarea
        :maxlength="body_max"
        v-model="pn.payload.body"
        id="description"
        class="form-control"
        :class="{ 'is-invalid': !isDescriptionValid }"
      />
      <span
        id="bodyLimit"
        class="form-text text-muted"
        v-text="pn.payload.body.length + ' Characters'"
      />
      <div v-if="!isDescriptionValid" class="invalid-feedback">
        Description is invalid. Backquote character("`") is not allowed.
      </div>
    </div>
    <CRow v-if="pn.payload.instant === '0' && !isAuto">
      <CCol> Feed </CCol>
      <CCol>
        <div
          class="form-check form-check-inline"
          v-for="(option, optionIndex) in feed_option_computed"
          :key="(option.value % 1) + optionIndex + 'feed'"
        >
          <input
            class="form-check-input"
            type="radio"
            :id="(option.value % 1) + optionIndex + 'feed'"
            :value="option.value"
            v-model="pn.payload.pnTopicId"
          />
          <label
            class="form-check-label"
            :for="(option.value % 1) + optionIndex + 'feed'"
            >{{ option.label }}</label
          >
        </div>
      </CCol>
    </CRow>
    <div v-if="pn.payload.instant === '0' && !isAuto">
      <br />
      <CRow>
        <CCol> OS </CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in os_options"
            :key="option.value + optionIndex + '_test_os'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="option.value + optionIndex + '_test_os'"
              :value="option.value"
              v-model="pn.payload.platform"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + '_test_os'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
    </div>
    <div v-if="pn.payload.instant === '0' && !isAuto">
      <br />
      <CRow>
        <CCol>Language</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in language_options"
            :key="option.value + optionIndex + 'lang'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="option.value + optionIndex + 'lang'"
              :value="option.value"
              v-model="pn.payload.language"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'lang'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
    </div>
    <div v-if="pn.payload.instant === '0' && !isAuto">
      <br />
      <CRow>
        <CCol>Match Sub-Language</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in yesno_options"
            :key="option.value + optionIndex + 'match'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="option.value + optionIndex + 'match'"
              :value="option.value"
              v-model="pn.payload.matchSubLang"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'match'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
    </div>
    <div>
      <br v-if="!isAuto" />
      <CRow>
        <CCol>PN Frequency</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in frequency_options"
            :key="option.value + optionIndex + 'pn'"
          >
            <input
              class="form-check-input"
              type="checkbox"
              :value="option.value"
              :id="option.value + optionIndex + 'pn'"
              v-model="pn.payload.pnPeriod"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'pn'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
    </div>
    <div v-if="pn.payload.instant === '0' && !isAuto">
      <br />
      <CRow>
        <CCol>Match Interest</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in yesno_options"
            :key="option.value + optionIndex + 'interest'"
          >
            <input
              class="form-check-input"
              type="radio"
              :value="option.value"
              :id="option.value + optionIndex + 'interest'"
              v-model="pn.payload.matchInterest"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'interest'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
    </div>
    <div v-if="pn.payload.instant === '0' && !isAuto">
      <br />
      <CRow>
        <CCol>Match Topic</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in replace_period"
            :key="option.value + optionIndex + 'topic'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="option.value + optionIndex + 'topic'"
              :value="option.value"
              v-model="pn.payload.ignoreSegment"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'topic'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
    </div>
    <div v-if="pn.payload.instant === '0' && !isAuto">
      <br />
      <CRow>
        <CCol>Replace Periodic</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in replace_period"
            :key="option.value + optionIndex + 'period'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="option.value + optionIndex + 'period'"
              :value="option.value"
              v-model="pn.payload.nonPreiodic"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'period'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
    </div>
    <div v-if="pn.payload.instant === '0' && !isAuto">
      <br />
      <CRow>
        <CCol>Skip Read Users</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in replace_period"
            :key="option.value + optionIndex + 'skip'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="option.value + optionIndex + 'skip'"
              :value="option.value"
              v-model="pn.payload.skipUserFeedView"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'skip'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
    </div>
    <div v-if="!isAuto">
      <br />
      <CRow>
        <CCol>Non-active Only</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in yesno_options"
            :key="option.value + optionIndex + 'active'"
          >
            <input
              type="radio"
              class="form-check-input"
              :id="option.value + optionIndex + 'active'"
              :value="option.value"
              v-model="pn.payload.nonActiveUsers"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'active'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
    </div>
    <div>
      <br />
      <CRow>
        <CCol>Schedule</CCol>
        <CCol>
          <vc-date-picker
            v-model="pn.scheduledPnTime"
            :min-date="today"
            :max-date="tomorrow"
            mode="dateTime"
            :masks="{ inputDateTime24hr: 'YYYY-MM-DD HH:mm' }"
            :model-config="{ type: 'string', mask: 'YYYY-MM-DD HH:mm' }"
            is24hr
          >
            <template v-slot="{ inputValue, inputEvents, togglePopover }">
              <div class="input-group mb-3">
                <div class="input-group-prepend">
                  <span class="input-group-text" @click="togglePopover">
                    <CIcon name="cil-calendar"></CIcon>
                  </span>
                </div>
                <input
                  class="form-control"
                  :value="inputValue"
                  v-on="inputEvents"
                />
              </div>
            </template>
          </vc-date-picker>
        </CCol>
      </CRow>
    </div>
    <OverlayLoader v-if="$store.getters.getScheduledLoading" />
  </div>
</template>

<script>
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';
import OverlayLoader from '@/components/views/OverlayLoader';
import format from 'date-fns/format';
import add from 'date-fns/add';
import set from 'date-fns/set';
export default {
  name: 'BaseScheduledPNForm',
  components: { OverlayLoader },
  props: {
    pn: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      base_en_max: 35,
      base_zh_max: 15,
      en_title_max: 120,
      zh_title_max: 45,
      body_max: 170,
      os_options: [
        { value: '', label: 'All' },
        { value: 'android', label: 'Android' },
        { value: 'ios', label: 'iOS' },
      ],
      language_options: [
        { value: 'en', label: 'EN' },
        { value: 'ms', label: 'MS' },
        { value: 'zh', label: 'ZH' },
      ],
      feed_options: [
        { value: '', label: 'All' },
        { value: 'publisher', label: 'Publisher' },
        { value: 'topic', label: 'Topic' },
      ],
      yesno_options: [
        { value: '1', label: 'Yes' },
        { value: '0', label: 'No' },
      ],
      replace_period: [
        { value: '0', label: 'Yes' },
        { value: '1', label: 'No' },
      ],
      frequency_options: [
        { value: '1', label: 'Low' },
        { value: '2', label: 'Medium' },
        { value: '3', label: 'High' },
      ],
      vid_language_options: [
        { value: 'en', label: 'EN' },
        { value: 'ms', label: 'MS' },
        { value: 'zh', label: 'ZH' },
        { value: '', label: 'All' },
      ],
      schedule_options: [
        { value: '0', label: 'Now' },
        { value: '1', label: 'Scheduled' },
      ],
    };
  },
  computed: {
    today() {
      return format(new Date(), "yyyy-MM-dd'T'HH:mm");
    },
    tomorrow() {
      return format(
        set(add(new Date(), { days: 3 }), {
          hours: 23,
          minutes: 59,
          seconds: 59,
        }),
        "yyyy-MM-dd'T'HH:mm"
      );
    },
    feed_option_computed() {
      return this.$store.getters.getFeedOptions;
    },
    isAuto() {
      return (
        this.pn.payload.articleId.startsWith('ATI_') ||
        this.pn.payload.articleId.startsWith('ATV_')
      );
    },
    isTitleValid() {
      return !this.pn.payload.title.includes('`');
    },
    isDescriptionValid() {
      return !this.pn.payload.body.includes('`');
    },
  },
};
</script>

<style scoped></style>
