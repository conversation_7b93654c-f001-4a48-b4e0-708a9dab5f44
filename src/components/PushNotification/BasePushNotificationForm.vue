<template>
  <div>
    <OverlayLoader v-if="pn_loading" />
    <div v-if="set === 'article'">
      <div v-if="!test">
        <CInput
          v-model="pn.articleId"
          label="Article ID"
          :is-valid="hasValue"
          invalid-feedback="Article ID is required"
          @input="onArticleIdInput"
        />
        <CButton
          v-if="
            titleSuggestions &&
            titleSuggestions.length > 0 &&
            descriptionSuggestions.length > 0
          "
          color="info"
          shape="pill"
          class="refresh-button"
          size="sm"
          @click="getPnTitleAndDescriptionSuggestions"
          :disabled="pn_loading"
        >
          <CIcon name="cil-sync" /> Refresh Suggestions
        </CButton>
        <br />
        <CRow v-if="!isAuto">
          <CCol>Instant PN</CCol>
          <CCol>
            <div
              class="form-check form-check-inline"
              v-for="(option, optionIndex) in yesno_options"
              :key="option.value + optionIndex + 'instant'"
            >
              <input
                class="form-check-input"
                type="radio"
                :id="option.value + optionIndex + 'instant'"
                :value="option.value"
                v-model="pn.instant"
                @change="$emit('set-instant')"
              />
              <label
                class="form-check-label"
                :for="option.value + optionIndex + 'instant'"
                >{{ option.label }}</label
              >
            </div>
          </CCol>
        </CRow>
        <br v-if="!isAuto" />
        <CRow v-if="!isAuto">
          <CCol>PN Title</CCol>
          <CCol>
            <div
              class="form-check form-check-inline"
              v-for="(option, optionIndex) in yesno_options"
              :key="option.value + optionIndex + 'desc'"
            >
              <input
                type="radio"
                class="form-check-input"
                :id="option.value + optionIndex + 'desc'"
                :value="option.value"
                v-model="pn.has_title"
              />
              <label
                :for="option.value + optionIndex + 'desc'"
                class="form-check-label"
                >{{ option.label }}</label
              >
              <div class="title-suggestions mt-2" v-if="titleSuggestions"></div>
            </div>
          </CCol>
        </CRow>
        <br v-if="!isAuto" />
        <div class="form-group" v-if="pn.has_title === '1' && !isAuto">
          <input
            v-model="pn.title"
            id="title"
            class="form-control"
            :class="{ 'is-invalid': !isTitleValid }"
            placeholder="EN: 35 w/desc, 120 w/o desc; ZH 15 w/desc, 45 w/o desc"
          />
          <span
            id="characterLimit"
            class="form-text text-muted"
            v-text="pn.title.length + ' Characters'"
          />
          <div v-if="!isTitleValid" class="invalid-feedback">
            Title is invalid. Backquote character("`") is not allowed.
          </div>
          <div class="d-flex flex-wrap">
            <CBadge
              v-for="(suggestion, index) in titleSuggestions"
              :key="'title-' + index"
              color="info"
              class="suggestion-badge mr-2 mb-2"
              @click="fillTitle(suggestion)"
            >
              {{ suggestion }}
            </CBadge>
          </div>
        </div>
        <div class="form-group" v-if="!isAuto">
          <label for="description">PN Description</label>
          <textarea
            :maxlength="body_max"
            v-model="pn.body"
            id="description"
            class="form-control"
            :class="{ 'is-invalid': !isDescriptionValid }"
          />
          <span
            id="bodyLimit"
            class="form-text text-muted"
            v-text="pn.body.length + ' Characters'"
          />
          <div v-if="!isDescriptionValid" class="invalid-feedback">
            Description is invalid. Backquote character("`") is not allowed.
          </div>
          <div
            class="description-suggestions mt-2"
            v-if="descriptionSuggestions"
          >
            <div class="d-flex flex-wrap">
              <CBadge
                v-for="(suggestion, index) in descriptionSuggestions"
                :key="'desc-' + index"
                color="info"
                class="suggestion-badge mr-2 mb-2"
                @click="fillDescription(suggestion)"
              >
                {{ suggestion }}
              </CBadge>
            </div>
          </div>
        </div>
        <CRow v-if="pn.instant === '0' && !isAuto">
          <CCol> Feed </CCol>
          <CCol>
            <div
              class="form-check form-check-inline"
              v-for="(option, optionIndex) in feed_option_computed"
              :key="(option.value % 1) + optionIndex + 'feed'"
            >
              <input
                class="form-check-input"
                type="radio"
                :id="(option.value % 1) + optionIndex + 'feed'"
                :value="option.value"
                v-model="pn.pnTopicId"
              />
              <label
                class="form-check-label"
                :for="(option.value % 1) + optionIndex + 'feed'"
                >{{ option.label }}</label
              >
            </div>
          </CCol>
        </CRow>
        <div v-if="pn.instant === '0' && !isAuto">
          <br />
          <CRow>
            <CCol> OS </CCol>
            <CCol class="multi">
              <div
                class="choices form-check form-check-inline"
                v-for="(option, optionIndex) in test
                  ? test_os_options
                  : os_options"
                :key="option.value + optionIndex + '_test_os'"
              >
                <input
                  class="form-check-input"
                  type="radio"
                  :id="option.value + optionIndex + '_test_os'"
                  :value="option.value"
                  v-model="pn.platform"
                />
                <label
                  class="form-check-label"
                  :for="option.value + optionIndex + '_test_os'"
                  >{{ option.label }}</label
                >
              </div>
            </CCol>
          </CRow>
        </div>
        <div v-if="!isAuto">
          <br v-if="pn.instant === '0' && !isAuto" />
          <CRow>
            <CCol>Language</CCol>
            <CCol class="multi">
              <div
                class="choices form-check form-check-inline"
                v-for="(option, optionIndex) in language_options"
                :key="option.value + optionIndex + 'lang'"
              >
                <input
                  class="form-check-input"
                  type="radio"
                  :id="option.value + optionIndex + 'lang'"
                  :value="option.value"
                  v-model="pn.language"
                />
                <label
                  class="form-check-label"
                  :for="option.value + optionIndex + 'lang'"
                  >{{ option.label }}</label
                >
              </div>
            </CCol>
          </CRow>
        </div>
        <div v-if="pn.instant === '0' && !isAuto">
          <br />
          <CRow>
            <CCol>Match Sub-Language</CCol>
            <CCol>
              <div
                class="form-check form-check-inline"
                v-for="(option, optionIndex) in yesno_options"
                :key="option.value + optionIndex + 'match'"
              >
                <input
                  class="form-check-input"
                  type="radio"
                  :id="option.value + optionIndex + 'match'"
                  :value="option.value"
                  v-model="pn.matchSubLang"
                />
                <label
                  class="form-check-label"
                  :for="option.value + optionIndex + 'match'"
                  >{{ option.label }}</label
                >
              </div>
            </CCol>
          </CRow>
        </div>
        <div>
          <br v-if="!isAuto" />
          <CRow>
            <CCol>PN Frequency</CCol>
            <CCol class="multi">
              <div
                class="choices form-check form-check-inline"
                v-for="(option, optionIndex) in frequency_options"
                :key="option.value + optionIndex + 'pn'"
              >
                <input
                  class="form-check-input"
                  type="checkbox"
                  :value="option.value"
                  :id="option.value + optionIndex + 'pn'"
                  v-model="pn.pnPeriod"
                />
                <label
                  class="form-check-label"
                  :for="option.value + optionIndex + 'pn'"
                  >{{ option.label }}</label
                >
              </div>
            </CCol>
          </CRow>
        </div>
        <div v-if="pn.instant === '0' && !isAuto">
          <br />
          <CRow>
            <CCol>Match Interest</CCol>
            <CCol>
              <div
                class="form-check form-check-inline"
                v-for="(option, optionIndex) in yesno_options"
                :key="option.value + optionIndex + 'interest'"
              >
                <input
                  class="form-check-input"
                  type="radio"
                  :value="option.value"
                  :id="option.value + optionIndex + 'interest'"
                  v-model="pn.matchInterest"
                />
                <label
                  class="form-check-label"
                  :for="option.value + optionIndex + 'interest'"
                  >{{ option.label }}</label
                >
              </div>
            </CCol>
          </CRow>
        </div>
        <div v-if="pn.instant === '0' && !isAuto">
          <br />
          <CRow>
            <CCol>Match Topic</CCol>
            <CCol>
              <div
                class="form-check form-check-inline"
                v-for="(option, optionIndex) in replace_period"
                :key="option.value + optionIndex + 'topic'"
              >
                <input
                  class="form-check-input"
                  type="radio"
                  :id="option.value + optionIndex + 'topic'"
                  :value="option.value"
                  v-model="pn.ignoreSegment"
                />
                <label
                  class="form-check-label"
                  :for="option.value + optionIndex + 'topic'"
                  >{{ option.label }}</label
                >
              </div>
            </CCol>
          </CRow>
        </div>
        <div v-if="pn.instant === '0' && !isAuto">
          <br />
          <CRow>
            <CCol>Replace Periodic</CCol>
            <CCol>
              <div
                class="form-check form-check-inline"
                v-for="(option, optionIndex) in replace_period"
                :key="option.value + optionIndex + 'period'"
              >
                <input
                  class="form-check-input"
                  type="radio"
                  :id="option.value + optionIndex + 'period'"
                  :value="option.value"
                  v-model="pn.nonPreiodic"
                />
                <label
                  class="form-check-label"
                  :for="option.value + optionIndex + 'period'"
                  >{{ option.label }}</label
                >
              </div>
            </CCol>
          </CRow>
        </div>
        <div v-if="pn.instant === '0' && !isAuto">
          <br />
          <CRow>
            <CCol>Skip Read Users</CCol>
            <CCol>
              <div
                class="form-check form-check-inline"
                v-for="(option, optionIndex) in replace_period"
                :key="option.value + optionIndex + 'skip'"
              >
                <input
                  class="form-check-input"
                  type="radio"
                  :id="option.value + optionIndex + 'skip'"
                  :value="option.value"
                  v-model="pn.skipUserFeedView"
                />
                <label
                  class="form-check-label"
                  :for="option.value + optionIndex + 'skip'"
                  >{{ option.label }}</label
                >
              </div>
            </CCol>
          </CRow>
        </div>
        <div v-if="!isAuto">
          <br />
          <CRow>
            <CCol>Non-active Only</CCol>
            <CCol>
              <div
                class="form-check form-check-inline"
                v-for="(option, optionIndex) in yesno_options"
                :key="option.value + optionIndex + 'active'"
              >
                <input
                  type="radio"
                  class="form-check-input"
                  :id="option.value + optionIndex + 'active'"
                  :value="option.value"
                  v-model="pn.nonActiveUsers"
                />
                <label
                  class="form-check-label"
                  :for="option.value + optionIndex + 'active'"
                  >{{ option.label }}</label
                >
              </div>
            </CCol>
          </CRow>
        </div>
        <div>
          <br />
          <CRow>
            <CCol>Schedule</CCol>
            <CCol>
              <div
                class="form-check form-check-inline"
                v-for="(option, optionIndex) in schedule_options"
                :key="option.value + optionIndex + 'schedule'"
              >
                <input
                  type="radio"
                  class="form-check-input"
                  :id="option.value + optionIndex + 'schedule'"
                  :value="option.value"
                  v-model="pn.scheduled"
                />
                <label
                  class="form-check-label"
                  :for="option.value + optionIndex + 'schedule'"
                  >{{ option.label }}</label
                >
              </div>
            </CCol>
          </CRow>
        </div>
        <div v-if="pn.scheduled === '1'">
          <CRow>
            <CCol></CCol>
            <CCol>
              <br />
              <vc-date-picker
                v-model="pn.scheduledPnTime"
                :min-date="today"
                :max-date="tomorrow"
                mode="dateTime"
                :masks="{ inputDateTime24hr: 'YYYY-MM-DD HH:mm' }"
                :model-config="{ type: 'string', mask: 'YYYY-MM-DD HH:mm' }"
                is24hr
              >
                <template v-slot="{ inputValue, inputEvents, togglePopover }">
                  <div class="input-group mb-3">
                    <div class="input-group-prepend">
                      <span class="input-group-text" @click="togglePopover">
                        <CIcon name="cil-calendar"></CIcon>
                      </span>
                    </div>
                    <input
                      class="form-control"
                      :value="inputValue"
                      v-on="inputEvents"
                    />
                  </div>
                </template>
              </vc-date-picker>
            </CCol>
          </CRow>
        </div>
      </div>
      <div v-else>
        <CInput
          v-model="pn.articleId"
          label="Unique ID"
          :is-valid="hasValue"
          invalid-feedback="Unique ID is required"
        />
        <CRow>
          <CCol>PN Title</CCol>
          <CCol>
            <div
              class="form-check form-check-inline"
              v-for="(option, optionIndex) in yesno_options"
              :key="option.value + optionIndex + 'desc'"
            >
              <input
                type="radio"
                class="form-check-input"
                :id="option.value + optionIndex + 'desc'"
                :value="option.value"
                v-model="pn.has_title"
              />
              <label
                :for="option.value + optionIndex + 'desc'"
                class="form-check-label"
                >{{ option.label }}</label
              >
            </div>
          </CCol>
        </CRow>
        <br />
        <div class="form-group" v-if="pn.has_title === '1'">
          <input
            v-model="pn.title"
            id="title_test"
            class="form-control"
            :class="{ 'is-invalid': !isTitleValid }"
            placeholder="EN: 35 w/desc, 120 w/o desc; ZH 15 w/desc, 45 w/o desc"
          />
          <span
            id="characterLimit_test"
            class="form-text text-muted"
            v-text="pn.title.length + ' Characters'"
          />
          <div v-if="!isTitleValid" class="invalid-feedback">
            Title is invalid. Backquote character("`") is not allowed.
          </div>
        </div>
        <div class="form-group">
          <label for="description_test">PN Description</label>
          <textarea
            :maxlength="body_max"
            v-model="pn.body"
            id="description"
            class="form-control"
            :class="{ 'is-invalid': !isDescriptionValid }"
          />
          <span
            id="bodyLimit_test"
            class="form-text text-muted"
            v-text="pn.body.length + ' Characters'"
          />
          <div v-if="!isDescriptionValid" class="invalid-feedback">
            Description is invalid. Backquote character("`") is not allowed.
          </div>
        </div>
        <CRow>
          <CCol> OS </CCol>
          <CCol>
            <div
              class="form-check form-check-inline"
              v-for="(option, optionIndex) in test
                ? test_os_options
                : os_options"
              :key="option.value + optionIndex + '_test_os'"
            >
              <input
                class="form-check-input"
                type="radio"
                :id="option.value + optionIndex + '_test_os'"
                :value="option.value"
                v-model="pn.platform"
              />
              <label
                class="form-check-label"
                :for="option.value + optionIndex + '_test_os'"
                >{{ option.label }}</label
              >
            </div>
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol>
            <CInput
              v-model="pn.profileId"
              label="Profile-ID"
              :is-valid="hasValue"
              invalid-feedback="Profile ID is required"
            />
          </CCol>
        </CRow>
      </div>
    </div>
    <div v-else-if="set === 'video'">
      <CInput
        v-model="pn.videoId"
        label="Video ID"
        :is-valid="hasValue"
        invalid-feedback="Video ID is required"
      />
      <CRow>
        <CCol>PN Title</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in yesno_options"
            :key="option.value + optionIndex + 'vid'"
          >
            <input
              type="radio"
              class="form-check-input"
              :id="option.value + optionIndex + 'vid'"
              :value="option.value"
              v-model="pn.has_title"
            />
            <label
              :for="option.value + optionIndex + 'vid'"
              class="form-check-label"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
      <br />
      <div class="form-group" v-if="pn.has_title === '1'">
        <input
          v-model="pn.title"
          id="title_vid"
          class="form-control"
          placeholder="EN: 35 w/desc, 120 w/o desc; ZH 15 w/desc, 45 w/o desc"
        />
        <span
          id="characterLimit_vid"
          class="form-text text-muted"
          v-text="pn.title.length + ' Characters'"
        />
      </div>
      <div class="form-group">
        <label for="description">PN Description</label>
        <textarea
          :maxlength="body_max"
          v-model="pn.body"
          id="description_vid"
          class="form-control"
        />
        <span
          id="bodyLimit_vid"
          class="form-text text-muted"
          v-text="pn.body.length + ' Characters'"
        />
      </div>
      <div>
        <CRow>
          <CCol> OS </CCol>
          <CCol>
            <div
              class="form-check form-check-inline"
              v-for="(option, optionIndex) in test
                ? test_os_options
                : os_options"
              :key="option.value + optionIndex + '_test_os'"
            >
              <input
                class="form-check-input"
                type="radio"
                :id="option.value + optionIndex + '_test_os'"
                :value="option.value"
                v-model="pn.platform"
              />
              <label
                class="form-check-label"
                :for="option.value + optionIndex + '_test_os'"
                >{{ option.label }}</label
              >
            </div>
          </CCol>
        </CRow>
        <br />
      </div>
      <CRow>
        <CCol>Language</CCol>
        <CCol class="multi">
          <div
            class="choices form-check form-check-inline"
            v-for="(option, optionIndex) in vid_language_options"
            :key="option.value + optionIndex + 'vid_lang'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="option.value + optionIndex + 'vid_lang'"
              :value="option.value"
              v-model="pn.language"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'vid_lang'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
      <div v-if="pn.language !== ''">
        <br />
        <CRow>
          <CCol>Match Sub-Language</CCol>
          <CCol>
            <div
              class="form-check form-check-inline"
              v-for="(option, optionIndex) in yesno_options"
              :key="option.value + optionIndex + 'match'"
            >
              <input
                class="form-check-input"
                type="radio"
                :id="option.value + optionIndex + 'match'"
                :value="option.value"
                v-model="pn.includeSubLanguage"
              />
              <label
                class="form-check-label"
                :for="option.value + optionIndex + 'match'"
                >{{ option.label }}</label
              >
            </div>
          </CCol>
        </CRow>
      </div>
      <br />
      <CRow>
        <CCol>PN Frequency</CCol>
        <CCol class="multi">
          <div
            class="choices form-check form-check-inline"
            v-for="(option, optionIndex) in frequency_options"
            :key="option.value + optionIndex + 'pn'"
          >
            <input
              class="form-check-input"
              type="checkbox"
              :value="option.value"
              :id="option.value + optionIndex + 'pn'"
              v-model="pn.pnPeriod"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'pn'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
      <div>
        <br />
        <CRow>
          <CCol>Non-active Only</CCol>
          <CCol>
            <div
              class="form-check form-check-inline"
              v-for="(option, optionIndex) in yesno_options"
              :key="option.value + optionIndex + 'active'"
            >
              <input
                type="radio"
                class="form-check-input"
                :id="option.value + optionIndex + 'active'"
                :value="option.value"
                v-model="pn.nonActiveUsers"
              />
              <label
                class="form-check-label"
                :for="option.value + optionIndex + 'active'"
                >{{ option.label }}</label
              >
            </div>
          </CCol>
        </CRow>
      </div>
    </div>
    <div v-else>
      <CInput
        v-model="pn.podcastId"
        label="Podcast ID"
        :is-valid="hasValue"
        invalid-feedback="Podcast ID is required"
      />
      <CRow>
        <CCol>PN Title</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in yesno_options"
            :key="option.value + optionIndex + 'vid'"
          >
            <input
              type="radio"
              class="form-check-input"
              :id="option.value + optionIndex + 'vid'"
              :value="option.value"
              v-model="pn.has_title"
            />
            <label
              :for="option.value + optionIndex + 'vid'"
              class="form-check-label"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
      <br />
      <div class="form-group" v-if="pn.has_title === '1'">
        <input
          v-model="pn.title"
          id="title_pod"
          class="form-control"
          placeholder="EN: 35 w/desc, 120 w/o desc; ZH 15 w/desc, 45 w/o desc"
        />
        <span
          id="characterLimit_pod"
          class="form-text text-muted"
          v-text="pn.title.length + ' Characters'"
        />
      </div>
      <div class="form-group">
        <label for="description">PN Description</label>
        <textarea
          :maxlength="body_max"
          v-model="pn.body"
          id="description_pod"
          class="form-control"
        />
        <span
          id="bodyLimit_pod"
          class="form-text text-muted"
          v-text="pn.body.length + ' Characters'"
        />
      </div>
      <div>
        <CRow>
          <CCol> OS </CCol>
          <CCol class="multi">
            <div
              class="choices form-check form-check-inline"
              v-for="(option, optionIndex) in test
                ? test_os_options
                : os_options"
              :key="option.value + optionIndex + '_test_os'"
            >
              <input
                class="form-check-input"
                type="radio"
                :id="option.value + optionIndex + '_test_os'"
                :value="option.value"
                v-model="pn.platform"
              />
              <label
                class="form-check-label"
                :for="option.value + optionIndex + '_test_os'"
                >{{ option.label }}</label
              >
            </div>
          </CCol>
        </CRow>
        <br />
      </div>
      <CRow>
        <CCol>Language</CCol>
        <CCol class="multi">
          <div
            class="choices form-check form-check-inline"
            v-for="(option, optionIndex) in vid_language_options"
            :key="option.value + optionIndex + 'vid_lang'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="option.value + optionIndex + 'vid_lang'"
              :value="option.value"
              v-model="pn.language"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'vid_lang'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
      <div v-if="pn.language !== ''">
        <br />
        <CRow>
          <CCol>Match Sub-Language</CCol>
          <CCol>
            <div
              class="form-check form-check-inline"
              v-for="(option, optionIndex) in yesno_options"
              :key="option.value + optionIndex + 'match'"
            >
              <input
                class="form-check-input"
                type="radio"
                :id="option.value + optionIndex + 'match'"
                :value="option.value"
                v-model="pn.includeSubLanguage"
              />
              <label
                class="form-check-label"
                :for="option.value + optionIndex + 'match'"
                >{{ option.label }}</label
              >
            </div>
          </CCol>
        </CRow>
      </div>
      <br />
      <CRow>
        <CCol>PN Frequency</CCol>
        <CCol class="multi">
          <div
            class="choices form-check form-check-inline"
            v-for="(option, optionIndex) in frequency_options"
            :key="option.value + optionIndex + 'pn'"
          >
            <input
              class="form-check-input"
              type="checkbox"
              :value="option.value"
              :id="option.value + optionIndex + 'pn'"
              v-model="pn.pnPeriod"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'pn'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
    </div>
  </div>
</template>

<script>
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';
import { isAutoPn } from '@/helpers/pn';
import OverlayLoader from '../views/OverlayLoader';

export default {
  name: 'BasePushNotificationForm',
  components: { OverlayLoader },
  props: {
    test: {
      type: Boolean,
      default: false,
    },
    pn: {
      type: Object,
      required: true,
    },
    set: {
      type: String,
    },
  },
  data() {
    return {
      base_en_max: 35,
      base_zh_max: 15,
      en_title_max: 120,
      zh_title_max: 45,
      body_max: 170,
      os_options: [
        { value: '', label: 'All' },
        { value: 'android', label: 'Android' },
        { value: 'ios', label: 'iOS' },
      ],
      test_os_options: [
        { value: 'android', label: 'Android' },
        { value: 'ios', label: 'iOS' },
      ],
      language_options: [
        { value: 'en', label: 'EN' },
        { value: 'ms', label: 'MS' },
        { value: 'zh', label: 'ZH' },
      ],
      feed_options: [
        { value: '', label: 'All' },
        { value: 'publisher', label: 'Publisher' },
        { value: 'topic', label: 'Topic' },
      ],
      yesno_options: [
        { value: '1', label: 'Yes' },
        { value: '0', label: 'No' },
      ],
      replace_period: [
        { value: '0', label: 'Yes' },
        { value: '1', label: 'No' },
      ],
      frequency_options: [
        { value: '1', label: 'Low' },
        { value: '2', label: 'Medium' },
        { value: '3', label: 'High' },
      ],
      vid_language_options: [
        { value: 'en', label: 'EN' },
        { value: 'ms', label: 'MS' },
        { value: 'zh', label: 'ZH' },
        { value: '', label: 'All' },
      ],
      schedule_options: [
        { value: '0', label: 'Now' },
        { value: '1', label: 'Scheduled' },
      ],
      suggestionTimer: null,
    };
  },
  mounted() {
    this.$store.dispatch('listFeed');
    this.resetSuggestions();
  },
  computed: {
    today() {
      let date = new Date();
      let yy = date.getFullYear();
      let mm = new Intl.DateTimeFormat('en', { month: '2-digit' }).format(date);
      let dd = new Intl.DateTimeFormat('en', { day: '2-digit' }).format(date);
      return `${yy}-${mm}-${dd}`;
    },
    tomorrow() {
      let date = new Date();
      date.setDate(date.getDate() + 3);
      let yy = date.getFullYear();
      let mm = new Intl.DateTimeFormat('en', { month: '2-digit' }).format(date);
      let dd = new Intl.DateTimeFormat('en', { day: '2-digit' }).format(date);
      return `${yy}-${mm}-${dd}`;
    },
    pn_loading() {
      return this.$store.getters.getPNLoading;
    },
    article_loading() {
      return this.$store.getters.getArticleLoading;
    },
    feed_option_computed() {
      return this.$store.getters.getFeedOptions;
    },
    title_length() {
      if (this.pn.language === 'zh') {
        if (this.pn.body.length === 0) {
          return this.zh_title_max;
        } else {
          return this.base_zh_max;
        }
      } else {
        if (this.pn.body.length === 0) {
          return this.en_title_max;
        } else {
          return this.base_en_max;
        }
      }
    },
    isAuto() {
      return isAutoPn(this.pn.articleId);
    },
    isTitleValid() {
      return !this.pn.title.includes('`');
    },
    isDescriptionValid() {
      return !this.pn.body.includes('`');
    },
    titleSuggestions() {
      return this.$store.getters.getTitleSuggestions;
    },
    descriptionSuggestions() {
      return this.$store.getters.getDescriptionSuggestions;
    },
  },
  methods: {
    getArticle() {
      this.$store
        .dispatch('getArticleData', this.pn.articleId)
        .then((res) => {
          let article = res.data;
          this.pn.title = article.title;
          this.pn.body = article.description;
        })
        .catch((err) => {
          alert(err.data.message);
        });
    },
    resetSuggestions() {
      this.$store.commit('SET_TITLE_SUGGESTIONS', []);
      this.$store.commit('SET_DESCRIPTION_SUGGESTIONS', []);

      if (this.suggestionTimer) {
        clearTimeout(this.suggestionTimer);
        this.suggestionTimer = null;
      }
    },
    onArticleIdInput() {
      if (this.suggestionTimer) {
        clearTimeout(this.suggestionTimer);
      }
      if (this.pn.articleId.length === 0) {
        this.resetSuggestions();
        return;
      }
      this.suggestionTimer = setTimeout(() => {
        if (this.pn.articleId.length === 0) {
          this.$store.commit('SET_TITLE_SUGGESTIONS', []);
          this.$store.commit('SET_DESCRIPTION_SUGGESTIONS', []);
          return;
        }
        this.getPnTitleAndDescriptionSuggestions();
      }, 1000);
    },
    hasValue(val) {
      return val ? val.length > 0 : false;
    },
    getPnTitleAndDescriptionSuggestions() {
      this.$store.dispatch(
        'getTitleAndDescriptionSuggestions',
        this.pn.articleId
      );
    },
    fillTitle(suggestion) {
      this.pn.title = suggestion;
    },
    fillDescription(suggestion) {
      this.pn.body = suggestion;
    },
  },
};
</script>

<style scoped>
@media (max-width: 750px) {
  .col {
    display: flex;
    justify-content: space-between;
  }
  .multi.col {
    flex-direction: column;
  }
  .choices {
    margin-bottom: 8px;
  }
}

.suggestion-badge {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.suggestion-badge:hover {
  opacity: 0.8;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.refresh-button {
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: bold;
}
</style>
