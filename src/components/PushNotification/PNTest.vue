<template>
  <div>
    <CForm @submit.prevent="testPush">
      <CCard>
        <CCardHeader>Test Push Notification</CCardHeader>
        <CCardBody>
          <BasePushNotificationForm
            :pn="pn"
            :test="true"
            :set="'article'"
            @set-instant="prefillInstant"
          />
          <br /><br />
          <div v-show="checkAction('pn_test_create')">
            <CButton
              block
              color="success"
              variant="outline"
              type="submit"
              :disabled="is_disabled"
              >Send</CButton
            >
          </div>
        </CCardBody>
      </CCard>
    </CForm>
  </div>
</template>

<script>
import BasePushNotificationForm from './BasePushNotificationForm';

export default {
  name: 'PNTest',
  components: { BasePushNotificationForm },
  data() {
    return {
      pn: {
        articleId: '',
        title: '',
        body: '',
        platform: '',
        postfix: '',
        matchSubLang: '0',
        language: '',
        ignoreSegment: '1',
        pnPeriod: ['1', '2', '3'],
        skipUserFeedView: '0',
        nonPreiodic: '1',
        profileId: '',
        instant: '0',
        pnTopicId: '',
        // dryMode : '1'
        has_title: '1',
      },
    };
  },
  computed: {
    is_disabled() {
      let disabled = [];
      Object.entries(this.pn).forEach(([prop, val]) => {
        if (prop === 'articleId' && val === '') {
          disabled.push('articleID');
        }
        if (prop === 'profileId' && val === '') {
          disabled.push('profileID');
        }
        if (prop === 'platform' && val === '') {
          disabled.push('platform');
        }
        if (prop === 'title' && val.includes('`')) {
          disabled.push('title');
        }
        if (prop === 'body' && val.includes('`')) {
          disabled.push('body');
        }
      });
      return disabled.length > 0;
    },
  },
  methods: {
    async testPush() {
      if (this.checkAction('pn_test_create')) {
        let msg =
          'This will send a notification with the current settings. Continue?';
        if (confirm(msg)) {
          if (this.pn.has_title === '0') {
            this.pn.title = ' ';
          }
          let newPn = {};
          Object.entries(this.pn).forEach(([prop, val]) => {
            if (val !== '' && val !== '0' && prop !== 'has_title') {
              newPn[prop] = val;
            }
          });
          newPn.postfix = await this.generatePostfix();
          newPn.pnPeriod = newPn.pnPeriod.join();
          newPn.profileId =
            this.pn.platform.slice(0, 1) + '_' + this.pn.profileId;
          if (newPn.articleId.charAt(0) === 'V') {
            newPn.videoId = newPn.articleId;
            await this.$store.dispatch('sendTestVideoPN', newPn);
          } else if (newPn.articleId.charAt(0) === 'P') {
            newPn.podcastId = newPn.articleId;
            await this.$store.dispatch('sendTestPodcastPN', newPn);
          } else {
            await this.$store.dispatch('sendTestPush', newPn);
          }
        }
      } else {
        this.$notify({
          group: 'error',
          title: 'Unauthorized',
          type: 'error',
          text: 'No permission to send test pn',
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
      }
    },
    generatePostfix() {
      let randomChars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      while (!/^(?=.*[A-Z])(?=.*\d).{6,}$/.test(result)) {
        result = '';
        for (let i = 0; i < 6; i++) {
          result += randomChars.charAt(
            Math.floor(Math.random() * randomChars.length)
          );
        }
      }
      return result;
    },
    prefillInstant() {
      this.pn.platform = '';
      this.pn.nonPreiodic = '1';
      this.pn.pnTopicId = '';
      this.pn.skipUserFeedView = '0';
      this.pn.matchSubLang = '0';
      this.pn.ignoreSegment = '1';
    },
  },
};
</script>

<style scoped></style>
