<template>
  <div>
    <CCard>
      <CCardHeader>Scheduled PNs</CCardHeader>
      <CCardBody>
        <CDataTable
          :loading="loading"
          :key="scheduled"
          :items="scheduled"
          :fields="schedule_fields"
          column-filter
          :items-per-page="10"
        >
          <template #uniqueID="{ item }">
            <td>{{ item.payload.articleId }}</td>
          </template>
          <template #languageShow="{ item }">
            <td>{{ item.languageShow | uppercase }}</td>
          </template>
          <template #actions="{ item }">
            <td>
              <CButtonToolbar>
                <CButton
                  variant="outline"
                  color="warning"
                  v-c-tooltip="'Edit'"
                  @click="$router.push('pn-scheduled/edit-pn/' + item.id)"
                >
                  <CIcon name="cil-pencil"></CIcon>
                </CButton>
                <CButton
                  variant="outline"
                  color="danger"
                  v-c-tooltip="'Delete'"
                  @click="deletePN(item)"
                >
                  <CIcon name="cil-trash"></CIcon>
                </CButton>
              </CButtonToolbar>
            </td>
          </template>
        </CDataTable>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import { getCancelScheduleText } from '@/helpers/pn';
export default {
  name: 'PNSchedule',
  data() {
    return {
      schedule_fields: [
        { key: 'send_at', label: 'Send At' },
        { key: 'uniqueID', label: 'Unique ID' },
        { key: 'languageShow', label: 'Language' },
        { key: 'match_sub_lang', label: 'Match Sub-lang' },
        { key: 'pnPeriodShow', label: 'PN Freq' },
        { key: 'actions', label: 'Actions', sorter: false, filter: false },
      ],
    };
  },
  filters: {
    uppercase(val) {
      return val.toUpperCase();
    },
  },
  created() {
    this.$store.dispatch('listScheduledPN');
  },
  computed: {
    scheduled() {
      return this.$store.getters.getScheduledPNs;
    },
    loading() {
      return this.$store.getters.getPNLoading;
    },
  },
  methods: {
    deletePN(pn) {
      const text = getCancelScheduleText(pn);
      this.$swal
        .fire({
          icon: 'question',
          text: text,
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            let passPN = JSON.parse(JSON.stringify(pn));
            let passObj = {};
            Object.entries(passPN).forEach(([prop, val]) => {
              passObj[prop] = val;
            });
            passObj.status = 'cancelled';
            passObj.payload.pnPeriod = passPN.payload.pnPeriod.sort().join(',');
            this.$store.commit('SET_PN_LOADING', true);
            this.$store
              .dispatch('updateScheduledPN', passObj)
              .then(() => {
                this.$store.commit('SET_PN_LOADING', false);
              })
              .catch(() => {
                this.$store.commit('SET_PN_LOADING', false);
              });
          }
        });
    },
  },
};
</script>

<style scoped></style>
