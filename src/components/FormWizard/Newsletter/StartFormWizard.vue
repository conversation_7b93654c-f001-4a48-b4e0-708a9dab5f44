<template>
  <div>
    <CRow>
      <CCol>
        <CButton variant="outline" shape="pill" color="primary" @click="goBack"
          >Back</CButton
        >
      </CCol>
    </CRow>
    <br />
    <CRow>
      <CCol>
        <CCard>
          <CCardBody>
            <form-wizard
              :title="'Create Newsletter'"
              :subtitle="''"
              @on-complete="redirectTo"
              color="#2eb85c"
            >
              <tab-content
                title="Newsletter Details"
                :before-change="createNewsletter"
              >
                <NewsletterForm :newsletter="newsletter" />
              </tab-content>
              <tab-content title="Newsletter Articles">
                <MainArticleForm
                  :created_newsletter="createdNewsletter"
                  :main_article="article"
                  :created_list="created_articles"
                  :display="display"
                  @push-to-create="createArticleNewsletter"
                  @show-display="setDisplay"
                />
              </tab-content>
              <tab-content title="Full Review">
                <NewsletterFullPreview
                  :newsletter="createdNewsletter"
                  :article_list="created_articles"
                />
              </tab-content>
            </form-wizard>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import MainArticleForm from './MainArticleForm';
import NewsletterForm from './NewsletterForm';
import axios from 'axios';
import NewsletterFullPreview from './NewsletterFullPreview';
import * as headers from '@/helpers/headers';
export default {
  name: 'StartFormWizard',
  components: { NewsletterFullPreview, NewsletterForm, MainArticleForm },
  data() {
    return {
      display: null,
      createdNewsletter: {},
      created_articles: [],
      newsletter: {
        title: '',
        description: '',
        language: '',
        date: '',
      },
      article: {
        heading: 'Breaking News',
        order: '',
        title: '',
        description: '',
        article_link: '',
        imageURL: '',
        publisher_name: '',
        article_unique_ID: '',
        newsletter_id: '',
      },
      config: {
        headers: {
          token: process.env.VUE_APP_TOKEN_VALUE,
        },
      },
    };
  },
  methods: {
    goBack() {
      this.$router.push('/newsletter');
    },
    setDisplay(item) {
      this.display = item.url;
    },
    validateInputs(object) {
      let pass = true;
      Object.keys(object).forEach(function (key, val) {
        if (key !== 'description') {
          if (val === '') {
            pass = false;
            return pass;
          }
        }
      });
      return pass;
    },
    createNewsletter() {
      if (JSON.stringify(this.createdNewsletter) == '{}') {
        if (this.validateInputs(this.newsletter)) {
          let success = true;
          axios
            .post(
              process.env.VUE_APP_CREATE_NEWSLETTER,
              this.newsletter,
              headers.createHeaders(
                this.$store.getters.getUserToken,
                process.env.VUE_APP_TOKEN_VALUE
              )
            )
            .then((res) => {
              this.createdNewsletter = res.data;
              console.log(this.createdNewsletter);
            })
            .catch((err) => {
              alert('Something went wrong');
              console.log('---Error---');
              console.log(err);
              success = false;
            });
          return success;
        } else {
          alert('Please fill in all the details before proceeding');
          return false;
        }
      } else {
        return true;
      }
    },
    async createArticleNewsletter() {
      this.article.newsletter_id = this.createdNewsletter.id;
      if (this.display) {
        if (this.display.includes('newsletter/temp/')) {
          await this.moveGoogleFile(this.display);
        }
      }
      if (this.validateInputs(this.article)) {
        axios
          .post(
            process.env.VUE_APP_CREATE_NEWSLETTER_ARTICLES +
              this.createdNewsletter.id,
            this.article,
            headers.createHeaders(
              this.$store.getters.getUserToken,
              process.env.VUE_APP_TOKEN_VALUE
            )
          )
          .then((res) => {
            alert('Article Added successfully');
            this.created_articles.push(res.data);
          })
          .catch((err) => {
            alert('Something went wrong');
            console.log('---Error---');
            console.log(err);
          });
        return true;
      } else {
        alert('Please fill in all the details before proceeding');
        return false;
      }
    },
    async moveGoogleFile(file) {
      let split = file.split('/');
      let fileName = split[split.length - 1];
      let tempPath = 'newsletter/temp/' + split[split.length - 1];
      let path = 'newsletter/use/';
      let fileData = new FormData();
      fileData.append('tempFile', tempPath);
      fileData.append('newPath', path);
      fileData.append('fileName', fileName);
      let res = await axios.post(
        process.env.VUE_APP_MOVE_FILE_TO_FOLDER,
        fileData
      );
      this.article.image = res.data.url;
    },
    redirectTo() {
      this.$router.push(
        '/newsletter/show-newsletter/' + this.createdNewsletter.id
      );
    },
  },
};
</script>

<style scoped></style>
