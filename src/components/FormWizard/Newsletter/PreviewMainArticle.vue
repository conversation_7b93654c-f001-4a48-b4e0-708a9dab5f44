<template>
  <div>
    <CCard>
      <CCardHeader> Article Preview </CCardHeader>
      <CCardBody
        v-bind:style="
          created.length > 0
            ? 'background-color: #FFFFF'
            : 'background-color: #EFCECE;'
        "
      >
        <CRow class="mb-3">
          <CCol>
            <div v-if="display">
              <img
                style="
                  display: block;
                  margin-left: auto;
                  margin-right: auto;
                  width: auto;
                  height: 200px;
                "
                :src="display"
              />
            </div>
            <div
              v-else
              style="height: 250px; width: auto; background-color: lightblue"
            ></div>
          </CCol>
        </CRow>
        <CRow class="mb-2">
          <CCol>
            <div v-if="main_article.header">
              <p>
                <strong style="color: #cf4641">{{ article_text }}</strong>
              </p>
            </div>
            <div v-else>
              <p><strong style="color: #cf4641">BREAKING NEWS</strong></p>
            </div>
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol>
            <div v-if="main_article.title">
              <h4>
                <b>{{ main_article.title }}</b>
              </h4>
            </div>
            <div v-else>
              <h4><b>This is where the article title is</b></h4>
            </div>
          </CCol>
        </CRow>
        <CRow style="height: 100px" class="mb-4">
          <CCol>
            <div v-if="main_article.description">
              <p>{{ main_article.description }}</p>
            </div>
            <div v-else>
              <p>This is where the article description is</p>
            </div>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CRow>
              <CCol>
                <button class="readAPP">READ ON APP</button>&nbsp;&nbsp;&nbsp;
                <button class="readDESK">READ ON DESKTOP</button>
              </CCol>
            </CRow>
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
export default {
  name: 'PreviewMainArticle',
  props: {
    main_article: {
      type: Object,
      required: true,
    },
    created: {
      type: Array,
    },
    display: {
      type: String,
    },
  },
  computed: {
    article_text() {
      return this.main_article.header.toUpperCase();
    },
  },
};
</script>

<style scoped>
@font-face {
  font-family: Montserrat-Bold;
  src: url('//cdn.newswav.com/mail-assetes/Montserrat-Bold.otf');
}

.readAPP {
  text-decoration: none;
  -webkit-text-size-adjust: none;
  -ms-text-size-adjust: none;
  mso-line-height-rule: exactly;
  font-family: Montserrat-Bold;
  font-size: 14px;
  color: #ffffff;
  border-style: solid;
  border-color: #ea3735;
  border-width: 10px 50px 10px 50px;
  display: inline-block;
  background: #ea3735 none repeat scroll 0% 0%;
  border-radius: 6px;
  font-weight: normal;
  font-style: normal;
  line-height: 17px;
  width: auto;
  text-align: center;
}

.readDESK {
  text-decoration: none;
  -webkit-text-size-adjust: none;
  -ms-text-size-adjust: none;
  mso-line-height-rule: exactly;
  font-family: Montserrat-Bold;
  font-size: 14px;
  color: #ea3735;
  border-style: solid;
  border-color: #fbf7f7;
  border-width: 9px 30px;
  display: inline-block;
  background: #fbf7f7 none repeat scroll 0% 0%;
  border-radius: 6px;
  font-weight: normal;
  font-style: normal;
  line-height: 17px;
  width: auto;
  text-align: center;
}
</style>
