<template>
  <div>
    <CInput
      v-model="newsletter.title"
      label="Newsletter Title"
      invalid-feedback="Newsletter Title is required"
      :is-valid="validator"
    />
    <CTextarea
      v-model="newsletter.description"
      label="Newsletter Description"
    />
    <CInput
      type="date"
      v-model="newsletter.date"
      label="Newsletter Date"
      invalid-feedback="Newsletter Date is required"
      :is-valid="dateValidator"
    />
    <CSelect
      :value.sync="newsletter.language"
      :options="languages"
      label="Newsletter Language"
      invalid-feedback="Newsletter Language is required"
      :is-valid="validator"
    />
  </div>
</template>

<script>
export default {
  name: 'NewsletterForm',
  props: {
    newsletter: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      languages: [
        { value: '', label: 'Please Select Newsletter Language' },
        { value: 'en', label: 'English' },
        { value: 'ms', label: 'Malay' },
        { value: 'zh', label: 'Chinese' },
      ],
    };
  },
  methods: {
    validator(val) {
      return val ? val !== '' : false;
    },
    dateValidator(val) {
      let today = new Date().setHours(0, 0, 0, 0);
      let compare = new Date(val).setHours(0, 0, 0, 0);
      return val ? compare >= today : false;
    },
  },
};
</script>

<style scoped></style>
