<template>
  <div>
    <CRow>
      <CCol>
        <CTabs>
          <CTab title="Add Articles">
            <CCardBody>
              <CRow>
                <CCol>
                  <BaseNewsletterForm
                    :main_article="main_article"
                    @show-display="passDisplay"
                  />
                </CCol>
                <CCol>
                  <PreviewMainArticle
                    :main_article="main_article"
                    :created="created_list"
                    :display="display"
                  />
                </CCol>
              </CRow>
            </CCardBody>
          </CTab>
          <CTab title="Created / Added Articles">
            <CDataTable
              :items="created_list"
              :fields="fields"
              sorter
              pagination
              :items-per-page="5"
              items-per-page-select
            >
              <template #action="{ item }">
                <td>
                  <EditNewsletterArticleModal :article="item" />
                </td>
              </template>
            </CDataTable>
          </CTab>
        </CTabs>
      </CCol>
    </CRow>
    <CRow>
      <CCol>
        <CButtonToolbar class="float-right">
          <CButton shape="pill" variant="outline" color="primary"
            >Reset</CButton
          >
          <CButton
            shape="pill"
            variant="outline"
            color="success"
            @click="addToCreate"
            v-bind:disabled="created_list.length === 5"
            >Add Article</CButton
          >
        </CButtonToolbar>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import PreviewMainArticle from './PreviewMainArticle';
import EditNewsletterArticleModal from '../../Newsletter/EditNewsletterArticleModal';
import BaseNewsletterForm from '../../Newsletter/BaseNewsletterForm';

export default {
  name: 'MainArticleForm',
  components: {
    BaseNewsletterForm,
    EditNewsletterArticleModal,
    PreviewMainArticle,
  },
  props: {
    main_article: {
      type: Object,
    },
    created_list: {
      type: Array,
    },
    display: {
      type: String,
    },
    created_newsletter: {
      type: Object,
    },
  },
  data() {
    return {
      fields: [
        { key: 'article_unique_ID', sorter: true, label: 'Article ID' },
        { key: 'sequence_order', sorter: true, label: 'Article Order' },
        { key: 'title', sorter: true, label: 'Title' },
        { key: 'description', sorter: true, label: 'Description' },
        { key: 'action', sorter: false, label: 'Action' },
      ],
    };
  },
  methods: {
    passDisplay(item) {
      this.$emit('show-display', item);
    },
    addToCreate() {
      let msg = 'This will add the article into the Newsletter. Continue?';
      if (confirm(msg)) {
        this.$emit('push-to-create');
      }
    },
  },
};
</script>

<style scoped></style>
