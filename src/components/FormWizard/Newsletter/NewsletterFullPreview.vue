<template>
  <div>
    <CTabs>
      <CTab title="Newsletter Details">
        <CCardBody>
          <CInput
            v-model="newsletter.title"
            label="Newsletter Title"
            readonly
          />
          <CTextarea
            v-model="newsletter.description"
            label="Newsletter Description"
            rows="5"
            readonly
          />
          <CInput
            type="date"
            v-model="newsletter.date"
            label="Newsletter Date"
            readonly
          />
          <CInput
            v-model="newsletter.language"
            label="Newsletter Language"
            readonly
          />
        </CCardBody>
      </CTab>
      <CTab title="Newsletter Articles">
        <CDataTable
          :items="article_list"
          :fields="fields"
          sorter
          pagination
          :items-per-page="5"
          items-per-page-select
        >
          <template #action="{ item }">
            <td>
              <EditNewsletterArticleModal :article="item" />
            </td>
          </template>
        </CDataTable>
      </CTab>
    </CTabs>
  </div>
</template>

<script>
import EditNewsletterArticleModal from '../../Newsletter/EditNewsletterArticleModal';
export default {
  name: 'NewsletterFullPreview',
  components: { EditNewsletterArticleModal },
  props: {
    newsletter: {
      type: Object,
      required: true,
    },
    article_list: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      languages: [
        { value: '', label: 'Please Select Newsletter Language' },
        { value: 'en', label: 'English' },
        { value: 'ms', label: 'Malay' },
        { value: 'zh', label: 'Chinese' },
      ],
      fields: [
        { key: 'article_unique_ID', sorter: true, label: 'Article ID' },
        { key: 'sequence_order', sorter: true, label: 'Article Order' },
        { key: 'title', sorter: true, label: 'Title' },
        { key: 'description', sorter: true, label: 'Description' },
        { key: 'action', sorter: false, label: 'Action' },
      ],
    };
  },
};
</script>

<style scoped></style>
