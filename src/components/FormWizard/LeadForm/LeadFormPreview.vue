<template>
  <div>
    <div class="d-flex flex-row justify-content-center align-items-center">
      <div class="d-flex flex-column justify-content-center preview_wrapper">
        <div id="lead_title">
          {{ lead.form_title ? lead.form_title : 'Lead Form Title' }}
        </div>
        <div id="vueper">
          <vueper-slides
            fraction
            slider
            :touchable="true"
            fixed-height="187px"
            :arrows="false"
          >
            <vueper-slide
              v-for="(img, idx) in lead.images"
              :key="idx"
              :image="img"
            >
            </vueper-slide>
          </vueper-slides>
        </div>
        <div id="fields">
          <div id="advertiser">
            <div class="d-flex flex-row align-items-center">
              <div style="margin-right: 10px">
                <img
                  :src="lead.advertiser_logo"
                  v-if="lead.advertiser_logo"
                  class="advertiser_logo"
                />
                <div v-else class="logo_placeholder"></div>
              </div>
              <div class="advertiser_name">
                {{
                  lead.advertiser.name
                    ? lead.advertiser.name
                    : 'Advertiser Name'
                }}
              </div>
            </div>
          </div>
          <div id="declare">
            {{
              lead.form_description
                ? lead.form_description
                : 'Lead Form Description'
            }}
          </div>
          <div v-if="lead.fields.length > 0">
            <div
              v-for="(field, idx) in lead.fields"
              :key="idx"
              class="d-flex flex-row align-items-center field-items"
            >
              Enter your {{ field.label }}
            </div>
          </div>
          <div v-else class="field_placeholder">Fields List</div>
          <div id="privacy">
            By submitting the form, you agree to share your details <br />
            with
            <strong>{{
              lead.advertiser.name ? lead.advertiser.name : 'Advertiser Name'
            }}</strong>
            who agrees to use it according to their
            <a
              :href="lead.form_policy ? lead.form_policy : '#'"
              class="policy_link"
              ><strong>Privacy Policy</strong></a
            >.
          </div>
          <div id="button">
            <button class="submit-btn">Submit</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { VueperSlides, VueperSlide } from 'vueperslides';
import 'vueperslides/dist/vueperslides.css';

export default {
  name: 'LeadFormPreview',
  components: { VueperSlides, VueperSlide },
  props: {
    lead: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>

<style scoped>
@import url('//fonts.cdnfonts.com/css/sf-ui-display');
@import url('//fonts.cdnfonts.com/css/sf-ui-text-2');
@import url('//fonts.cdnfonts.com/css/sf-ui-display?styles=16103,16102,16099,16100,16101,16097,16098,16096');
@import url('//fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200;0,300;0,400;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,600;1,700;1,800;1,900&display=swap');

.preview_wrapper {
  max-width: 360px;
  width: 100%;
  border: 1px solid #b2b2b2;
  padding-top: 15px;
  padding-bottom: 16px;
}

.advertiser_name {
  font-size: 16px;
  line-height: 19px;
  font-family: 'SF UI Display';
  font-weight: 700;
}

.advertiser_logo {
  height: 24px;
  width: 24px;
  border-radius: 50%;
}

.logo_placeholder {
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background-color: lightblue;
}

#lead_title {
  text-align: center;
  font-family: 'SF UI Display';
  color: #121212;
  font-weight: 800;
  line-height: 20px;
  font-size: 17px;
  margin-bottom: 17px;
}

#advertiser {
  margin-top: 15px;
  margin-bottom: 15px;
}

.submit-btn {
  display: block;
  width: 100%;
  border: none;
  background-color: rgba(236, 53, 53, 0.5);
  color: #ffffff;
  border-radius: 3px;
  font-family: SF UI Display;
  font-weight: 400;
  height: 45px;
}

#declare {
  line-height: 21px;
  font-size: 16px;
  color: #121212;
  margin-bottom: 15px;
  font-family: SF UI Text;
}

#privacy {
  font-family: SF UI Text;
  color: #121212;
  font-size: 12px;
  line-height: 18px;
  width: 100%;
  max-width: 328px;
  margin-bottom: 20px;
  margin-top: 20px;
}

#fields {
  margin-left: 16px;
  margin-right: 16px;
  max-width: 100%;
  color: #121212;
  font-family: 'SF UI Text';
}

.field-items {
  border-radius: 3px;
  background-color: rgba(240, 240, 240, 1);
  height: 50px;
  width: 328px;
  margin-bottom: 10px;
  padding-left: 15px;
  padding-bottom: 15px;
  padding-top: 16px;
}

.field_placeholder {
  padding-left: 15px;
  padding-bottom: 15px;
  padding-top: 16px;
  height: 50px;
  width: 100%;
  background-color: rgba(240, 240, 240, 1);
}

.policy_link {
  color: #ec3535;
  text-decoration: underline;
}

.handle {
  float: left;
  padding-top: 8px;
  padding-bottom: 8px;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
</style>
