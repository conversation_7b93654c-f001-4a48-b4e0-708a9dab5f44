<template>
  <div>
    <CRow>
      <CCol>
        <CButton
          variant="outline"
          shape="pill"
          color="primary"
          @click="goBack()"
          >Back</CButton
        >
      </CCol>
    </CRow>
    <br />
    <CRow>
      <CCol>
        <CCard>
          <CCardBody>
            <form-wizard
              :title="edit ? 'Edit Lead Form' : 'Create Lead Form'"
              :subtitle="''"
              color="#EC3535"
              @on-complete="createLead"
            >
              <tab-content
                title="General Lead Form Information"
                :before-change="checkAdvertiserData"
              >
                <AdvertiserLeadForm
                  :lead="lead"
                  @setLogo="setAdvertiserLogo"
                  @re-use="setLead"
                />
              </tab-content>
              <tab-content title="Lead Form Images">
                <LeadImagesForm
                  :images="lead.images"
                  @appendImage="addImage"
                  @removeImage="popImage"
                />
              </tab-content>
              <tab-content title="Lead Form Fields">
                <LeadFieldsForm
                  :fields="lead.fields"
                  @appendField="addFieldList"
                  @removeField="popFieldList"
                />
              </tab-content>
              <tab-content title="Lead Form Preview">
                <LeadFormPreview :lead="lead" />
              </tab-content>
            </form-wizard>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import AdvertiserLeadForm from '@/components/FormWizard/LeadForm/AdvertiserLeadForm';
import LeadFieldsForm from '@/components/FormWizard/LeadForm/LeadFieldsForm';
import LeadImagesForm from '@/components/FormWizard/LeadForm/LeadImagesForm';
import LeadFormPreview from '@/components/FormWizard/LeadForm/LeadFormPreview';

export default {
  name: 'LeadFormWizard',
  components: {
    LeadFormPreview,
    LeadImagesForm,
    LeadFieldsForm,
    AdvertiserLeadForm,
  },
  data() {
    return {
      lead: {
        form_name: '',
        form_title: '',
        advertiser: '',
        advertiser_logo: '',
        form_description: '',
        form_policy: '',
        form_website: '',
        fields: [],
        images: [],
      },
      edit: this.$route.params.id !== undefined,
    };
  },
  created() {
    if (this.$route.params.id !== undefined) {
      this.$store
        .dispatch('listLeadForm', { id: parseInt(this.$route.params.id) })
        .then((res) => {
          this.lead = res.data[0];
        })
        .catch(() => {
          alert('Something went wrong in retrieving data');
        });
    }
  },
  methods: {
    goBack() {
      this.$router.push('/lead-forms');
    },
    addFieldList(item) {
      let idx = this.lead.fields.findIndex((fi) => fi.name === item.name);
      if (idx !== -1) {
        this.lead.fields.splice(idx, 1, item);
      } else {
        this.lead.fields.push(item);
      }
    },
    popFieldList(idx) {
      this.lead.fields.splice(idx, 1);
    },
    addImage(link) {
      this.lead.images.push(link);
    },
    popImage(idx) {
      this.lead.images.splice(idx, 1);
    },
    setAdvertiserLogo(item) {
      this.lead.advertiser_logo = item;
    },
    setLead(item) {
      this.lead = item;
    },
    checkAdvertiserData() {
      let errors = [];
      let custom = ['form_policy', 'form_website', 'fields', 'images'];
      Object.entries(this.lead).forEach(([prop, val]) => {
        if (!custom.includes(prop) && val === '') {
          errors.unshift(prop);
        }
        if (prop === 'form_policy' && !val.includes('http')) {
          errors.unshift('form_policy');
        }
        if (prop === 'form_website' && !val.includes('http')) {
          errors.unshift('form_website');
        }
      });
      return errors.length === 0;
    },
    createLead() {
      let pass = {};
      Object.entries(this.lead).forEach(([prop, val]) => {
        pass[prop] = val;
      });
      if (this.edit) {
        this.$store
          .dispatch('editForm', pass)
          .then(() => {
            this.$router.push('/lead-forms');
          })
          .catch((err) => {
            console.log(err);
          });
      } else {
        this.$store
          .dispatch('createForm', pass)
          .then(() => {
            this.$router.push('/lead-forms');
          })
          .catch((err) => {
            console.log(err);
          });
      }
    },
  },
};
</script>

<style scoped></style>
