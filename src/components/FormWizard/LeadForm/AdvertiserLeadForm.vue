<template>
  <div>
    <CRow>
      <CCol>
        <CButton
          class="float-right"
          variant="outline"
          color="info"
          @click="show = !show"
          >Choose from Existing Lead Form</CButton
        >
        <CModal :show.sync="show" title="Choose Lead Form" size="lg">
          <CDataTable
            :sorter-value="{ column: 'created_at', asc: true }"
            :loading="leadLoading"
            sorter
            small
            :items="forms"
            :fields="lead_fields"
            pagination
          >
            <template #show_details="{ item }">
              <td>
                <CButton
                  color="primary"
                  variant="outline"
                  @click="
                    $emit('re-use', item);
                    show = !show;
                  "
                  >Choose
                </CButton>
              </td>
            </template>
          </CDataTable>
          <CPagination
            @update:activePage="goToPage"
            :pages="pagination.last_page"
            :activePage.sync="pagination.current_page"
          />
          <template slot="footer">
            <CButton color="dark" variant="outline" @click="show = !show"
              >Cancel</CButton
            >
          </template>
        </CModal>
      </CCol>
    </CRow>
    <br />
    <CRow>
      <CCol>
        <CInput label="Lead Form Name" v-model="lead.form_name" />
        <div>
          <label for="advertiserSelect">Advertiser</label>
          <multiselect
            id="advertiserSelect"
            :multiple="false"
            v-model="lead.advertiser"
            track-by="id"
            label="name"
            :options="advertisers"
          >
            <span slot="noResult"
              >Oops! No advertiser with the name. Consider changing the search
              query.</span
            >
          </multiselect>
          <br />
        </div>
        <CRow>
          <CCol>
            <CRow>
              <CCol><label for="adIconFile1">Advertiser Logo</label></CCol>
              <CCol>
                <label class="custom-file-upload">
                  <input
                    type="file"
                    id="adIconFile1"
                    @change="onFileChange($event)"
                    ref="inputIcon"
                    class="form-control-file"
                    data-content="icon"
                    accept=".png, .jpg, .jpeg"
                  />
                  <i class="fa fa-cloud-upload"></i> Upload
                </label>
              </CCol>
            </CRow>
          </CCol>
          <CCol>
            <div class="d-flex flex-row justify-content-center">
              <img
                v-if="logo_display"
                :src="logo_display"
                alt=""
                class="logo_place"
              />
              <div v-else class="placeholder_img"></div>
            </div>
            <OverlayLoader v-if="loading" />
          </CCol>
        </CRow>
        <CInput label="Title" v-model="lead.form_title" />
        <CInput label="Description" v-model="lead.form_description" />
        <CInput
          label="Privacy Policy"
          v-model="lead.form_policy"
          :is-valid="urlValidator"
        />
        <CInput
          label="Website URL"
          v-model="lead.form_website"
          :is-valid="urlValidator"
        />
      </CCol>
    </CRow>
  </div>
</template>
<style
  src="../../../../node_modules/vue-multiselect/dist/vue-multiselect.min.css"
></style>

<script>
import { Multiselect } from 'vue-multiselect';
import slugify from 'slugify';
import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import OverlayLoader from '@/components/views/OverlayLoader';
import { mapActions } from 'vuex';

export default {
  name: 'AdvertiserLeadForm',
  components: { OverlayLoader, Multiselect },
  data() {
    return {
      show: false,
      lead_fields: [
        { key: 'created_at', sorter: true, label: 'Created Date' },
        { key: 'form_name', sorter: true, label: 'Lead Form Name' },
        { key: 'advertiserShow', sorter: true, label: 'Advertiser' },
        { key: 'form_title', sorter: true, label: 'Title' },
        { key: 'show_details', sorter: false, filter: false, label: 'Action' },
      ],
    };
  },
  props: {
    lead: {
      type: Object,
      required: true,
    },
  },
  computed: {
    advertisers() {
      let data = [];
      this.$store.getters.allAdvertisers.forEach((item) => {
        let pass = {
          id: item.id,
          name: item.name,
        };
        data.push(pass);
      });
      return data;
    },
    logo_display() {
      return this.lead.advertiser_logo;
    },
    loading() {
      return this.$store.getters.getLoading;
    },
    leadLoading() {
      return this.$store.getters.getLeadLoading;
    },
    forms() {
      return this.$store.getters.getLeadForms;
    },
    pagination() {
      return this.$store.getters.getLeadPagination;
    },
  },
  created() {
    if (this.$store.getters.allAdvertiser.length === 0) {
      this.$store.dispatch('getAllAdvertisers');
    }
    this.listLeadForm({ id: '', currentPage: 1 });
  },
  methods: {
    ...mapActions({
      listLeadForm: 'listLeadForm',
    }),
    urlValidator(val) {
      return (
        (val.includes('http://') || val.includes('https://')) && val !== ''
      );
    },
    onFileChange(event) {
      let input = event.target;
      if (input.files && input.files[0]) {
        let iSize = input.files[0].size / 1000 / 1000;
        if (iSize <= 0.1) {
          this.getIconDimension(input);
        } else {
          alert('File size exceeded. Max 100KB');
          this.$refs.inputIcon.value = null;
        }
      }
    },
    getIconDimension(input) {
      let file = input.files[0];
      if (!file || file.type.indexOf('image/') !== 0) {
        alert('image not found');
      } else {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (evt) => {
          let img = new Image();
          img.onload = () => {
            let w = img.width;
            let h = img.height;
            let r = this.gcd(w, h);

            var error = 'Logo must be in 1:1 ratio';
            var a1 = w / r;
            var a2 = h / r;
            if (a1 == 1 && a2 == 1) {
              this.googleUpload(input);
            } else {
              alert(error);
              this.$refs.inputIcon.value = null;
            }
          };
          img.src = evt.target.result;
        };
        reader.onerror = (evt) => {
          console.error(evt);
        };
      }
    },
    gcd(a, b) {
      return b == 0 ? a : this.gcd(b, a % b);
    },
    googleUpload: function (input) {
      let fileName = slugify(input.files[0].name);
      let fileData = new FormData();
      fileData.append('path', 'boss-images/temp');
      fileData.append('file', input.files[0]);
      fileData.append('name', fileName);
      this.$store.commit('SET_GENERAL_LOADING', true);
      axios
        .post(process.env.VUE_APP_UPLOAD_FILE_TO_GCS, fileData)
        .then((res) => {
          this.$emit('setLogo', res.data.url);
          this.$store.commit('SET_GENERAL_LOADING', false);
        })
        .catch((error) => {
          this.$store.commit('SET_GENERAL_LOADING', false);
          alert('Something went wrong with uploading media');
          console.log('----Error----');
          console.log(error);
          Bugsnag.notify(error);
        });
    },
    goToPage(page) {
      this.listLeadForm({
        id: '',
        currentPage: page,
      });
    },
  },
};
</script>

<style scoped>
input[type='file'] {
  display: none;
}

.custom-file-upload {
  border: 1px solid #ccc;
  display: inline-block;
  padding: 6px 12px;
  cursor: pointer;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  margin: 0 !important;
}

input[type='number'] {
  -moz-appearance: textfield !important; /* Firefox */
}

.placeholder_img {
  height: 150px;
  width: 150px;
  background-color: lightblue;
  border-radius: 50%;
  border: 1px solid #b2b2b2;
}

.logo_place {
  border-radius: 50%;
  border: 1px solid #b2b2b2;
  width: 150px;
  height: 150px;
}
</style>
