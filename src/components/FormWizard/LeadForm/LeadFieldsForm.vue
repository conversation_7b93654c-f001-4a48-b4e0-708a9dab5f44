<template>
  <div>
    <CRow>
      <CCol>
        <CButton
          @click="show = !show"
          color="info"
          variant="outline"
          shape="pill"
          class="float-right"
        >
          <CIcon name="cil-plus" />
          Fields
        </CButton>
      </CCol>
    </CRow>
    <br />
    <CRow>
      <CCol>
        <draggable :list="fields" handle=".handle" ghost-class="ghost">
          <div
            class="d-flex flex-row align-items-center selected-field-list"
            v-for="(option, optionIndex) in fields"
            :key="option.name + optionIndex + 'selected'"
          >
            <div class="mr-2">
              <a
                class="handle"
                href="#"
                style="text-decoration: none; color: #121212"
              >
                <CIcon name="cil-cursor-move"></CIcon>
              </a>
            </div>
            <div class="d-flex flex-column mr-auto">
              <div>
                Label : <strong> {{ option.label }} </strong>
              </div>
              <div>Field Name : {{ option.name }}</div>
            </div>
            <div>
              <CButtonToolbar>
                <CButton
                  v-c-tooltip="'Edit Field'"
                  @click="setToEdit(option)"
                  v-if="canEdit(option)"
                >
                  <CIcon name="cil-pencil"></CIcon>
                </CButton>
                <CButton
                  v-c-tooltip="'Delete Field'"
                  @click="$emit('removeField', optionIndex)"
                >
                  <CIcon name="cil-trash"></CIcon>
                </CButton>
              </CButtonToolbar>
            </div>
          </div>
        </draggable>
      </CCol>
    </CRow>
    <CModal
      :show.sync="show"
      title="Add Lead Form Fields"
      :close-on-backdrop="false"
      centered
      size="lg"
    >
      <div class="d-flex flex-column field-wrapper">
        <div
          class="d-flex flex-row align-items-center field-item"
          v-for="item in defaultFieldList"
          :key="item.key"
        >
          <div class="mr-auto">{{ item.label }}</div>
          <div>
            <CButton @click="addToField(item)" :disabled="alreadyExists(item)">
              <CIcon name="cil-plus"></CIcon>
            </CButton>
          </div>
        </div>
      </div>
      <template slot="footer">
        <CButton color="secondary" @click="show = !show">Cancel</CButton>
      </template>
    </CModal>
    <CModal
      :show.sync="editShow"
      title="Edit Lead Form Fields"
      :close-on-backdrop="false"
      centered
      size="lg"
    >
      <CInput v-model="fieldValue.label" label="Field Label" />
      <CInput v-model="fieldValue.name" label="Field Name" />
      <CInput v-model="fieldValue.type" label="Field Type" disabled readonly />
      <div>
        <label for="requireRadio">Compulsory</label>
        <br />
        <div
          class="form-check form-check-inline"
          v-for="(option, optionIndex) in yes_no_options"
          :key="(option.value % 1) + optionIndex + 'yesno_reader'"
        >
          <input
            class="form-check-input"
            type="radio"
            :id="(option.value % 1) + optionIndex + 'yesno_reader'"
            :value="option.value"
            v-model="fieldValue.required"
          />
          <label
            class="form-check-label"
            :for="(option.value % 1) + optionIndex + 'yesno_reader'"
            >{{ option.label }}</label
          >
        </div>
      </div>
      <template slot="footer">
        <CButton
          color="success"
          @click="submitToField"
          variant="outline"
          v-text="forEdit ? 'Edit Field' : 'Add Field'"
        ></CButton>
      </template>
    </CModal>
  </div>
</template>

<script>
import draggable from 'vuedraggable';
export default {
  name: 'LeadFieldsForm',
  components: { draggable },
  props: {
    fields: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      fieldValue: {
        label: '',
        key: '',
        type: '',
        required: 0,
      },
      show: false,
      editShow: false,
      forEdit: false,
      defaultFieldList: [
        { key: 'name', label: 'Name', once: true, type: 'textField' },
        {
          key: 'contact_number',
          label: 'Contact Number',
          once: true,
          type: 'textField',
        },
        { key: 'email', label: 'Email', once: true, type: 'textField' },
        { key: 'textField', label: 'Text Field', type: 'textField' },
        { key: 'numberField', label: 'Number Field', type: 'numberField' },
      ],
      yes_no_options: [
        { value: 1, label: 'Yes' },
        { value: 0, label: 'No' },
      ],
    };
  },
  methods: {
    alreadyExists(item) {
      let dummy = this.fields.findIndex((fi) => fi.name === item.key);
      return dummy !== -1 && item.once;
    },
    canEdit(item) {
      let nonEditable = ['name', 'contact_number', 'email'];
      if (nonEditable.includes(item.name)) {
        return false;
      } else {
        return true;
      }
    },
    addToField(item) {
      if (item.once) {
        let sudo = {
          label: item.label,
          name: item.key,
          type: item.type,
          required: 1,
        };
        this.$emit('appendField', sudo);
        this.show = !this.show;
      } else {
        this.show = !this.show;
        this.setToEdit(item);
      }
    },
    setToEdit(item) {
      this.fieldValue = {
        label: item.label,
        name: item.key ?? item.name,
        type: item.type,
        required: item.required ?? 0,
      };
      this.forEdit = !this.forEdit;
      this.editShow = !this.editShow;
    },
    submitToField() {
      let pass = {};
      Object.entries(this.fieldValue).forEach(([prop, val]) => {
        pass[prop] = val;
      });
      this.$emit('appendField', pass);
      this.resetFieldValue();
      this.editShow = !this.editShow;
      this.forEdit = !this.forEdit;
    },
    resetFieldValue() {
      this.fieldValue = {
        label: '',
        key: '',
        type: '',
        required: 0,
      };
    },
  },
};
</script>

<style scoped>
.field-item {
  border: solid 1px rgba(178, 178, 178, 0.5);
  padding: 10px 20px 10px 20px;
  border-radius: 5px;
  margin-bottom: 8px;
}

.selected-field-list {
  border: solid 1px rgba(178, 178, 178, 0.5);
  background-color: #ffffff;
  padding: 10px 20px 10px 20px;
  border-radius: 5px;
  margin-bottom: 8px;
}
.handle {
  float: left;
  padding-top: 8px;
  padding-bottom: 8px;
}
.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
</style>
