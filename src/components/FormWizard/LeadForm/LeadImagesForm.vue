<template>
  <div>
    <CRow>
      <CCol>
        <CRow>
          <CCol>
            <label for="adIconFile1">Lead Form Images</label>
          </CCol>
          <CCol>
            <label class="custom-file-upload">
              <input
                type="file"
                id="adIconFile1"
                @change="onFileChange($event)"
                ref="inputIcon"
                class="form-control-file"
                data-content="icon"
                accept=".png, .jpg, .jpeg"
              />
              <i class="fa fa-cloud-upload"></i> Upload
            </label>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <draggable :list="imagesList" ghost="ghost" handle=".handle">
              <div
                class="d-flex flex-row align-items-center img-container"
                v-for="(item, idx) in imagesList"
                :key="idx"
              >
                <div class="mr-2">
                  <a
                    class="handle"
                    href="#"
                    style="text-decoration: none; color: #121212"
                  >
                    <CIcon name="cil-cursor-move"></CIcon>
                  </a>
                </div>
                <div class="d-flex flex-column mr-auto">
                  <div style="margin-bottom: 5px">Image {{ idx + 1 }}</div>
                  <div>
                    <textarea
                      class="form-control"
                      cols="80"
                      rows="1"
                      :value="item"
                      readonly
                      disabled
                      style="resize: none"
                    ></textarea>
                  </div>
                </div>
                <div>
                  <CButton @click="$emit('removeImage', idx)">
                    <CIcon name="cil-trash"></CIcon>
                  </CButton>
                </div>
              </div>
            </draggable>
          </CCol>
        </CRow>
      </CCol>
      <CCol>
        <CCard>
          <CCardHeader>Image Previews</CCardHeader>
          <CCardBody>
            <vueper-slides slider :touchable="false" fixed-height="400px">
              <vueper-slide
                v-for="(img, idx) in imagesList"
                :key="idx"
                :image="img"
              />
            </vueper-slides>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import { VueperSlides, VueperSlide } from 'vueperslides';
import 'vueperslides/dist/vueperslides.css';
import slugify from 'slugify';
import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import draggable from 'vuedraggable';

export default {
  name: 'LeadImagesForm',
  components: {
    draggable,
    VueperSlides,
    VueperSlide,
  },
  props: {
    images: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      splide_opt: {
        rewind: true,
        width: '100%',
        heightRatio: 0.5,
        perPage: 1,
      },
      flux_opt: {
        allowFullscreen: false,
        delay: 3000,
        lazyLoad: true,
      },
      transition: [
        {
          name: 'slide',
          options: {
            totalDuration: 1000,
            easing: 'ease-in-out',
          },
        },
      ],
    };
  },
  computed: {
    loading() {
      return this.$store.getters.getLoading;
    },
    imagesList() {
      return this.images;
    },
  },
  created() {},
  methods: {
    onFileChange(event) {
      let input = event.target;
      if (input.files && input.files[0]) {
        let iSize = input.files[0].size / 1000 / 1000;
        if (iSize <= 0.5) {
          this.getCoverDimension(input);
        } else {
          alert('File size exceeded. Max 500KB for Image');
        }
      }
    },
    getCoverDimension(input) {
      let file = input.files[0];
      if (!file || file.type.indexOf('image/') !== 0) {
        alert('Image not found');
      } else {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (evt) => {
          let img = new Image();
          img.onload = () => {
            let w = img.width;
            let h = img.height;
            let r = this.gcd(w, h);
            var error = 'Image must be in 16:9 ratio';
            var a1 = w / r;
            var a2 = h / r;
            if (a1 === 16 && a2 === 9) {
              this.googleUpload(input);
            } else {
              alert(error);
            }
          };
          img.src = evt.target.result;
        };
        reader.onerror = (evt) => {
          console.error(evt);
        };
      }
    },
    gcd(a, b) {
      return b == 0 ? a : this.gcd(b, a % b);
    },
    googleUpload: function (input) {
      let fileName = slugify(input.files[0].name);
      let fileData = new FormData();
      fileData.append('path', 'lead-form/temp');
      fileData.append('file', input.files[0]);
      fileData.append('name', fileName);
      this.$store.commit('SET_GENERAL_LOADING', true);
      axios
        .post(process.env.VUE_APP_UPLOAD_FILE_TO_GCS, fileData)
        .then((res) => {
          this.$emit('appendImage', res.data.url);
          this.$store.commit('SET_GENERAL_LOADING', false);
        })
        .catch((error) => {
          this.$store.commit('SET_GENERAL_LOADING', false);
          alert('Something went wrong with uploading image');
          console.log('----Error----');
          console.log(error);
          Bugsnag.notify(error);
        });
    },
  },
};
</script>

<style scoped>
.img-container {
  margin-bottom: 12px;
  border: solid 1px rgba(178, 178, 178, 0.5);
  padding: 10px;
  border-radius: 5px;
  background-color: #ffffff;
}

input[type='file'] {
  display: none;
}

.custom-file-upload {
  border: 1px solid #ccc;
  display: inline-block;
  padding: 6px 12px;
  cursor: pointer;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  margin: 0 !important;
}

input[type='number'] {
  -moz-appearance: textfield !important; /* Firefox */
}

.handle {
  float: left;
  padding-left: 8px;
  padding-top: 8px;
  padding-bottom: 8px;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
</style>
