<template>
  <form-wizard
    :title="title"
    :subtitle="''"
    @on-complete="redirectTo"
    color="#2eb85c"
  >
    <tab-content title="Search User by ID" :before-change="searchUser">
      <CInput label="User ID" v-model="user_id" />
    </tab-content>
    <tab-content title="User Details">
      <CInput label="User ID" v-model="user_id" />
      <CInput label="User Name" v-model="username" />
      <CInput label="User Email" v-model="email" />
    </tab-content>
  </form-wizard>
</template>

<script>
export default {
  name: 'StartFormWizard',
  props: {
    title: {
      type: String,
    },
  },
  data() {
    return {
      user_id: '',
      username: '',
      email: '',
    };
  },
  methods: {
    goBack() {
      this.$router.push('/list-blocked');
    },
    redirectTo() {
      this.$router.push('/list-blocked');
    },
    searchUser() {},
  },
};
</script>

<style scoped></style>
