<template>
  <div>
    <CCard>
      <OverlayLoader v-if="isLoading" />
      <CCardHeader>{{ edit ? 'Edit Video' : 'Create Video' }}</CCardHeader>
      <CCardBody>
        <CRow>
          <CCol sm="12" md="6" lg="6">
            <!-- base video form with update listener -->
            <BaseAddvideoForm
              :video="video"
              :isLoading="isLoading"
              :topics="modifiedTopics"
              @update:is_live="setIsLive"
              @loading="isLoading = $event"
            />
          </CCol>
          <CCol sm="12" md="6" lg="6">
            <!-- video preview -->
            <VideoPreview :videos="video" :cover="video.source_thumb_url" />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol>
            <!-- submit form button -->
            <CButton
              color="success"
              variant="outline"
              block
              @click="submitVideo"
              :disabled="is_disabled"
              >{{ edit ? 'Update' : 'Submit' }}</CButton
            >
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>
<script>
import BaseAddvideoForm from '@/components/YoutubeVideos/BaseAddVideoForm';
import VideoPreview from '@/components/YoutubeVideos/VideoPreview.vue';
import { mapActions } from 'vuex';
import OverlayLoader from '../../views/OverlayLoader';
export default {
  // components used
  components: { BaseAddvideoForm, VideoPreview, OverlayLoader },

  data() {
    return {
      modifiedTopics: [],
      // video item
      video: {
        title: '',
        description: '',
        youtube_link: '',
        source_thumb_url: '',
        publisher_id: '',
        topic: { value: null, label: '' },
        language: null,
        is_live: 0,
      },

      // video thumbnail
      displayCover: '',

      isLoading: false,

      // check if video id is defined
      edit: this.$route.params.id !== undefined,
    };
  },
  computed: {
    // check if any required fields are empty or invalid (disable submit button)
    is_disabled() {
      //check each required field for validity
      const fieldsValidity = {
        titleValid:
          this.video.title.trim().length > 0 &&
          this.video.title.trim().length <= 120,
        descValid:
          this.video.description.trim().length > 0 &&
          this.video.description.trim().length <= 1000,
        videoUrlValid:
          this.video.youtube_link.trim().length > 0 &&
          this.video.youtube_link.startsWith(
            'https://www.youtube.com/watch?v='
          ),
        topicValid: this.video.topic.value != -1,
        publisherIdValid: this.video.publisher_id.trim().length > 0,
      };

      // Return true (disable submit button) if any field is invalid
      return !Object.values(fieldsValidity).every((isValid) => isValid);
    },
  },

  async created() {
    // if id is defined for this form
    if (this.$route.params.id !== undefined) {
      // pass video details in based on id
      this.currentVideo(parseInt(this.$route.params.id))
        .then((res) => {
          const response = res[0];
          Object.entries(response).forEach(([prop, val]) => {
            if (prop === 'publisherIds') {
              this.video[prop] = response[prop].map((i) => {
                return { value: parseInt(i.value), label: i.label };
              });
            } else if (prop === 'topic' && typeof val === 'string') {
              try {
                // Try parsing the topic string into an object
                this.video[prop] = JSON.parse(val);
              } catch (error) {
                console.error('Error parsing topic JSON:', error);
                this.video[prop] = { value: null, label: '' };
              }
            } else {
              this.video[prop] = val;
            }
          });
          this.displayCover = this.video.imageUrl;
        })
        .catch((err) => {
          console.log(err);
        });
    }

    this.getTopics().then((res) => {
      if (res && Array.isArray(res.data)) {
        // Use a data property to store the modified topics
        this.modifiedTopics = res.data.map((t) => ({
          value: t.id,
          label: t.nameEN,
        }));
      } else {
        // Handle the case when res or res.data is not as expected
        console.error('Unexpected response format for topics:', res);
      }
    });
  },

  methods: {
    ...mapActions({
      currentVideo: 'listVideosById',
      createVideo: 'createVideo',
      updateVideo: 'updateVideo',
      getTopics: 'listVideoTopics',
    }),

    async submitVideo() {
      this.isLoading = true;

      this.video.description = this.video.description.replace(/\n/g, '<br>');

      // get fields inputs
      let pass = {};
      Object.entries(this.video).forEach(([prop, val]) => {
        pass[prop] = val;
      });

      // if form is for updating a video
      if (this.edit) {
        // trigger update video function
        this.updateVideo(pass)
          .then(() => {
            this.$swal
              .fire({
                icon: 'success',
                text: 'Video successfully updated!',
                showCancelButton: false,
              })
              .then((res) => {
                if (res.isConfirmed) {
                  this.$router.push('/ytvideo');
                }
              });
          })
          .catch((err) => {
            let message = 'Something went wrong in updating video';
            if (err.response.status == 401) {
              message = 'Unauthorized. Check console for more info';
            }
            if (err.response.status == 404) {
              message = err.response.data.message;
            }

            if (err.response.status == 400) {
              message = err.response.data.message;
            }
            this.$swal.fire({
              icon: 'error',
              text: message,
              showCancelButton: false,
            });
          });
      } else {
        // if this form is for creating new video
        // trigger create video function
        this.createVideo(pass)
          .then(() => {
            this.$swal
              .fire({
                icon: 'success',
                text: 'Video successfully created!',
                showCancelButton: false,
              })
              .then((res) => {
                if (res.isConfirmed) {
                  this.$router.push('/ytvideo');
                }
              });
          })
          .catch((err) => {
            console.log(err);
            let message = 'Something went wrong in creating video';
            if (err.response.status == 401) {
              message = 'Unauthorized. Check console for more info';
            }
            if (err.response.status == 404) {
              message = err.response.data.message;
            }

            if (err.response.status == 400) {
              message = err.response.data.message;
            }
            this.$swal.fire({
              icon: 'error',
              text: message,
              showCancelButton: false,
            });
          });
      }
    },

    // defined setters for video details
    setTitle(val) {
      this.video.title = val;
    },
    setDesc(val) {
      this.video.description = val;
    },
    setIsLive(val) {
      this.video.is_live = val;
    },
    setVideoUrl(val) {
      this.video.youtube_link = val;
    },
    setThumbnailUrl(val) {
      this.video.source_thumb_url = val;
    },
    setPublisherId(val) {
      this.video.publisher_id = val;
    },
  },
};
</script>
