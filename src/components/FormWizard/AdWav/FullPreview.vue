<template>
  <div>
    <CRow>
      <CCol>
        <CCardBody>
          You've reached the end of the wizard, following this the campaign and
          ads have already been created and this is for preview. Click 'Finish'
          and you will be redirected to the created campaign's page
        </CCardBody>
      </CCol>
    </CRow>
    <CRow>
      <CCol>
        <CTabs variant="pills">
          <CTab title="Campaign" active>
            <CCardBody>
              <CRow>
                <CCol>
                  <BaseCampaignForm :campaign="campaign" :disableForm="true" />
                </CCol>
              </CRow>
            </CCardBody>
          </CTab>
          <CTab title="Advertisement(s)">
            <CCardBody>
              <CDataTable
                sorter
                small
                :fields="ads_fields"
                :items-per-page="5"
                :items="ads"
              >
                <template #action="{ item }">
                  <td>
                    <EditAdvertisementModal
                      :advertisement="item"
                      :adv_name="campaign.adv_name"
                    />
                  </td>
                </template>
              </CDataTable>
            </CCardBody>
          </CTab>
        </CTabs>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import EditAdvertisementModal from '@/components/Advertisements/EditAdvertisementModal';
import BaseCampaignForm from '@/components/Campaigns/BaseCampaignForm';
export default {
  name: 'FullPreview',
  components: {
    EditAdvertisementModal,
    BaseCampaignForm,
  },
  props: {
    ads: {
      type: Array,
      required: true,
    },
    campaign: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      ads_fields: [
        { key: 'type', sorter: true, label: 'Advertisement Type' },
        { key: 'body', sorter: true, label: 'Advertisement Body' },
        { key: 'title', sorter: true, label: 'Advertisement Title' },
        { key: 'language', sorter: true, label: 'Language' },
        { key: 'action', sorter: false, label: 'Action' },
      ],
    };
  },
};
</script>
