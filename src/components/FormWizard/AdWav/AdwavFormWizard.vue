<template>
  <div>
    <CRow>
      <CCol>
        <BackButton />
      </CCol>
    </CRow>
    <br />
    <CRow>
      <CCol>
        <CCard>
          <CCardBody>
            <form-wizard
              :title="'Create Campaign & Ads'"
              :subtitle="''"
              @on-complete="onComplete"
              color="#2eb85c"
            >
              <tab-content
                title="Campaign Details"
                :before-change="checkCampaign"
              >
                <CampaignForm
                  :campaign="campaign"
                  :created-campaign="createdCampaign"
                  @selected-campaign="campaignSelected"
                  @reset-campaign="resetCampaign"
                  @create-campaign="submitCampaign"
                />
              </tab-content>
              <tab-content
                title="Advertisement Details"
                :before-change="checkAds"
              >
                <AdvertisementForm
                  :advert="advert"
                  :campaign="campaign"
                  :display_icon="display_icon"
                  :display_cover="display_cover"
                  :adv="adv"
                  :created="createdAds"
                  :ad_list="ad_list"
                  @selected-ad="adSelected"
                  @reset-ad="resetAd"
                  @show-display="showDisplays"
                  @clear-display="clearDisplays"
                  @prefill-data="setPlaceholder"
                />
              </tab-content>
              <tab-content title="Full Review">
                <FullPreview :ads="createdAds" :campaign="createdCampaign" />
              </tab-content>
              <template slot="footer" slot-scope="props">
                <div class="wizard-footer-left">
                  <wizard-button
                    v-if="props.activeTabIndex > 0 && !props.isLastStep"
                    @click.native="props.prevTab()"
                    :style="props.fillButtonStyle"
                    >Previous
                  </wizard-button>
                </div>
                <div class="wizard-footer-right">
                  <div v-show="checkAction('campaign_submit')">
                    <wizard-button
                      v-if="!props.isLastStep"
                      @click.native="props.nextTab()"
                      class="wizard-footer-right"
                      :style="props.fillButtonStyle"
                      :disabled="checkInvestor"
                      >Next
                    </wizard-button>

                    <wizard-button
                      v-else
                      @click.native="onComplete"
                      class="wizard-footer-right"
                      :style="props.fillButtonStyle"
                      >{{ props.isLastStep ? 'Finish' : 'Next' }}
                    </wizard-button>
                  </div>
                </div>
              </template>
            </form-wizard>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import BackButton from '@/components/common/BackButton';
import { mapGetters, mapActions } from 'vuex';
import Bugsnag from '@bugsnag/js';
import FullPreview from '@/components/FormWizard/AdWav/FullPreview';
import axios from 'axios';
import AdvertisementForm from '@/components/FormWizard/AdWav/AdvertisementForm';
import CampaignForm from '@/components/FormWizard/AdWav/CampaignForm';
import slugify from 'slugify';
import * as headers from '@/helpers/headers';
import { adwavValidateValues } from '@/helpers/field-validate';
import { format } from 'date-fns';

export default {
  components: { CampaignForm, AdvertisementForm, FullPreview, BackButton },
  name: 'CreateFormWizard',
  data() {
    return {
      createdCampaign: {
        id: '',
        name: '',
        advertiser_id: this.$route.params.adv_id,
        start_date: '',
        end_date: '',
        total_budget: '',
        impressions_per_day: '',
        impressions_per_user: '',
        clicks_per_user: '',
        impressions_per_session: '',
        clicks_per_sessions: '',
        type: '',
        targeted_states: [],
        targeted_interests: [],
        targeted_feeds: [],
        targeted_telcos: [],
        targeted_devices: [],
        status: 'active',
        impressions_per_time_block: '',
        clicks_per_time_block: '',
        cpm: '',
        start_hour: 8,
        estimate_count: 0,
        estimate_count_telco: 0,
        estimate_count_location: 0,
        estimate_count_device: 0,
        ads_type: '',
      },
      createdAds: [],
      ad_list: [],
      campaign: {
        name: '',
        advertiser_id: this.$route.params.adv_id,
        start_date: '',
        end_date: '',
        total_budget: '',
        impressions_per_day: '',
        impressions_per_user: '',
        clicks_per_user: '',
        impressions_per_session: '',
        clicks_per_sessions: '',
        type: '',
        targeted_states: [],
        targeted_interests: [],
        targeted_feeds: [],
        targeted_telcos: [],
        targeted_devices: [],
        status: 'active',
        impressions_per_time_block: '',
        clicks_per_time_block: '',
        cpm: '',
        start_hour: 8,
        estimate_count: 0,
        estimate_count_telco: 0,
        estimate_count_location: 0,
        estimate_count_device: 0,
        ads_type: '',
      },
      display_icon: null,
      display_cover: null,
      advert: {
        ad_media: [],
        name: '',
        campaign_id: '',
        type: 'bigcard',
        body: '',
        title: '',
        subtitle: '',
        cover_url: '',
        icon_url: '',
        cta: '',
        landing_url: '',
        landing_page_type: 'url', // url, form, nw_post
        lead_form: {},
        media_type: '',
        cap_enabled: 0,
        language: [],
        advertiser_id: this.$route.params.adv_id,
        splash_media_duration: '0',
        splash_media_no_skip_duration: '0',
        metadata: {},
      },
      now: format(new Date(), 'yyyy-MM-dd'),
    };
  },
  watch: {
    createdCampaign(newVal) {
      if (newVal && this.advert.name === '') {
        this.advert.name = newVal.name;
        this.advert.campaign_id = newVal.id;
      }
    },
  },
  computed: {
    ...mapGetters({
      getAdvertiserById: 'getAdvertiser',
      locations: 'getLocations',
      interests: 'getInterests',
      feeds: 'getFeeds',
      advertisers: 'allAdvertisers',
    }),
    adv() {
      return this.getAdvertiserById(parseInt(this.$route.params.adv_id));
    },
  },
  created() {
    if (this.locations.length == 0) {
      this.listLocations();
    }
    if (this.interests.length == 0) {
      this.listInterests();
    }
    if (this.feeds.length == 0) {
      this.listFeeds();
    }
    if (this.advertisers.length === 0) {
      this.listAdvertisers();
    }
    this.getAds();
  },
  methods: {
    ...mapActions({
      listLocations: 'listLocations',
      listInterests: 'listInterests',
      listFeeds: 'listFeeds',
      listAdvertisers: 'getAllAdvertisers',
    }),
    async submitCampaign() {
      if (adwavValidateValues('campaign', this.campaign)) {
        let a = JSON.parse(JSON.stringify(this.campaign));
        if (
          this.campaign.targeted_states.find((element) => element.id === 0) !=
          null
        ) {
          a.targeted_states = [];
        }
        if (
          this.campaign.targeted_devices.find((element) => element.id === 0) !=
          null
        ) {
          a.targeted_devices = [];
        }
        if (
          this.campaign.targeted_devices.find((element) => element.id === 0) !=
          null
        ) {
          a.targeted_devices = [];
        }
        if (
          this.campaign.targeted_interests.find(
            (element) => element.id === 0
          ) != null
        ) {
          a.targeted_interests = [];
        }
        if (
          this.campaign.targeted_feeds.find((element) => element.id === '0') !=
          null
        ) {
          a.targeted_feeds = [];
        }
        if (
          this.campaign.targeted_telcos.find((element) => element.id === 0) !=
          null
        ) {
          a.targeted_telcos = [];
        }
        if (
          this.campaign.targeted_feeds.find(
            (element) => element.name === 'Videos-Videos'
          ) != null
        ) {
          let f = {
            id: 'F_F_-7',
            name: 'Videos-Videos',
          };
          a.targeted_feeds.push(f);
        }
        let cont = false;
        a.user_email = JSON.parse(localStorage.user).email;
        await axios
          .post(
            process.env.VUE_APP_ADD_CAMPAIGN_ENDPOINT,
            a,
            headers.createHeaders(this.$store.getters.getUserToken)
          )

          .then((res) => {
            this.createdCampaign = res.data[0];
            this.advert.type = this.createdCampaign.ads_type;
            this.$store.commit('SET_NEW_CAMPAIGN', res.data[0]);
            this.$swal.fire({
              icon: 'success',
              text: 'Campaign successfully created!',
              showCancelButton: false,
            });
            if (this.advert.type === 'sponsored') {
              this.$set(this.advert, 'metadata', {
                banner_background_color: null,
                banner_text_color: null,
              });
            }
            cont = true;
          })
          .catch((error) => {
            console.log(error.response.data);
            let message = 'Check console for more info';
            let title = 'Error';
            if (error.response.status == 401) {
              message =
                'Unauthorized. You have no permission to delete campaign';
              title = 'Unauthorized';
            }
            this.$notify({
              group: 'error',
              title: title,
              type: 'error',
              text: `${error.response.status} : ${message}`,
              duration: -1,
              position: 'bottom right',
              closeOnClick: true,
            });
            console.log('-----Error-----');
            console.log(error);
            Bugsnag.notify(error);
          });
        return cont;
      } else {
        return false;
      }
    },
    getAds() {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      let id = this.$route.params.adv_id;
      axios
        .get(
          process.env.VUE_APP_GET_ADVERTISEMENT_BY_ADVERTISER + id + '?r=' + r,
          headers.createHeaders(this.$store.getters.getUserToken)
        )
        .then((res) => {
          this.ad_list = res.data;
        })
        .catch((error) => {
          console.log(error.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (error.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          this.$notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${error.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          console.log('---Error---');
          console.log(error);
          Bugsnag.notify(error);
        });
    },
    checkCampaign() {
      if (JSON.stringify(this.createdCampaign) === '{}') {
        this.$swal.fire({
          icon: 'warning',
          text: 'Please create campaign before proceeding',
          showCancelButton: false,
        });
        return false;
      } else {
        return true;
      }
    },
    checkAds() {
      if (this.createdAds.length === 0) {
        this.$swal.fire({
          icon: 'warning',
          text: 'Please create ad(s) before proceeding',
          showCancelButton: false,
        });
        return false;
      } else {
        return true;
      }
    },
    onComplete() {
      this.$router.push(
        '/advertisers/' +
          this.createdCampaign.advertiser_id +
          '/show-campaign/' +
          this.createdCampaign.id
      );
    },
    goBack() {
      this.$router.push('/advertisers/' + this.$route.params.adv_id);
    },
    campaignSelected(selected) {
      this.campaign = selected;
    },
    adSelected(selected) {
      Object.entries(selected).forEach(([prop, val]) => {
        this.advert[prop] = val;
      });
      this.display_cover = selected.cover_url;
      this.display_icon = selected.icon_url;
    },
    resetAd() {
      this.advert = {
        name: '',
        campaign_id: '',
        type: 'bigcard',
        body: '',
        title: '',
        subtitle: '',
        cover_url: '',
        icon_url: '',
        cta: '',
        landing_url: '',
        landing_page_type: 'url', // url, form, nw_post
        lead_form: {},
        media_type: '',
        cap_enabled: 0,
        language: [],
        advertiser_id: this.$route.params.adv_id,
        splash_media_duration: '0',
        splash_media_no_skip_duration: '0',
      };
      this.display_cover = null;
      this.display_icon = null;
    },
    resetCampaign() {
      this.campaign = {
        name: '',
        advertiser_id: this.$route.params.adv_id,
        start_date: '',
        end_date: '',
        total_budget: '',
        impressions_per_day: '',
        impressions_per_user: '',
        clicks_per_user: '',
        impressions_per_session: '',
        clicks_per_sessions: '',
        type: '',
        targeted_states: [],
        targeted_interests: [],
        targeted_feeds: [],
        targeted_telcos: [],
        targeted_devices: [],
        status: 'active',
        impressions_per_time_block: '',
        clicks_per_time_block: '',
        cpm: '',
        start_hour: 8,
        estimate_count: 0,
        estimate_count_telco: 0,
        estimate_count_location: 0,
        estimate_count_device: 0,
        ads_type: '',
      };
    },
    showDisplays(item) {
      if (item.space === 'cover') {
        this.display_cover = item.data;
      } else {
        this.display_icon = item.data;
      }
    },
    clearDisplays() {
      this.display_cover = null;
      this.display_icon = null;
    },
    async moveGoogleFile(file, type) {
      let split = file.split('/');
      let fileName = split[split.length - 1];
      let tempPath = 'adwav/temp/' + split[split.length - 1];
      let path = 'adwav/' + slugify(this.adv.name.toLowerCase()) + '/';
      let fileData = new FormData();
      fileData.append('tempFile', tempPath);
      fileData.append('newPath', path);
      fileData.append('fileName', fileName);
      try {
        let res = await axios.post(
          process.env.VUE_APP_MOVE_FILE_TO_FOLDER,
          fileData
        );
        if (type === 'cover') {
          this.advert.cover_url = res.data.url;
        } else {
          this.advert.icon_url = res.data.url;
        }
      } catch (e) {
        Bugsnag.notify(e);
      }
    },
    setPlaceholder(val) {
      this.advert.title = val;
      this.advert.body = val;
      this.advert.subtitle = val;
      this.advert.cta = val;
    },
    checkPostUrl(val) {
      // eslint-disable-next-line no-useless-escape
      let regex = /^https\:\/\/(dev\.)?newswav.com/i;
      if (process.env.VUE_APP_APP_ENV !== 'production') {
        // eslint-disable-next-line no-useless-escape
        regex = /^https:\/\/dev-website\.newswav.dev/i;
      }
      return regex.test(val);
    },
  },
};
</script>
