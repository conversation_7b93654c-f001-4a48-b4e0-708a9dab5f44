<template>
  <div>
    <div class="d-flex flex-column">
      <ExistingCampaigns
        @selected-campaign="passSelected"
        class="mb-2"
        :toDisable="alreadyCreated"
      />
      <BaseCampaignForm
        class="mb-2"
        :campaign="campaign"
        :disableForm="alreadyCreated"
      />
      <CButtonToolbar class="justify-content-end mb-2">
        <CButton
          :disabled="alreadyCreated"
          color="primary"
          variant="outline"
          shape="pill"
          @click="createCampaign"
          >Submit</CButton
        >
      </CButtonToolbar>
    </div>
  </div>
</template>

<script>
import BaseCampaignForm from '@/components/Campaigns/BaseCampaignForm';
import ExistingCampaigns from '@/components/Campaigns/ExistingCampaigns';
export default {
  name: 'CampaignForm',
  components: { BaseCampaignForm, ExistingCampaigns },
  props: {
    campaign: {
      type: Object,
      required: true,
    },
    createdCampaign: {
      type: Object,
      required: true,
    },
  },
  computed: {
    alreadyCreated() {
      return this.createdCampaign.id !== '';
    },
  },
  methods: {
    resetCampaign() {
      this.$emit('reset-campaign');
    },
    createCampaign() {
      this.$swal
        .fire({
          icon: 'question',
          text: 'Campaign will be created. Continue?',
          showConfirmButton: true,
          showCancelButton: true,
        })
        .then((res) => {
          if (res.isConfirmed) {
            this.$emit('create-campaign');
          }
        });
    },
    passSelected(val) {
      this.$emit('selected-campaign', val);
    },
  },
};
</script>

<style scoped></style>
