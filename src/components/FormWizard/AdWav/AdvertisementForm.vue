<template>
  <div>
    <CTabs>
      <CTab title="Select / Create" active>
        <CCardBody>
          <CRow>
            <CCol>
              <ChooseExistingAd
                :ad_list="ad_list"
                @choose-existing="chooseAd"
              />
            </CCol>
          </CRow>
          <br />
          <CRow>
            <CCol sm="12" md="6" lg="6">
              <BaseAdvertisementForm
                :advertisement="advert"
                :adv="adv"
                :selected="selected"
                @clear-display="passClearDisplays"
                @show-display="passShowDisplays"
                @prefill-data="passPlaceholder"
              />
            </CCol>
            <CCol sm="12" md="6" lg="6">
              <AdPreview
                :adv="adv"
                :advertisement="advert"
                :cover="display_cover"
                :icon="display_icon"
              />
            </CCol>
          </CRow>
          <CRow>
            <CCol>
              <CButtonToolbar class="float-right">
                <CButton
                  color="primary"
                  variant="outline"
                  shape="pill"
                  @click="createAd"
                  >Submit</CButton
                >
              </CButtonToolbar>
            </CCol>
          </CRow>
        </CCardBody>
      </CTab>
      <CTab title="Created List">
        <CCardBody>
          <CDataTable
            sorter
            small
            :fields="ads_fields"
            :items-per-page="5"
            :items="created"
          >
            <template #action="{ item }">
              <td>
                <EditAdvertisementModal
                  :advertisement="item"
                  :adv_name="adv.name"
                />
              </td>
            </template>
          </CDataTable>
        </CCardBody>
      </CTab>
    </CTabs>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import Bugsnag from '@bugsnag/js';
import slugify from 'slugify';
import axios from 'axios';
import AdPreview from '@/components/Advertisements/AdPreview';
import EditAdvertisementModal from '@/components/Advertisements/EditAdvertisementModal';
import ChooseExistingAd from '@/components/Advertisements/ChooseExistingAd';
import BaseAdvertisementForm from '@/components/Advertisements/BaseAdvertisementForm';
import { adwavValidateValues } from '@/helpers/field-validate';
import * as headers from '@/helpers/headers';
export default {
  name: 'AdvertisementForm',
  components: {
    BaseAdvertisementForm,
    ChooseExistingAd,
    EditAdvertisementModal,
    AdPreview,
  },
  props: [
    'advert',
    'campaign',
    'display_cover',
    'display_icon',
    'adv',
    'created',
    'ad_list',
  ],
  data() {
    return {
      show: false,
      selected: false,
      ads_fields: [
        { key: 'type', sorter: true, label: 'Advertisement Type' },
        { key: 'body', sorter: true, label: 'Advertisement Body' },
        { key: 'title', sorter: true, label: 'Advertisement Title' },
        { key: 'language', sorter: true, label: 'Language' },
        { key: 'action', sorter: false, label: 'Action' },
      ],
    };
  },
  methods: {
    ...mapActions({
      createAdInCampaign: 'addAdvertInCampaign',
    }),
    chooseAd(send) {
      this.selected = true;
      this.$emit('selected-ad', send);
    },
    async createAd() {
      this.$swal
        .fire({
          icon: 'question',
          text: 'Advertisement will be created. Continue?',
        })
        .then(async (res) => {
          if (res.isConfirmed) {
            this.$store.commit('SET_GENERAL_LOADING', true);
            await this.handleAdvertFileUploads();

            if (adwavValidateValues('ads', this.advert)) {
              try {
                axios
                  .post(
                    process.env.VUE_APP_ADD_ADVERTISEMENT_ENDPOINT,
                    this.advert,
                    headers.createHeaders(this.$store.getters.getUserToken)
                  )
                  .then((res) => {
                    console.log(res);
                    res.data.forEach((data) => {
                      this.created.push(data);
                      this.ad_list.push(data);
                    });
                    this.$swal.fire({
                      icon: 'success',
                      text: 'Advertisement successfully created!',
                      showCancelButton: false,
                    });
                  })
                  .catch((error) => {
                    console.log(error.response.data);
                    let message = 'Check console for more info';
                    let title = 'Error';

                    if (error.response?.status === 401) {
                      message = 'Unauthorized. Check console for more info';
                      title = 'Unauthorized';
                    }

                    this.$notify({
                      group: 'error',
                      title: title,
                      type: 'error',
                      text: `${error.response?.status || 'Unknown'} : ${message}`,
                      duration: -1,
                      position: 'bottom right',
                      closeOnClick: true,
                    });

                    Bugsnag.notify(error);
                  });
              } catch (error) {
                console.log(error);
                let message = 'Check console for more info';
                let title = 'Error';

                if (error.response?.status === 401) {
                  message = 'Unauthorized. Check console for more info';
                  title = 'Unauthorized';
                }

                if (error.response) {
                  this.$notify({
                    group: 'error',
                    title: title,
                    type: 'error',
                    text: `${error.response?.status || 'Unknown'} : ${message}`,
                    duration: -1,
                    position: 'bottom right',
                    closeOnClick: true,
                  });

                  Bugsnag.notify(error);
                }
              } finally {
                this.$store.commit('SET_GENERAL_LOADING', false);
              }
            }
          }
        });
    },
    resetAd() {
      this.$emit('reset-ad');
    },

    async handleAdvertFileUploads() {
      if (
        this.display_cover &&
        this.display_cover.includes('/temp/') &&
        this.advert.cover_url === ''
      ) {
        this.advert.cover_url = await this.moveGoogleFile(this.display_cover);
      }

      if (
        this.display_thumbnail &&
        this.display_thumbnail.includes('/temp/') &&
        this.advert.thumbnail_url === ''
      ) {
        this.advert.thumbnail_url = await this.moveGoogleFile(
          this.display_thumbnail
        );
      }

      if (this.advert.type !== 'spotlight' && this.advert.type !== 'splash') {
        if (
          this.display_icon &&
          this.display_icon.includes('/temp/') &&
          this.advert.icon_url === ''
        ) {
          this.advert.icon_url = await this.moveGoogleFile(this.display_icon);
        }
      } else {
        this.advert.icon_url = '';
      }

      if (this.advert.type === 'sponsored') {
        this.advert.cover_url = this.advert.icon_url;
        this.advert.cta = this.advert.type;
      }

      // handle html splash ad
      if (this.advert.type === 'splash' && this.advert.media_type === 'web') {
        this.advert.cover_url = '';
        this.advert.landing_url = '';
        this.advert.cta = this.advert.type;

        await Promise.all(
          this.advert.ad_media.map(async (urlData, index) => {
            if (urlData.media_url.includes('/temp/')) {
              let url = await this.moveGoogleFile(urlData.media_url);
              this.advert.cover_url = url;
              this.advert.landing_url = this.advert.ad_media[index].landing_url;
              this.advert.ad_media[index].media_url = url;
            }
          })
        );
      }
    },
    async moveGoogleFile(file) {
      let split = file.split('/');
      let fileName = split[split.length - 1];
      let tempPath = 'adwav/temp/' + split[split.length - 1];
      let path = 'adwav/' + slugify(this.adv.name.toLowerCase()) + '/';
      let fileData = new FormData();
      fileData.append('tempFile', tempPath);
      fileData.append('newPath', path);
      fileData.append('fileName', fileName);
      try {
        let res = await axios.post(
          process.env.VUE_APP_MOVE_FILE_TO_FOLDER,
          fileData
        );
        return res.data.url;
      } catch (err) {
        Bugsnag.notify(err);
      }
    },
    passClearDisplays() {
      this.$emit('clear-display');
    },
    passShowDisplays(item) {
      this.$emit('show-display', item);
    },
    passPlaceholder(val) {
      this.$emit('prefill-data', val);
    },
  },
};
</script>

<style scoped></style>
