<template>
  <div>
    <BasePollForm :poll="poll" :from_wizard="from_wizard" />
    <br />
    <CRow>
      <CCol>
        <CButton
          @click="resetPoll"
          class="float-right"
          variant="outline"
          shape="pill"
          color="info"
          >Reset</CButton
        >
      </CCol>
    </CRow>
  </div>
</template>

<script>
import BasePollForm from '../../Polls/BasePollForm';
export default {
  name: 'PollTab',
  components: { BasePollForm },
  props: {
    poll: {
      type: Object,
    },
    from_wizard: {
      type: Boolean,
    },
  },
  methods: {
    resetPoll() {
      this.$emit('reset-poll');
    },
  },
};
</script>

<style scoped></style>
