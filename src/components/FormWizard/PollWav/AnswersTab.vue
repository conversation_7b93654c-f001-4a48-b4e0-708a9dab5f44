<template>
  <div>
    <CTabs>
      <CTab title="Create Answer">
        <CCardBody>
          <CRow>
            <CCol>
              <CInput
                readonly
                label="Created Poll Question"
                v-model="poll.text"
              />
            </CCol>
          </CRow>
          <CRow>
            <CCol>
              <BaseAnswerForm
                :answer="answer"
                :is_edit="false"
                :from_wizard="true"
                @pass-select="passSetValue"
              />
            </CCol>
            <CCol>
              <PollPreview :poll="poll" :created_ans="created_ans" />
            </CCol>
          </CRow>
          <br />
          <CRow>
            <CCol>
              <CButtonToolbar class="float-right">
                <CButton
                  type="reset"
                  color="info"
                  variant="outline"
                  shape="pill"
                  >Reset</CButton
                >
                <CButton
                  variant="outline"
                  shape="pill"
                  color="success"
                  @click="createAns"
                  :disabled="is_disabled"
                >
                  Create
                </CButton>
              </CButtonToolbar>
            </CCol>
          </CRow>
        </CCardBody>
      </CTab>
      <CTab title="Created Answer(s)">
        <CDataTable
          sorter
          pagination
          :items-per-page="5"
          :items="created_ans"
          :fields="answer_fields"
        >
          <template #action="{ item }">
            <td>
              <CButtonToolbar>
                <EditAnswerModal
                  :answer="item"
                  :is_edit="true"
                  :from_wizard="true"
                />
              </CButtonToolbar>
            </td>
          </template>
        </CDataTable>
      </CTab>
    </CTabs>

    <CRow> </CRow>
  </div>
</template>

<script>
import BaseAnswerForm from '../../Answers/BaseAnswerForm';
import EditAnswerModal from '../../Answers/EditAnswerModal';
import PollPreview from '../../Polls/PollPreview';

export default {
  name: 'AnswersTab',
  components: { PollPreview, EditAnswerModal, BaseAnswerForm },
  props: {
    poll: {
      type: Object,
      required: true,
    },
    answer: {
      type: Object,
      required: true,
    },
    created_ans: {
      type: Array,
    },
  },
  computed: {
    is_disabled() {
      let errors = [];
      Object.entries(this.answer).forEach(([prop, val]) => {
        if (prop === 'text' && val === '') {
          errors.push('text');
        }
        if (prop === 'is_feedback' && val === '') {
          errors.push('is_feedback');
        }
        if (prop === 'is_rate' && val.length === 0) {
          errors.push('is_rate');
        }
        if (prop === 'skip' && val === '') {
          errors.push('skip');
        }
      });
      return errors.length > 0;
    },
  },
  data() {
    return {
      answer_fields: [
        { key: 'text', label: 'Answer Text' },
        { key: 'action', sorter: false, label: 'Action' },
      ],
    };
  },
  methods: {
    createAns(e) {
      e.preventDefault();
      let msg = 'Answer will be created. Continue?';
      if (confirm(msg)) {
        this.$emit('create-answer');
      }
    },
    passSetValue() {
      this.$emit('set-select-values');
    },
  },
};
</script>

<style scoped></style>
