<template>
  <div>
    <CRow>
      <CCol>
        <CButton variant="outline" shape="pill" color="primary" @click="goBack"
          >Back</CButton
        >
      </CCol>
    </CRow>
    <br />
    <CCard>
      <CCardBody>
        <form-wizard
          :title="'Create New Poll & Answers'"
          :subtitle="''"
          @on-complete="onComplete"
          ref="wizard"
          color="#2eb85c"
        >
          <tab-content title="Poll Details" :before-change="createPoll">
            <PollTab :poll="poll" :from_wizard="true" @reset-poll="clearPoll" />
          </tab-content>
          <tab-content title="Poll Answers" :before-change="checkAnswers">
            <AnswersTab
              :answer="answer"
              :poll="createdPoll"
              :created_ans="createdAns"
              @create-answer="createAnswer"
            />
          </tab-content>
          <template slot="footer" scope="props">
            <div class="wizard-footer-left">
              <wizard-button
                v-if="props.activeTabIndex > 0 && !props.isLastStep"
                :style="props.fillButtonStyle"
                >Previous</wizard-button
              >
            </div>
            <div class="wizard-footer-right">
              <wizard-button
                v-if="!props.isLastStep"
                @click.native="props.nextTab()"
                class="wizard-footer-right"
                :style="props.fillButtonStyle"
                :disabled="is_disabled"
                >Next</wizard-button
              >

              <wizard-button
                v-else
                @click.native="onComplete"
                class="wizard-footer-right"
                :style="props.fillButtonStyle"
                >{{ props.isLastStep ? 'Finish' : 'Next' }}</wizard-button
              >
            </div>
          </template>
        </form-wizard>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import axios from 'axios';
import PollTab from './PollTab';
import AnswersTab from './AnswersTab';
import * as headers from '@/helpers/headers';
import Bugsnag from '@bugsnag/js';

export default {
  name: 'StartFormWizard',
  components: { AnswersTab, PollTab },
  data() {
    return {
      createdPoll: {},
      createdAns: [],
      poll: {
        text: '',
        text_MS: '',
        text_ZH: '',
        option_type: '',
        anonymous: '',
        status: 'scheduled',
        language: 'en',
        close_at: '',
        name: '',
        random: 'No',
        voting_status: '',
        published_date: '',
        is_sponsored: '',
        advertiser: '',
        max_desired_responses: '',
      },
      answer: {
        poll_id: '',
        is_feedback: '',
        is_rate: '',
        skip: '',
        text: '',
        text_MS: '',
        text_ZH: '',
        total_votes: 0,
      },
      config: {
        headers: {
          token: process.env.VUE_APP_TOKEN_VALUE,
        },
      },
    };
  },
  computed: {
    is_disabled() {
      let errors = [];
      Object.entries(this.poll).forEach(([prop, val]) => {
        if (prop === 'text' && val === '') {
          errors.unshift('text');
        }
        if (prop === 'name' && val === '') {
          errors.unshift('name');
        }
        if (prop === 'option_type' && val === '') {
          errors.unshift('option_type');
        }
        if (prop === 'close_at' && val === '') {
          errors.unshift('close_at');
        }
        if (prop === 'voting_status' && val === '') {
          errors.unshift('voting_status');
        }
        if (prop === 'is_sponsored' && val === 'Yes') {
          Object.entries(this.poll).forEach(([prop2, val2]) => {
            if (prop2 === 'advertiser' && val2 === '') {
              errors.unshift('advertiser');
            }
            if (prop2 === 'max_desired_responses' && !/[0-9]+/g.test(val2)) {
              errors.unshift('max_desired_responses');
            }
          });
        }
      });
      return errors.length > 0;
    },
  },
  methods: {
    isLastStep() {
      if (this.$refs.wizard) {
        return this.$refs.wizard.isLastStep;
      }
      return false;
    },
    goBack() {
      location.href = '/polls';
    },
    onComplete() {
      location.href = '/polls/show-poll/' + this.createdPoll.id;
    },
    async createPoll() {
      if (JSON.stringify(this.createdPoll) === '{}') {
        let success = false;
        let newPoll = {};
        Object.entries(this.poll).forEach(([prop, val]) => {
          newPoll[prop] = val;
        });
        newPoll.is_sponsored = newPoll.is_sponsored === 'Yes' ? 1 : 0;
        newPoll.random = newPoll.random === 'Yes' ? 1 : 0;
        await axios
          .post(
            process.env.VUE_APP_CREATE_POLL,
            newPoll,
            headers.createHeaders(
              this.$store.getters.getUserToken,
              process.env.VUE_APP_TOKEN_VALUE
            )
          )
          .then((res) => {
            success = true;
            this.createdPoll = res.data;
            this.createdPoll.is_sponsored =
              this.createdPoll.is_sponsored === 1 ? 'Yes' : 'No';
            this.createdPoll.random =
              this.createdPoll.random === 1 ? 'Yes' : 'No';
          })
          .catch((err) => {
            console.log(err.response.data);
            let message = 'Check console for more info';
            let title = 'Error';
            if (err.response.status == 401) {
              message = 'Unauthorized. Check console for more info';
              title = 'Unauthorized';
            }
            this.$notify({
              group: 'error',
              title: title,
              type: 'error',
              text: `${err.response.status} : ${message}`,
              duration: -1,
              position: 'bottom right',
              closeOnClick: true,
            });
            console.log(err);
          });
        if (success) {
          return true;
        } else {
          return false;
        }
      } else {
        return true;
      }
    },
    createAnswer() {
      this.answer.poll_id = this.createdPoll.id;
      this.$store.commit('SET_LOADING', true);
      axios
        .post(
          process.env.VUE_APP_CREATE_POLL_ANSWER,
          this.answer,
          headers.createHeaders(
            this.$store.getters.getUserToken,
            process.env.VUE_APP_TOKEN_VALUE
          )
        )
        .then((res) => {
          alert('Answer created successfully');
          this.createdAns.push(res.data[0]);
          console.log(this.createdAns);
          this.$store.commit('SET_LOADING', false);
        })
        .catch((err) => {
          console.log(err.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          this.$notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          console.log(err);
          Bugsnag.notify(err);
          this.$store.commit('SET_LOADING', false);
        });
    },
    clearPoll() {
      this.poll = {
        text: '',
        text_MS: '',
        text_ZH: '',
        option_type: '',
        anonymous: '',
        status: '',
        language: '',
        close_at: '',
        name: '',
        voting_status: '',
      };
    },
    checkAnswers() {
      if (this.createdAns.length === 0) {
        alert('Create answers for the poll before proceeding');
        return false;
      } else {
        return true;
      }
    },
  },
};
</script>

<style scoped></style>
