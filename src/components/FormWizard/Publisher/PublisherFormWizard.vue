<template>
  <div>
    <CRow>
      <CCol>
        <CButton variant="outline" shape="pill" color="primary" @click="goBack"
          >Back</CButton
        >
      </CCol>
    </CRow>
    <br />
    <CCard>
      <CCardBody>
        <form-wizard
          :title="'Add Publisher'"
          :subtitle="''"
          @on-complete="onComplete"
          ref="wizard"
          color="#2eb85c"
        >
          <tab-content title="Publisher Details" :before-change="addPublisher">
            <CRow>
              <CCol>
                <BasePublisherForm
                  :publisher="publisher_data"
                  :publisherMonitoring="publisher_monitoring_data"
                  @setImageLink="setImage"
                  :edit="false"
                />
              </CCol>
              <CCol>
                <CCard>
                  <CCardHeader>Publisher Logo</CCardHeader>
                  <CCardBody>
                    <div class="d-flex flex-row justify-content-center">
                      <img
                        v-if="logo_display"
                        :src="logo_display"
                        alt=""
                        class="logo_place"
                      />
                      <div v-else class="placeholder_img"></div>
                    </div>
                  </CCardBody>
                </CCard>
              </CCol>
            </CRow>
          </tab-content>
          <tab-content
            title="Publisher Channels"
            :before-change="checkChannels"
          >
            <CRow>
              <CCol>
                <BaseChannelForm :channel="channel" />
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CButtonToolbar class="float-right">
                  <CButton
                    color="info"
                    variant="outline"
                    shape="pill"
                    :disabled="cha_disabled"
                    @click="addChannel"
                  >
                    Create Channel
                  </CButton>
                </CButtonToolbar>
              </CCol>
            </CRow>
            <br />
          </tab-content>
          <template slot="footer" slot-scope="props">
            <div class="wizard-footer-left">
              <wizard-button
                v-if="props.activeTabIndex > 0 && !props.isLastStep"
                :style="props.fillButtonStyle"
              >
                Previous
              </wizard-button>
            </div>
            <div class="wizard-footer-right">
              <wizard-button
                v-if="!props.isLastStep"
                @click.native="props.nextTab()"
                class="wizard-footer-right"
                :style="props.fillButtonStyle"
                :disabled="is_disabled"
                >Next
              </wizard-button>

              <wizard-button
                v-else
                @click.native="onComplete"
                class="wizard-footer-right"
                :style="props.fillButtonStyle"
                >{{ props.isLastStep ? 'Finish' : 'Next' }}
              </wizard-button>
            </div>
          </template>
        </form-wizard>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import BasePublisherForm from '@/components/Publishers/BasePublisherForm';
import BaseChannelForm from '@/components/Publishers/BaseChannelForm';

export default {
  name: 'CreatePublisherWizard',
  components: { BaseChannelForm, BasePublisherForm },
  data() {
    return {
      createdPublisher: {},
      publisher_data: {
        name: '',
        project_type: 'publisher',
        description: '',
        imageLink: '',
        language: [],
        isReaderMode: 0,
        enabled: 0,
        verified: 0,
        ga_id: '',
        topics: [],
        autoFollow: 0,
        ad_share: 60,
        website_url: '',
      },
      publisher_monitoring_data: {
        status: 0,
        frequency: 'medium',
      },
      createdChannels: [],
      logo_display: '',
      channel: {
        host: '',
        content_type: '',
        ch_language: '',
        isReaderMode: 0,
        enabled: '',
      },
    };
  },
  mounted() {
    if (this.allTopics.length === 0) {
      this.listTopics();
    }
  },
  computed: {
    ...mapGetters({
      allTopics: 'getTopics',
    }),
    is_disabled() {
      let errors = [];
      let exclude = ['ga_id', 'topic'];
      Object.entries(this.publisher_data).forEach(([prop, val]) => {
        if (!exclude.includes(prop) && val === '') {
          errors.unshift(prop);
        }
      });
      return errors.length > 0;
    },
    cha_disabled() {
      let chaErrors = [];
      Object.entries(this.channel).forEach(([prop, val]) => {
        if (val === '') {
          chaErrors.unshift(prop);
        }
      });
      return chaErrors.length > 0;
    },
  },
  methods: {
    ...mapActions({
      listTopics: 'listTopics',
      createPublisher: 'createNewPublisher',
      updateMonitoringById: 'updatePublisherMonitorinById',
      createChannel: 'createPublisherChannel',
    }),
    goBack() {
      this.$router.push('/publishers');
    },
    onComplete() {
      if (this.checkChannels()) {
        this.$router.push('/publishers/' + this.createdPublisher.id);
      }
      // this.$router.push('/publishers/' + this.createdPublisher.id)
    },
    setImage(item) {
      this.publisher_data.imageLink = item.data;
      this.logo_display = item.data;
    },
    async addPublisher() {
      if (JSON.stringify(this.createdPublisher) === '{}') {
        let success = false;
        let pass = {};
        Object.entries(this.publisher_data).forEach(([prop, val]) => {
          pass[prop] = val;
        });
        pass.language = this.publisher_data.language.join(',');
        pass.topics =
          this.publisher_data.topics.length > 0
            ? this.publisher_data.topics.at(-1).value === '-1'
              ? []
              : this.publisher_data.topics.map((val) => {
                  return val.value;
                })
            : [];
        let publisherMonitor = {
          status: this.publisher_monitoring_data.status,
          frequency: this.publisher_monitoring_data.frequency,
        };
        await this.createPublisher(pass)
          .then((res) => {
            this.createdPublisher = res.data[0];
            success = true;
          })
          .then(() => {
            publisherMonitor.id = this.createdPublisher.id;
            this.updateMonitoringById(publisherMonitor);
          })
          .catch((err) => {
            console.log(err);
          });
        return success;
      } else {
        return true;
      }
    },
    addChannel() {
      this.$swal
        .fire({
          icon: 'question',
          text:
            'This will create content channels for ' +
            this.createdPublisher.name +
            '. Continue?',
        })
        .then(async (res) => {
          if (res.isConfirmed) {
            let pass = {};
            Object.entries(this.channel).forEach(([prop, val]) => {
              pass[prop] = val;
            });
            pass.publisher_id = this.createdPublisher.id;
            // pass.host = this.channel.host.join(',')
            await this.createChannel(pass)
              .then((res) => {
                this.createdChannels.unshift(res.data);
                this.$swal.fire({
                  icon: 'success',
                  text: `Successfully created channels for ${this.createdPublisher.name}`,
                  showCancelButton: false,
                });
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'error',
                  text: `Something went wrong in creating channels for ${this.createdPublisher.name}`,
                  showCancelButton: false,
                });
              });
          }
        });
    },
    checkChannels() {
      if (this.createdChannels.length > 0) {
        return true;
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'Please create channels for publisher before proceeding',
          showCancelButton: false,
        });
      }
    },
  },
};
</script>

<style scoped>
.placeholder_img {
  height: 150px;
  width: 150px;
  background-color: lightblue;
  border-radius: 50%;
  border: 1px solid #b2b2b2;
}

.logo_place {
  border-radius: 50%;
  border: 1px solid #b2b2b2;
  width: 150px;
  height: 150px;
}

@media (max-width: 750px) {
  .vue-form-wizard * {
    display: flex;
    flex-direction: column;
  }
}
</style>
