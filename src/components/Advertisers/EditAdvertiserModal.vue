<template>
  <div>
    <div v-show="checkAction('advertiser_edit')">
      <CButton
        color="warning"
        v-c-tooltip="'Edit Advertiser'"
        variant="outline"
        @click="show = true"
      >
        <CIcon name="cil-pencil"></CIcon>
      </CButton>
    </div>

    <CModal
      title="Edit Advertiser"
      :show.sync="show"
      :close-on-backdrop="false"
      centered
    >
      <OverlayLoader v-if="loading" />
      <CInput
        v-model="newAdvertiser.name"
        label="Advertiser Name"
        invalid-feedback="Advertiser name is required"
        :is-valid="newAdvertiser.name.length > 0 ? true : false"
      />
      <CInput
        v-model="newAdvertiser.phone"
        label="Advertiser Contact Number"
        placeholder="e.g 019-2345678"
      />
      <CInput
        v-model="newAdvertiser.email"
        label="Advertiser Email"
        placeholder="e.g <EMAIL>"
      />
      <template slot="footer">
        <CButton
          color="success"
          variant="outline"
          type="button"
          :disabled="checkInvestor"
          @click="editAdvertiser"
          >Submit</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
import OverlayLoader from '@/components/views/OverlayLoader';
export default {
  name: 'EditAdvertiserModal',
  components: { OverlayLoader },
  props: {
    advertiser: {
      type: Object,
      required: true,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      show: false,
    };
  },
  computed: {
    loading() {
      return this.$store.getters.getGeneralLoading;
    },
    newAdvertiser: {
      get() {
        let adv = {};
        Object.entries(this.advertiser).forEach(([prop, val]) => {
          adv[prop] = val;
        });
        return adv;
      },
      set(newVal) {
        return newVal;
      },
    },
  },
  methods: {
    editAdvertiser() {
      this.$swal
        .fire({
          icon: 'question',
          text: 'This will update the advertiser. Continue?',
        })
        .then((res) => {
          if (res.isConfirmed) {
            this.$store
              .dispatch('alterAdvertiser', this.newAdvertiser)
              .then(() => {
                this.$swal.fire({
                  icon: 'success',
                  text: 'Advertiser successfully updated!',
                });
                this.show = !this.show;
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'error',
                  text: 'Something went wrong in updating advertiser!',
                });
                this.show = !this.show;
              });
          }
        });
    },
  },
};
</script>

<style scoped></style>
