<template>
  <div>
    <CRow>
      <CCol>
        <BackButton />
      </CCol>
    </CRow>
    <br />
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader> List of Campaigns </CCardHeader>
          <CCardBody>
            <div>
              <div v-show="checkAction('campaign_add')">
                <router-link
                  :to="'create-campaign-ads/' + $route.params.adv_id"
                >
                  <CButton
                    class="float-right"
                    variant="outline"
                    color="success"
                    shape="pill"
                    >Create Campaign
                  </CButton>
                </router-link>
              </div>
            </div>
            <CDataTable
              :loading="campaignLoading"
              sorter
              table-filter
              small
              :items-per-page="5"
              :items="campaigns"
              :fields="campaign_fields"
              pagination
            >
              <template v-if="campaignLoading" #no-items-view>
                <div style="height: 300px"></div>
              </template>
              <template #type="{ item }">
                <td>
                  {{ item.type | ads_type }}
                </td>
              </template>
              <template #show_details="{ item }">
                <td>
                  <CButtonToolbar>
                    <EditCampaignModal
                      :disabled="checkInvestor"
                      :id="item.id"
                      :section="true"
                    />
                    <div v-show="checkAction('campaign_show')">
                      <CButton
                        @click="redirectTo(item.id)"
                        color="primary"
                        variant="outline"
                        v-c-tooltip="'Show Campaign'"
                      >
                        <CIcon name="cil-envelope-open"></CIcon>
                      </CButton>
                    </div>
                    <CButton
                      v-if="checkAction('campaign_delete')"
                      variant="outline"
                      color="danger"
                      v-c-tooltip="'Delete'"
                      @click="deleteCampaign(item.id)"
                    >
                      <CIcon name="cil-trash"></CIcon>
                    </CButton>
                  </CButtonToolbar>
                </td>
              </template>
            </CDataTable>
          </CCardBody>
        </CCard>
        <hr />
        <AdvertisementsTable
          :loading="adsLoading"
          :table_data="advertisements"
          :table_fields="ad_fields"
          :page="'advertiser'"
          :advertiser="advertiser"
        >
          <template #card-title> List of Advertisements </template>
        </AdvertisementsTable>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import EditCampaignModal from '@/components/Campaigns/EditCampaignModal';
import AdvertisementsTable from '@/components/Advertisements/AdvertisementsTable';
import BackButton from '@/components/common/BackButton';
import { ADS_TABLE_FIELDS, ADS_TYPE } from '@/constant/constants';
export default {
  name: 'ShowAdvertiser',
  components: { EditCampaignModal, AdvertisementsTable, BackButton },
  data() {
    return {
      ad_fields: ADS_TABLE_FIELDS,
      campaign_fields: [
        { key: 'id', sorter: true, label: 'Campaign ID' },
        { key: 'name', sorter: true, label: 'Name' },
        { key: 'start_date', sorter: true, label: 'Start Date' },
        { key: 'end_date', sorter: true, label: 'End Date' },
        { key: 'status', sorter: true, label: 'Status' },
        { key: 'total_budget', sorter: true, label: 'Budget(RM)' },
        { key: 'show_details', sorter: false, label: 'Action' },
      ],
    };
  },
  filters: {
    ads_type(val) {
      return ADS_TYPE[val];
    },
  },
  created() {
    this.listLocations();
    this.listInterests();
    this.listFeeds();
    this.listAdvertisers();
    this.listCampaigns(this.$route.params.adv_id);
    this.listAdvertisements(this.$route.params.adv_id);
  },
  computed: {
    ...mapGetters({
      campaigns: 'getAllCampaigns',
      advertisements: 'allAds',
      campaignLoading: 'getCampaignLoading',
      adsLoading: 'getAdsLoading',
      advertisers: 'allAdvertisers',
      currentAdvertiser: 'getAdvertiser',
    }),
    advertiser() {
      return this.advertisers.length > 0
        ? this.currentAdvertiser(parseInt(this.$route.params.adv_id))
        : {};
    },
  },
  methods: {
    ...mapActions({
      listLocations: 'listLocations',
      listInterests: 'listInterests',
      listFeeds: 'listFeeds',
      listAdvertisers: 'getAllAdvertisers',
      listCampaigns: 'getAllCampaign',
      listAdvertisements: 'getAllAds',
      removeCampaign: 'removeCampaign',
    }),
    deleteCampaign(id) {
      this.$swal
        .fire({
          icon: 'question',
          text: 'This will delete the campaign. Are you sure?',
        })
        .then((res) => {
          if (res.isConfirmed) {
            this.removeCampaign(id);
          }
        });
    },
    goBack() {
      this.$router.push('/advertisers');
    },
    redirectTo(id) {
      this.$router.push(this.$route.params.adv_id + '/show-campaign/' + id);
    },
  },
};
</script>
