<template>
  <div>
    <CCard>
      <CCardHeader> List of Advertisers </CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <add-advertisers />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol>
            <CDataTable
              :sorter-value="{ column: 'name', asc: true }"
              :key="advertisers.length"
              :loading="loading"
              sorter
              table-filter
              column-filter
              small
              items-per-page-select
              :items-per-page="5"
              :items="advertisers"
              :fields="fields"
              pagination
            >
              <template #no-items-view>
                <div style="height: 300px"></div>
              </template>
              <template #email="{ item }">
                <td>
                  {{ item.email | email }}
                </td>
              </template>
              <template #show_details="{ item }">
                <td>
                  <CButtonToolbar>
                    <EditAdvertiserModal :advertiser="item" />
                    <CButton
                      @click="redirectTo(item.id)"
                      color="primary"
                      variant="outline"
                    >
                      <CIcon name="cil-share"></CIcon>
                    </CButton>
                  </CButtonToolbar>
                </td>
              </template>
            </CDataTable>
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import AddAdvertisers from './AddAdvertiser';
import EditAdvertiserModal from '@/components/Advertisers/EditAdvertiserModal';

export default {
  name: 'ListAdvertisers',
  components: { EditAdvertiserModal, AddAdvertisers },
  data() {
    return {
      fields: [
        { key: 'name', sorter: true, label: 'Advertiser' },
        { key: 'id', sorter: true, label: 'Advertiser ID' },
        { key: 'email', sorter: true, label: 'Email' },
        { key: 'show_details', sorter: false, filter: false, label: 'Action' },
      ],
    };
  },
  computed: {
    ...mapGetters({
      advertisers: 'allAdvertisers',
      loading: 'getAdvertiserLoading',
      locations: 'getLocations',
      interests: 'getInterests',
      feeds: 'getFeeds',
    }),
  },
  filters: {
    email(val) {
      return val !== '' || val !== null ? val : '-';
    },
  },
  created() {
    if (this.locations.length == 0) this.listLocations();
    if (this.interests.length == 0) this.listInterests();
    if (this.feeds.length == 0) this.listFeeds();
    this.listAdvertisers();
  },
  methods: {
    ...mapActions({
      listAdvertisers: 'getAllAdvertisers',
      listFeeds: 'listFeeds',
      listLocations: 'listLocations',
      listInterests: 'listInterests',
    }),
    redirectTo(id) {
      this.$router.push('/advertisers/' + id);
    },
  },
};
</script>
