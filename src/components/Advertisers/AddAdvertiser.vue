<template>
  <div>
    <div v-show="checkAction('advertiser_add')">
      <CButton
        color="success"
        @click="addModal = true"
        shape="pill"
        class="float-right"
        variant="outline"
        >Add Advertiser
      </CButton>
    </div>
    <CModal
      title="Add Advertiser"
      color="default"
      :show.sync="addModal"
      @update:show="resetAdvertiser"
      centered
    >
      <OverlayLoader v-if="loading" />
      <CInput
        v-model="advertiser.name"
        label="Advertiser Name"
        placeholder="Enter advertiser name"
        invalid-feedback="Advertiser name is required"
        :is-valid="validator"
      />
      <CInput
        v-model="advertiser.phone"
        label="Advertiser Contact Number"
        placeholder="e.g 019-12345678"
      />
      <CInput
        v-model="advertiser.email"
        label="Advertiser Email"
        placeholder="e.g <EMAIL>"
      />
      <template slot="footer">
        <CButton
          color="success"
          variant="outline"
          type="button"
          :disabled="checkInvestor"
          @click="submitAdvertiser"
          >Add</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
import OverlayLoader from '../views/OverlayLoader';

export default {
  name: 'AddAdvertisers',
  components: { OverlayLoader },
  data() {
    return {
      addModal: false,
      advertiser: {
        name: '',
        phone: '',
        email: '',
      },
    };
  },
  computed: {
    loading() {
      return this.$store.getters.getLoading;
    },
  },
  methods: {
    submitAdvertiser() {
      this.$swal
        .fire({
          icon: 'question',
          text: 'Advertiser will be added. Continue?',
        })
        .then((res) => {
          if (res.isConfirmed) {
            this.$store
              .dispatch('addAdvertiser', this.advertiser)
              .then(() => {
                this.$swal.fire({
                  icon: 'success',
                  text: 'Advertiser is successfully added!',
                });
                this.addModal = !this.addModal;
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'error',
                  text: 'Something went wrong in adding advertiser!',
                });
              });
          }
        });
    },
    validator(val) {
      return val ? val !== '' : false;
    },
    resetAdvertiser() {
      this.advertiser = {
        name: '',
        phone: '',
        email: '',
      };
    },
  },
};
</script>

<style></style>
