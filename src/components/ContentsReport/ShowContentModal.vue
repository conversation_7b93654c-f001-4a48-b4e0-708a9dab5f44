<template>
  <div>
    <CButton
      class="float-right"
      variant="outline"
      color="primary"
      @click="show = true"
      v-c-tooltip="'View Content'"
      ><CIcon name="cil-justify-center"></CIcon
    ></CButton>
    <CModal
      title="Content Details"
      color="default"
      size="lg"
      :show.sync="show"
      centered
    >
      <hr />

      <CRow>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Published By</small><br />
            <strong class="h4">{{
              `${content.publisher_object.publisherName} (${content.publisher_object.id})`
            }}</strong>
          </CCallout>
        </CCol>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Publish Date</small><br />
            <strong class="h4">{{ `${content.published_date}` }}</strong>
          </CCallout>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Title</small><br />
            <strong class="h6">{{ content.title }}</strong>
          </CCallout>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Description</small><br />
            <strong class="h6">{{
              content.description + (content.length > 299 ? '' : '...')
            }}</strong>
          </CCallout>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Matched Keywords</small><br />
            <strong class="h6">{{
              this.matchedKeyword(content).join(',')
            }}</strong>
          </CCallout>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CButton color="primary" @click="visible = !visible"
            >Full Content</CButton
          >
          <CCollapse :show="visible">
            <div
              v-html="content.content"
              style="
                height: 500px !important;
                overflow-y: scroll;
                max-width: 100%;
              "
            ></div>
          </CCollapse>
        </CCol>
      </CRow>
      <hr />
      <CCardBody>
        <CDataTable :items="sortedReport" :fields="fields" hover sorter>
        </CDataTable>
      </CCardBody>
      <template slot="footer">
        <CButton
          :href="linkItem(content.entity_id)"
          target="_blank"
          color="primary"
          variant="outline"
          >Newswav Link</CButton
        >
        <CButton @click="show = false" color="danger">Close</CButton>
      </template>
    </CModal>
  </div>
</template>

<script>
export default {
  name: 'ShowContentModal',
  components: '',
  props: {
    content: {
      type: Object,
      required: true,
    },
    section: {
      type: Boolean,
    },
    dateRange: {
      type: Object,
      required: true,
    },
    hide: {
      type: String,
      required: true,
    },
    report: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      fields: [
        { key: 'reason_date', sorter: true, label: 'Report Date' },
        { key: 'reason_name', sorter: true, label: 'Report Reason' },
      ],
      show: false,
      visible: false,
    };
  },
  created() {},
  computed: {
    sortedReport() {
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      return this.content.reason_data.sort(function (a, b) {
        return new Date(b.reason_date) - new Date(a.reason_date);
      });
    },
  },
  methods: {
    convertTime(time) {
      let t = new Date(time);
      t = t.toLocaleDateString('en-GB');
      return t;
    },
    returnMedia(media) {
      let m = media[0] ? media[0] : '';
      let url = m.url ? m.url : '';
      return url;
    },
    formatDate(data) {
      const d = new Date(data);
      const ye = new Intl.DateTimeFormat('en', { year: 'numeric' }).format(d);
      const mo = new Intl.DateTimeFormat('en', { month: 'numeric' }).format(d);
      const da = new Intl.DateTimeFormat('en', { day: '2-digit' }).format(d);
      return `${ye}-${mo}-${da}`;
    },
    linkItem(id) {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      let link = process.env.VUE_APP_NEWSWAV_URL + id + '?r=' + r;
      return link;
    },
    sendAction(data, status) {
      let startDate = this.formatDate(this.dateRange.startDate);
      let endDate = this.formatDate(this.dateRange.endDate);
      let dateRange = { startDate, endDate };
      let adwav_user = `${this.$store.getters.getUser.name} (${this.$store.getters.getUser.id})`;
      if (status != data.review_status) {
        let msg = '';
        if (data.review_status == 'Hide' && status == 'Ignore') {
          msg = 'Content currently hidden, this action will unhide the content';
        } else {
          msg = `This will ${status} the content. Proceed?`;
        }
        this.$swal({
          title: 'Are you sure?',
          text: msg,
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Yes',
          cancelButtonText: 'Cancel',
          buttonsStyling: true,
        }).then((isConfirm) => {
          if (isConfirm.value === true) {
            this.$store.dispatch('reportAction', {
              data,
              status,
              adwav_user,
              dateRange,
            });
          }
        });
      } else {
        alert('No changes made');
      }
    },
    matchedKeyword(content) {
      //check report
      content = content.title + ' ' + content.content;
      let matched = [];
      const regexReport = new RegExp(this.report, 'gi');
      let foundReport = content.match(regexReport);
      //check hide
      const regexHide = new RegExp(this.hide, 'gi');
      let foundHide = content.match(regexHide);
      if (foundHide) {
        matched = [...matched, ...foundHide];
      }
      if (foundReport) {
        matched = [...matched, ...foundReport];
      }
      return [...new Set(matched)];
    },
  },
};
</script>

<style scoped>
img {
  max-width: 50% !important;
  height: auto;
}
</style>
