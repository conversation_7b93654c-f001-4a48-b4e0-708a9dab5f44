/* eslint-disable no-control-regex */
<template>
  <div>
    <CRow>
      <CCol>
        <CCard>
          <date-range-picker
            v-model="dateRange"
            class="float-left"
            :ranges="false"
            :locale-data="{ firstDay: 1, format: 'yyyy/mm/dd' }"
            @update="updateDate"
          >
          </date-range-picker>
        </CCard>
      </CCol>
    </CRow>
    <CRow>
      <CCol>
        <CDataTable
          :items="reports"
          :fields="fields"
          :columnFilter="{ external: true }"
          :sorter="{ external: true, resetable: false }"
          hover
          pagination
          :loading="loading"
          @update:column-filter-value="onFilterChange"
          @update:sorter-value="onSorterChange"
        >
          <template #title="{ item }">
            <td>
              {{ item.title | truncate(200, '...') }}
            </td>
          </template>
          <template #publisher_name="{ item }">
            <td>
              <span v-if="item.type && item.type === 'm'">(Mi<PERSON><PERSON>av)</span>
              {{ item.publisher_name }}
            </td>
          </template>
          <template #review_status="{ item }">
            <td>
              {{ item.review_status ? item.review_status : '-' }}
            </td>
          </template>
          <template #reviewed_by="{ item }">
            <td>
              {{ item.reviewed_by ? item.reviewed_by : '-' }}
            </td>
          </template>
          <template #reviewed_on="{ item }">
            <td>
              {{ item.reviewed_on ? item.reviewed_on : '-' }}
            </td>
          </template>
          <template #action="{ item }">
            <td>
              <CButtonToolbar>
                <ShowContentModal
                  :content="item"
                  :section="true"
                  :hide="hide"
                  :report="report"
                  :dateRange="dateRange"
                />
                <div v-if="checkAction('report_content_action')">
                  <span
                    v-if="
                      item.review_status === 'Unhidden' ||
                      item.review_status === '-' ||
                      item.review_status === 'Ignored'
                    "
                  >
                    <CButton
                      :id="item.id"
                      @click="sendAction(item, 'Hide')"
                      target="_blank"
                      color="danger"
                      variant="outline"
                      v-c-tooltip="'Hide/Unhide'"
                      ><font-awesome-icon icon="eye-slash"
                    /></CButton>
                    <CButton
                      @click="sendAction(item, 'Ignore')"
                      target="_blank"
                      color="warning"
                      variant="outline"
                      v-c-tooltip="'Ignore'"
                      ><font-awesome-icon icon="check"
                    /></CButton>
                  </span>
                  <span v-else>
                    <CButton
                      @click="sendAction(item, 'Unhide')"
                      target="_blank"
                      color="danger"
                      variant="outline"
                      v-c-tooltip="'Hide/Unhide'"
                      ><font-awesome-icon icon="eye"
                    /></CButton>
                  </span>
                </div>
              </CButtonToolbar>
            </td>
          </template>
        </CDataTable>
        <CPagination
          @update:activePage="goToPage"
          :pages="pagination.last_page"
          :activePage.sync="pagination.current_page"
        />
      </CCol>
    </CRow>
  </div>
</template>

<script>
import DateRangePicker from 'vue2-daterange-picker';
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';
import ShowContentModal from './ShowContentModal';
import { mapActions } from 'vuex';

export default {
  name: 'ReportContentTable',
  components: { DateRangePicker, ShowContentModal },
  data() {
    let startDate = new Date();
    let endDate = new Date();
    startDate.setDate(startDate.getDate() - 0);
    endDate.setDate(endDate.getDate() - 0);
    return {
      fields: [
        { key: 'review_status', sorter: true, filter: false, label: 'Status' },
        {
          key: 'updated_at',
          sorter: true,
          filter: false,
          label: 'Reported At',
        },
        { key: 'title', sorter: false, filter: true, label: 'Title' },
        {
          key: 'publisher_name',
          sorter: false,
          filter: false,
          label: 'Publisher',
        },
        { key: 'total', sorter: false, filter: false, label: 'Report Count' },
        { key: 'entity_id', sorter: true, filter: true, label: 'Unique ID' },
        {
          key: 'reviewed_by',
          sorter: true,
          filter: false,
          label: 'Reviewed By',
        },
        {
          key: 'reviewed_on',
          sorter: true,
          filter: false,
          label: 'Reviewed On',
        },
        {
          key: 'action',
          sorter: true,
          label: 'Actions',
          filter: false,
          _style: { width: '10px' },
        },
      ],
      dateRange: { startDate, endDate },
      hide: '',
      report: '',
      currentPage: 1,
      filters: {},
      sorters: {},
    };
  },
  filters: {
    date(val) {
      return val ? val.toLocaleString() : '';
    },
    truncate: function (text, length, suffix) {
      if (text.length > length) {
        return text.substring(0, length) + suffix;
      } else {
        return text;
      }
    },
  },
  computed: {
    reports() {
      return this.$store.getters.getContentReports;
    },
    pagination() {
      return this.$store.getters.getContentPagination;
    },
    loading() {
      return this.$store.getters.getContentReportsLoading;
    },
    random() {
      return this.$store.getters.getContentRandom;
    },
    report_keywords() {
      return this.$store.getters.getReportKeywords;
    },
    hide_keywords() {
      return this.$store.getters.getHideKeywords;
    },
  },
  async created() {
    let startDate = await this.formatDate(this.dateRange.startDate);
    let endDate = await this.formatDate(this.dateRange.endDate);

    await this.$store.dispatch('getContentReports', { startDate, endDate });
    await this.$store.dispatch('getKeywords').then(() => {
      this.report = this.createRegex(this.report_keywords.split(','));
      this.hide = this.createRegex(this.hide_keywords.split(','));
    });
    //await console.log(this.createRegex);
  },
  methods: {
    ...mapActions({
      getContentReports: 'getContentReports',
    }),

    async onFilterChange(updatedFilters) {
      clearTimeout(this.debounceTimer);

      this.filters = updatedFilters;
      this.currentPage = 1;
      let startDate = await this.formatDate(this.dateRange.startDate);
      let endDate = await this.formatDate(this.dateRange.endDate);

      this.debounceTimer = setTimeout(() => {
        let title = null;
        let id = null;
        let sortBy = null;
        let asc = null;
        if (this.filters.title) {
          title = this.filters.title;
        }
        if (this.filters.entity_id) {
          id = this.filters.entity_id;
        }

        if (this.sorters.column != null && this.sorters.asc != null) {
          sortBy = this.sorters.column;
          asc = this.sorters.asc;
        }

        this.getContentReports({
          startDate,
          endDate,
          id: id,
          title: title,
          sortBy: sortBy,
          asc: asc,
        });
      }, 1000);
    },
    async onSorterChange(updatedSorter) {
      this.sorters = updatedSorter;
      let sortBy = this.sorters.column;
      let asc = this.sorters.asc;
      this.currentPage = 1;
      let startDate = await this.formatDate(this.dateRange.startDate);
      let endDate = await this.formatDate(this.dateRange.endDate);
      let title = null;
      let id = null;
      if (this.filters.title) {
        title = this.filters.title;
      }
      if (this.filters.id) {
        id = this.filters.unique_id;
      }
      this.getContentReports({
        startDate,
        endDate,
        id: id,
        title: title,
        sortBy: sortBy,
        asc: asc,
      });
    },
    showReasons(items) {
      let a = '';
      let item;
      for (item of items) {
        a = a + item.reason_en + ',';
      }
      return a.slice(0, -1);
    },
    showReviewStatus(a) {
      if (a == 'Hide') {
        return 'Hidden';
      } else if (a == 'Unhide') {
        return 'Unhidden';
      } else {
        return 'Ignored';
      }
    },
    updateDate(data) {
      let startDate = data.startDate;
      let endDate = data.endDate;
      this.dateRange = { startDate, endDate };
      endDate = this.formatDate(data.endDate);
      startDate = this.formatDate(data.startDate);
      let title = null;
      let id = null;
      let sortBy = null;
      let asc = null;
      if (this.filters.title) {
        title = this.filters.title;
      }
      if (this.filters.entity_id) {
        id = this.filters.entity_id;
      }

      if (this.sorters.column != null && this.sorters.asc != null) {
        sortBy = this.sorters.column;
        asc = this.sorters.asc;
      }
      this.getContentReports({
        startDate,
        endDate,
        id: id,
        title: title,
        sortBy: sortBy,
        asc: asc,
      });
    },
    formatDate(data) {
      const d = new Date(data);
      const ye = new Intl.DateTimeFormat('en', { year: 'numeric' }).format(d);
      const mo = new Intl.DateTimeFormat('en', { month: 'numeric' }).format(d);
      const da = new Intl.DateTimeFormat('en', { day: '2-digit' }).format(d);
      return `${ye}-${mo}-${da}`;
    },
    sendAction(data, status) {
      let startDate = this.formatDate(this.dateRange.startDate);
      let endDate = this.formatDate(this.dateRange.endDate);
      let dateRange = { startDate, endDate };
      let adwav_user = `${this.$store.getters.getUser.name} (${this.$store.getters.getUser.id})`;
      if (status != data.review_status) {
        let msg = '';
        if (data.review_status == 'Hide' && status == 'Ignore') {
          msg = 'Content currently hidden, this action will unhide the content';
        } else {
          msg = `This will ${status} the content. Proceed?`;
        }
        this.$swal({
          title: 'Are you sure?',
          text: msg,
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Yes',
          cancelButtonText: 'Cancel',
          buttonsStyling: true,
        }).then((isConfirm) => {
          if (isConfirm.value === true) {
            this.$store.dispatch('reportContentAction', {
              data,
              status,
              adwav_user,
              dateRange,
            });
          }
        });
      } else {
        alert('No changes made');
      }
    },
    containsNonLatinCodepoints(s) {
      // eslint-disable-next-line no-control-regex
      return /[^\u0000-\u00ff]/.test(s);
    },
    createRegex(keywords) {
      let ascii = [];
      let unicode = [];

      keywords.forEach((keyword) => {
        if (this.containsNonLatinCodepoints(keyword)) {
          // eslint-disable-next-line no-useless-escape
          unicode.push(keyword.trim().replace(/[-[\]/{}()*+?.\\^$|]/g, '\\$&'));
          unicode.push(keyword.trim().replace(/[-[\]/{}()*+?.\\^$|]/g, '\\$&'));
        } else {
          // eslint-disable-next-line no-useless-escape
          ascii.push(keyword.trim().replace(/[-[\]/{}()*+?.\\^$|]/g, '\\$&'));
        }
      });
      ascii = ascii.join('|');
      unicode = unicode.join('|');
      let test = '(\\b(?:' + ascii + ')\\b)|(' + unicode + ')';

      return test;
    },
    async goToPage(page) {
      this.currentPage = page;
      let startDate = await this.formatDate(this.dateRange.startDate);
      let endDate = await this.formatDate(this.dateRange.endDate);
      let title = null;
      let id = null;
      let sortBy = null;
      let asc = null;
      if (this.filters.title) {
        title = this.filters.title;
      }
      if (this.filters.entity_id) {
        id = this.filters.entity_id;
      }

      if (this.sorters.column != null && this.sorters.asc != null) {
        sortBy = this.sorters.column;
        asc = this.sorters.asc;
      }
      this.getContentReports({
        startDate,
        endDate,
        id: id,
        title: title,
        sortBy: sortBy,
        asc: asc,
        currentPage: this.currentPage,
      });
    },
  },
};
</script>

<style scoped>
.slot {
  background-color: #aaa;
  padding: 0.5rem;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-black {
  color: #000;
}
</style>
