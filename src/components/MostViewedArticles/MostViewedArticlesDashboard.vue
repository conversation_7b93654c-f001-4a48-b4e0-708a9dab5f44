<template>
  <div>
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div class="filter-bar">
        <date-range-picker
          v-model="dateRange"
          :ranges="false"
          :single-date-picker="'range'"
          :locale-data="{ firstDay: 1, format: 'yyyy/mm/dd' }"
          @update="updateDate"
          class="filter-bar-item custom-datepicker"
        />

        <CInput
          v-model="searchUniqueId"
          placeholder="Unique ID"
          prepend-content
          class="filter-bar-item custom-input"
        >
        </CInput>
        <multiselect
          v-model="searchLanguage"
          :options="languages"
          :multiple="true"
          :searchable="false"
          :close-on-select="false"
          :clear-on-select="false"
          :preserve-search="true"
          placeholder="Select languages"
          class="filter-bar-item compact-multiselect"
        >
        </multiselect>
        <template>
          <multiselect
            v-model="searchProject"
            :options="projects"
            :multiple="true"
            :searchable="false"
            :close-on-select="false"
            :clear-on-select="false"
            :preserve-search="true"
            placeholder="Select Publisher Type"
            class="filter-bar-item compact-multiselect"
          >
            <template #selection="{ values }">
              <span v-if="values.length === projects.length">
                All Publisher Types
              </span>
              <span v-else> {{ values.length }} Publisher Type Selected </span>
            </template>
          </multiselect>
        </template>
      </div>
      <CButton @click="submit()" color="primary" class="search-button"
        >Search</CButton
      >
    </div>
    <div>
      <CDataTable
        :items="articles"
        :fields="fields"
        hover
        pagination
        :loading="loading"
      >
      </CDataTable>
    </div>
    <CPagination
      @update:activePage="lookAtPage"
      :pages="pages"
      :activePage.sync="currentPage"
    >
      <template slot="previous-button">&lsaquo; Prev</template>
      <template slot="next-button">Next &rsaquo;</template>
    </CPagination>
  </div>
</template>

<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>

<script>
import Multiselect from 'vue-multiselect';
import DateRangePicker from 'vue2-daterange-picker';
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';
import { mapGetters, mapActions, mapMutations } from 'vuex';

export default {
  name: 'MostViewedArticlesDashboard',
  components: { DateRangePicker, Multiselect },
  data() {
    let startDate = new Date();
    let endDate = new Date();
    return {
      fields: [
        { key: 'unique_id', label: 'Unique ID' },
        { key: 'title', label: 'Title' },
        { key: 'publisher', label: 'Publisher' },
        { key: 'project', label: 'Publisher Type' },
        { key: 'views', label: 'Views' },
        { key: 'reactions', label: 'Reactions' },
        { key: 'comments', label: 'Comments' },
        { key: 'shares', label: 'Shares' },
      ],
      dateRange: { startDate, endDate },
      languages: ['en', 'ms', 'zh'],
      projects: ['wavmaker', 'newswav', 'publisher', 'ugc'],
      searchUniqueId: null,
      searchLanguage: ['en', 'ms', 'zh'],
      searchProject: ['wavmaker', 'newswav', 'publisher', 'ugc'],
    };
  },
  computed: {
    ...mapGetters({
      articles: 'getMostViewedArticles',
      loading: 'getMostViewedArticlesLoading',
      pages: 'getMostViewedArticlesPages',
      nowPage: 'getMostViewedArticlesCurrentPage',
    }),
    currentPage: {
      get() {
        return this.nowPage;
      },
      set(val) {
        this.setNewPage(val);
      },
    },
  },
  methods: {
    ...mapMutations({
      setNewPage: 'SET_MOST_VIEWED_ARTICLES_CURRENT_PAGE',
    }),
    ...mapActions({
      getMostViewedArticles: 'getMostViewedArticles',
    }),
    submit() {
      if (!this.searchLanguage || this.searchLanguage.length === 0) {
        this.$toast.error('Please select at least one language');
        return;
      }
      if (!this.searchProject || this.searchProject.length === 0) {
        this.$toast.error('Please select at least one publisher type');
        return;
      }

      const params = {
        startDate: this.formatDate(this.dateRange.startDate),
        endDate: this.formatDate(this.dateRange.endDate),
        uniqueId: this.searchUniqueId,
        language: this.getLanguageParam(),
        project: this.getProjectParam(),
        currentPage: this.currentPage,
      };
      this.getMostViewedArticles(params);
    },
    updateDate(dateRange) {
      this.dateRange = dateRange;
    },
    getLanguageParam() {
      if (this.searchLanguage.length === this.languages.length) {
        return null;
      }
      return this.searchLanguage.join(',');
    },
    getProjectParam() {
      if (this.searchProject.length === this.projects.length) {
        return null;
      }
      return this.searchProject.join(',');
    },
    formatDate(date) {
      return date.toISOString().split('T')[0];
    },
    lookAtPage(page) {
      const params = {
        startDate: this.formatDate(this.dateRange.startDate),
        endDate: this.formatDate(this.dateRange.endDate),
        uniqueId: this.searchUniqueId,
        language: this.getLanguageParam(),
        project: this.getProjectParam(),
        currentPage: page,
      };
      this.getMostViewedArticles(params);
    },
  },
  mounted() {
    this.currentPage = 1;
    this.submit();
  },
};
</script>

<style scoped>
.filter-container {
  flex-wrap: wrap;
}

.filter-bar {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-bar-item {
  width: 15rem;
  min-width: 200px;
}

.search-button {
  margin-left: auto;
  width: auto !important;
  white-space: nowrap;
}

.custom-input >>> .form-control {
  height: 43px !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  border-radius: 4px !important;
}

.custom-datepicker >>> .form-control {
  height: 43px !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  border-radius: 4px !important;
  border-color: lightgray;
}

.compact-multiselect >>> .multiselect__tags {
  min-height: 43px !important;
}

/* Mobile and tablet responsive styles */
@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    align-items: stretch !important;
  }

  .filter-bar {
    flex-direction: column;
    width: 100%;
    gap: 15px;
    margin-bottom: 20px;
  }

  .filter-bar-item {
    width: 100%;
    min-width: unset;
  }

  .search-button {
    margin-left: 0;
    width: 100% !important;
    margin-top: 10px;
  }
}

@media (max-width: 1024px) {
  .filter-bar {
    gap: 15px;
  }

  .filter-bar-item {
    width: 12rem;
    min-width: 180px;
  }
}
</style>
