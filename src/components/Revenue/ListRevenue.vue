<template>
  <div>
    <CCard>
      <CCardHeader> Publishers Revenue </CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <CCard>
              <date-range-picker
                v-model="dateRange"
                :sorter-value="{ column: 'trackDate', asc: false }"
                class="float-left"
                :ranges="false"
                :locale-data="{ firstDay: 1, format: 'yyyy/mm/dd' }"
                @update="updateDate"
              >
                <template #ranges="ranges">
                  <div class="ranges">
                    <ul>
                      <li
                        v-for="(range, name) in ranges.ranges"
                        :key="name"
                        @click="ranges.clickRange(range)"
                      >
                        <b>{{ name }}</b>
                        <small class="text-muted"
                          >{{ range[0].toDateString() }} -
                          {{ range[1].toDateString() }}</small
                        >
                      </li>
                    </ul>
                  </div>
                </template>
              </date-range-picker>
            </CCard>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <multiselect
              id="pub"
              v-model="pub"
              :options="publishers"
              :multiple="false"
              track-by="id"
              label="name"
              :allow-empty="false"
              placeholder="Select publisher(s)"
              @input="onChange"
            >
              <span slot="noResult"
                >Oops! No publishers with the name. Consider changing the search
                query.</span
              >
            </multiselect>
            <br />
          </CCol>
        </CRow>
        <CCard>
          <CRow>
            <CCol md class="ml-1 mr-1">
              <CCallout color="danger">
                Impressions<br />
                <strong class="h4">{{
                  revloading || revpubloading
                    ? 'Loading...'
                    : format_numbers(rev_summary.total_imp)
                }}</strong>
              </CCallout>
            </CCol>
            <CCol md class="ml-1 mr-1">
              <CCallout color="warning">
                eCPM (MYR)<br />
                <strong class="h4">{{
                  revloading || revpubloading
                    ? 'Loading...'
                    : rev_summary.total_ecpm !== undefined
                      ? format_numbers(rev_summary.total_ecpm.toFixed(2))
                      : 'N/A'
                }}</strong>
              </CCallout>
            </CCol>
            <CCol md class="ml-1 mr-1">
              <CCallout color="success">
                Estimated Revenue (MYR)<br />
                <strong class="h4">{{
                  revloading || revpubloading
                    ? 'Loading...'
                    : rev_summary.total_est !== undefined
                      ? format_numbers(rev_summary.total_est.toFixed(2))
                      : 'N/A'
                }}</strong>
              </CCallout>
            </CCol>
          </CRow>
        </CCard>
        <CRow>
          <CCol>
            <CButton
              v-if="revenues.length > 0 && checkAction('list_revenue')"
              @click="exportToCSV()"
              style="margin: 5px"
              color="success"
              class="float-right"
              >Download CSV</CButton
            >
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CDataTable
              v-bind:loading="revloading || revpubloading"
              :sorter="{ external: true, resetable: false }"
              small
              :items="revenues"
              :fields="fields"
              pagination
              @update:sorter-value="onSorterChange"
            >
              <template #trackDate="{ item }">
                <td>
                  {{ item.trackDate ? formatDate(item.trackDate, '/') : '-' }}
                </td>
              </template>
              <template #source="{ item }">
                <td>
                  {{ displayName(item.source) }}
                </td>
              </template>
              <template #total_imp="{ item }">
                <td>
                  {{
                    item.total_imp != null
                      ? format_numbers(item.total_imp)
                      : '-'
                  }}
                </td>
              </template>
              <template #ecpm="{ item }">
                <td>
                  {{
                    item.ecpm != null
                      ? format_numbers(item.ecpm.toFixed(2))
                      : '-'
                  }}
                </td>
              </template>
              <template #total_est="{ item }">
                <td>
                  {{
                    item.total_est != null
                      ? format_numbers(item.total_est.toFixed(2))
                      : '-'
                  }}
                </td>
              </template>
            </CDataTable>
            <CPagination
              @update:activePage="goToPage"
              :pages="pagination.last_page"
              :activePage.sync="pagination.current_page"
            />
          </CCol>
        </CRow>
        <hr />
      </CCardBody>
    </CCard>
    <CCard>
      <CCardHeader>Import Bonus</CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <vue-csv-import
              v-show="checkAction('submit_bonus')"
              v-model="csv"
              :map-fields="['Date', 'Publisher ID', 'Bonus']"
              :auto-match-fields="true"
            >
            </vue-csv-import>
            <CButtonToolbar class="float-right">
              <CButton
                v-show="checkUpload()"
                @click="submitCSV"
                color="success"
                variant="outline"
                shape="pill"
                >Upload
              </CButton>
            </CButtonToolbar>
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<style>
@media only screen and (max-width: 541px) {
  .daterangepicker {
    width: 100% !important;
  }

  .daterangepicker .drp-calendar {
    display: none;
    max-width: 100%;
    width: 100%;
    flex: 0 0 98%;
  }

  .daterangepicker .drp-calendar.left {
    padding: 8px;
    margin: auto;
  }

  .daterangepicker .drp-calendar.right {
    padding: 8px;
    margin: auto;
  }
}
</style>
<script>
import DateRangePicker from 'vue2-daterange-picker';
import Multiselect from 'vue-multiselect';
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';
import VueCsvImport from '@/components/CSVReader/CSVReader';
import { mapActions } from 'vuex';

export default {
  name: 'ListCPM',
  components: { DateRangePicker, Multiselect, VueCsvImport },
  computed: {
    publishers() {
      return this.$store.getters.getRevPublishers;
    },
    revenues() {
      return this.$store.getters.getPaginatedRevenues;
    },
    rev_summary() {
      return this.$store.getters.getRevSummary;
    },
    revloading() {
      return this.$store.getters.getREVLoading;
    },
    revpubloading() {
      return this.$store.getters.getRevPubLoading;
    },
    pagination() {
      return this.$store.getters.getRevenuePagination;
    },
  },
  data() {
    let startDate = new Date();
    let endDate = new Date();
    startDate.setDate(startDate.getDate() - 7);
    endDate.setDate(endDate.getDate() - 1);
    return {
      fields: [
        { key: 'trackDate', sorter: true, label: 'Date' },
        { key: 'source', sorter: true, label: 'Source' },
        { key: 'total_imp', sorter: true, label: 'Impressions' },
        { key: 'ecpm', sorter: true, label: 'eCPM (MYR)' },
        { key: 'total_est', sorter: true, label: 'Estimated Revenue (MYR)' },
      ],
      dateRange: { startDate, endDate },
      pub: { id: -1, name: 'All Publishers' },
      csv: null,
      currentPage: 1,
      sorters: {},
      columnMapping: {
        trackDate: 'trackDate',
        source: 'source',
        total_imp: 'imp',
        ecpm: 'ecpm',
        total_est: 'final_revenue',
      },
    };
  },
  filters: {},
  async created() {
    await this.getRevPublishers();
    await this.updateData();
  },
  methods: {
    ...mapActions({
      getPaginatedRevenues: 'getPaginatedRevenues',
      getAllRevenues: 'getAllRevenues',
      getRevPublishers: 'getRevPublishers',
    }),
    async onSorterChange(updatedSorter) {
      this.sorters = updatedSorter;
      let sortBy = this.sorters.column;
      let asc = this.sorters.asc;
      const mappedSortBy = this.columnMapping[sortBy] || null;
      let endDate = await this.formatDate(this.dateRange.endDate);
      let startDate = await this.formatDate(this.dateRange.startDate);
      let publisher_ids = this.pub.id;
      this.getPaginatedRevenues({
        startDate,
        endDate,
        publisher_ids,
        sortBy: mappedSortBy,
        asc: asc,
      });
    },
    updateDate(data) {
      let startDate = data.startDate;
      let endDate = data.endDate;
      this.dateRange = { startDate, endDate };
      this.updateData();
    },
    async updateData() {
      let endDate = this.formatDate(this.dateRange.endDate);
      let startDate = this.formatDate(this.dateRange.startDate);
      let publisher_ids = this.pub.id;
      let sortBy = null;
      let asc = null;
      let mappedSortBy = null;
      if (this.sorters.column != null && this.sorters.asc != null) {
        sortBy = this.sorters.column;
        asc = this.sorters.asc;
        mappedSortBy = this.columnMapping[sortBy] || null;
      }
      await this.getPaginatedRevenues({
        startDate,
        endDate,
        publisher_ids,
        currentPage: this.currentPage,
        sortBy: mappedSortBy,
        asc: asc,
      });
    },
    formatDate(data, separator = '-') {
      const d = new Date(data);
      const ye = new Intl.DateTimeFormat('en', { year: 'numeric' }).format(d);
      const mo = new Intl.DateTimeFormat('en', { month: '2-digit' }).format(d);
      const da = new Intl.DateTimeFormat('en', { day: '2-digit' }).format(d);
      return `${ye}${separator}${mo}${separator}${da}`;
    },
    onChange(value) {
      this.pub = value;
      this.updateData();
    },
    format_numbers(val) {
      if (val) {
        return val.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
      }
      return val;
    },
    displayName(name) {
      let n = 'test';
      switch (name) {
        case 'adwav':
          n = 'Adwav-Premium';
          break;
        case 'adwav-cheap':
          n = 'Adwav-Cheap';
          break;
        case 'fb':
          n = 'Facebook';
          break;
        case 'hms':
          n = 'Huawei';
          break;
        case 'admob':
          n = 'Google Admob';
          break;
        default:
          n = name;
      }
      return n;
    },
    async exportToCSV() {
      let BOM = '\uFEFF';
      let dateFrom = this.formatDate(this.dateRange.startDate, '');
      let dateTo = this.formatDate(this.dateRange.endDate, '');
      let csv = '';
      let publishers_name = this.pub.name;
      csv += '\nPublisher Name, Date From, Date To\n';
      csv += `"${publishers_name}", ${dateFrom}, ${dateTo} \n\n`;
      csv += 'Summary, Impressions, eCPM (MYR), Estimated Revenue (MYR)\n';
      csv += `\t, ${this.rev_summary.total_imp}, ${this.rev_summary.total_ecpm}, ${this.rev_summary.total_est},\n\n`;
      csv += 'Daily Breakdown, \t, \t,\n';
      csv +=
        'Date, Source, Impressions, eCPM (MYR), Estimated Revenue (MYR) \n';
      const allRevenues = await this.getAllPublisherRevenues();

      allRevenues.forEach((rev) => {
        csv += `"${rev.trackDate}", ${this.displayName(rev.source)}, ${rev.total_imp}, ${rev.ecpm}, ${rev.total_est}, \n`;
      });
      let reportName =
        publishers_name +
        '_MonetizationReport_' +
        dateFrom +
        '-' +
        dateTo +
        '.csv';
      let hiddenElement = document.createElement('a');
      hiddenElement.href =
        'data:text/csv;charset=utf-8,' + encodeURIComponent(BOM + csv);
      hiddenElement.target = '_blank';
      hiddenElement.download = reportName;
      hiddenElement.click();
    },
    submitCSV() {
      let bonus = { bonus: this.csv };
      this.$store.dispatch('submitBonus', bonus);
    },
    checkUpload() {
      if (this.csv == null || this.csv == '') {
        return false;
      }
      return true;
    },
    async goToPage(page) {
      this.currentPage = page;
      let endDate = await this.formatDate(this.dateRange.endDate);
      let startDate = await this.formatDate(this.dateRange.startDate);
      let publisher_ids = this.pub.id;
      let sortBy = null;
      let asc = null;
      let mappedSortBy = null;

      if (this.sorters.column != null && this.sorters.asc != null) {
        sortBy = this.sorters.column;
        asc = this.sorters.asc;
        mappedSortBy = this.columnMapping[sortBy] || null;
      }
      this.getPaginatedRevenues({
        startDate,
        endDate,
        publisher_ids,
        currentPage: this.currentPage,
        sortBy: mappedSortBy,
        asc: asc,
      });
    },
    async getAllPublisherRevenues() {
      let startDate = this.formatDate(this.dateRange.startDate);
      let endDate = this.formatDate(this.dateRange.endDate);
      let publisher_ids = this.pub.id;
      const data = await this.getAllRevenues({
        startDate,
        endDate,
        publisher_ids,
      });
      return data;
    },
  },
};
</script>
