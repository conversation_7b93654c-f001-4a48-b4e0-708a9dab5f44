<template>
  <div>
    <div>
      <CButton @click="refresh()" color="primary" class="mb-2">
        Refresh
      </CButton>
      <CButton
        @click="exportData()"
        :disabled="loading"
        color="success"
        class="mb-2 ml-2"
      >
        Export
      </CButton>
    </div>
    <div class="container px-0">
      <date-range-picker
        v-model="dateRange"
        :ranges="false"
        :locale-data="{ firstDay: 1, format: 'yyyy/mm/dd' }"
        class="w-100"
        @update="updateDate"
      />
    </div>
    <div>
      <CRow class="d-flex justify-content-between">
        <CCol class="col-2">
          <CSelect :options="type" v-model="selected" class="w-100" />
        </CCol>
        <CCol class="col-8">
          <input
            class="form-control w-100"
            type="text"
            placeholder="Search something"
            v-model="searchKeyword"
            v-on:keyup.enter="search(encodedSearchKeyword, selected)"
          />
        </CCol>
        <CCol class="col-2">
          <CButton
            class="w-100"
            variant="outline"
            color="success"
            @click="search(encodedSearchKeyword, selected)"
          >
            Search
            <CIcon name="cil-magnifying-glass"></CIcon>
          </CButton>
        </CCol>
      </CRow>

      <CDataTable
        :items="processedContents"
        :fields="fields"
        hover
        pagination
        :loading="loading"
      >
        <template #published_date="{ item }">
          <td>
            {{ item.published_date | date }}
          </td>
        </template>

        <template #view_count="{ item }">
          <td>
            {{ item.total_views }}
          </td>
        </template>
        <template #post="{ item }">
          <td>
            {{ item.truncatedContent }}
          </td>
        </template>
        <template #stats="{ item }">
          <td>
            {{
              `${item.reactions.total_reaction} / ${item.total_comments} / ${item.total_shares}`
            }}
          </td>
        </template>
        <template #username="{ item }">
          <td>
            {{
              item.user_profile.username == ''
                ? '-'
                : item.user_profile.username
            }}
          </td>
        </template>
        <template #flagged="{ item }">
          <td>
            {{ item.is_flagged == true ? `yes - ${item.flagged_at}` : 'no' }}
          </td>
        </template>
        <template #action="{ item, index }">
          <td>
            <MikrowavPreview :mikrowav="item" class="mx-1" />
            <CButton
              v-if="showWhichButtons(item)"
              :id="index"
              @click="updateFlagContent(item, 'hide')"
              color="danger"
              variant="outline"
              v-c-tooltip="'Toggle Hide/Unhide'"
              class="mx-1"
              ><font-awesome-icon icon="eye-slash" />
            </CButton>
            <CButton
              v-else
              :id="index"
              @click="updateFlagContent(item, 'unhide')"
              color="danger"
              variant="outline"
              v-c-tooltip="'Toggle Hide/Unhide'"
              class="mx-1"
            >
              <font-awesome-icon icon="eye" />
            </CButton>
          </td>
        </template>
      </CDataTable>
    </div>
    <CPagination
      @update:activePage="lookAtPage"
      :pages="pages"
      :activePage.sync="currentPage"
    >
      <template slot="previous-button">&lsaquo; Prev</template>
      <template slot="next-button">Next &rsaquo;</template>
    </CPagination>
  </div>
</template>

<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>

<script>
import { mapGetters, mapActions, mapMutations } from 'vuex';
import DateRangePicker from 'vue2-daterange-picker';
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';
import MikrowavPreview from '@/components/AllMikrowav/MikrowavPreview';
export default {
  name: 'AllMikrowav',
  components: { DateRangePicker, MikrowavPreview },
  data() {
    let startDate = new Date();
    let endDate = new Date();
    startDate.setDate(startDate.getDate() - 7);
    endDate.setDate(endDate.getDate() - 0);
    return {
      collapse: false,
      innerCollapse: false,
      searchKeyword: '',
      allPublishersSelected: true,
      searchQuery: '',
      selected: 'unique_id',
      fields: [
        {
          key: 'published_date',
          filter: false,
          label: 'Published Date',
        },
        { key: 'view_count', filter: false, label: 'Views' },
        { key: 'unique_id', filter: false, label: 'Unique ID' },
        { key: 'post', filter: false, label: 'Post' },
        { key: 'username', filter: false, label: 'Username' },
        { key: 'user_id', filter: false, label: 'User ID' },
        {
          key: 'stats',
          filter: true,
          label: 'R / C / S',
        },
        { key: 'flagged', filter: false, label: 'Hidden' },
        {
          key: 'action',
          label: 'Action',
          filter: true,
          _style: { width: '10px' },
        },
      ],
      type: [
        { value: 'unique_id', label: 'Unique ID' },
        { value: 'content', label: 'Content' },
        { value: 'username', label: 'Username' },
      ],
      dateRange: { startDate, endDate },
    };
  },
  filters: {
    date(val) {
      if (!val) return '';
      const date = new Date(val);
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = String(date.getFullYear()).slice(-2);
      return `${day}/${month}/${year}`;
    },
    truncate(text, length, suffix) {
      if (text.length > length) {
        return text.substring(0, length) + suffix;
      } else {
        return text;
      }
    },
  },
  async created() {
    await this.getMikrowavContent();
  },
  computed: {
    ...mapGetters({
      mikrowavs: 'getMikrowavContents',
      pages: 'getMikrowavPages',
      nowPage: 'getMikrowavCurrentPage',
    }),
    currentPage: {
      get() {
        return this.nowPage;
      },
      set(val) {
        this.setNewPage(val);
      },
    },
    loading() {
      return this.$store.getters.getMikrowavContentLoading;
    },
    processedContents() {
      if (!Array.isArray(this.mikrowavs.data)) {
        return [];
      }
      return this.mikrowavs.data.map((item) => {
        item.truncatedContent = this.$options.filters.truncate(
          item.content,
          45,
          '...'
        );
        return item;
      });
    },
    encodedSearchKeyword() {
      return encodeURIComponent(this.searchKeyword);
    },
  },
  methods: {
    ...mapMutations({
      setNewPage: 'SET_MIKROWAV_CURRENT_PAGE',
    }),
    ...mapActions({
      getMikrowavContent: 'getMikrowavContent',
      exportToExcel: 'exportToExcel',
      markHide: 'hideMikrowavResource',
      markUnhide: 'showMikrowavResource',
    }),
    onChange() {
      this.getMikrowavsdContent();
    },
    handleSearchChange(query) {
      this.searchQuery = query;
    },
    refresh() {
      let startDate = new Date();
      let endDate = new Date();
      startDate.setDate(startDate.getDate() - 7);
      endDate.setDate(endDate.getDate() - 0);
      this.searchKeyword = '';
      this.dateRange = { startDate, endDate };
      this.selected = 'unique_id';
      const defaultSetting = {
        keyword: this.encodedSearchKeyword,
        type: this.type,
        startDate: this.dateRange.startDate,
        endDate: this.dateRange.endDate,
        page: this.currentPage,
      };
      this.getMikrowavContent(defaultSetting);
    },
    updateFlagContent(mikrowav, status) {
      this.$swal({
        icon: 'question',
        title: 'Are you sure?',
        text: `This will ${status} the mikrowav. Proceed?`,
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes',
        cancelButtonText: 'Cancel',
        buttonsStyling: true,
      }).then(async (isConfirm) => {
        if (isConfirm.value === true) {
          if (status === 'hide') {
            await this.markHide(mikrowav);
          } else if (status === 'unhide') {
            await this.markUnhide(mikrowav);
          }
        }
      });
    },
    showWhichButtons(content) {
      return content.is_flagged === false;
    },
    search(key, type) {
      if (!key) {
        alert('Search box is empty');
        return;
      }
      if (!type) {
        alert('No type selected');
        return;
      }
      let newPage = {
        keyword: key,
        type: type,
        startDate: this.dateRange.startDate,
        endDate: this.dateRange.endDate,
        page: this.currentPage,
      };
      this.getMikrowavContent(newPage);
    },
    updateDate() {
      let filter = {
        keyword: this.encodedSearchKeyword || '',
        type: this.selected || 'unique_id',
        startDate: this.dateRange.startDate,
        endDate: this.dateRange.endDate,
        page: this.currentPage,
      };
      this.getMikrowavContent(filter);
    },
    exportData() {
      let filter = {
        keyword: this.encodedSearchKeyword || '',
        type: this.selected || 'unique_id',
        startDate: this.dateRange.startDate,
        endDate: this.dateRange.endDate,
        page: this.currentPage,
      };
      this.exportToExcel(filter);
    },
    lookAtPage(pageNumber) {
      this.currentPage = pageNumber;
      let params = {
        keyword: this.encodedSearchKeyword || '',
        startDate: this.dateRange.startDate,
        endDate: this.dateRange.endDate,
        type: this.selected || 'unique_id',
        currentPage: pageNumber,
      };
      this.getMikrowavContent(params);
    },
  },
};
</script>
