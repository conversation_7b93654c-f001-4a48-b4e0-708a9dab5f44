<template>
  <div class="mikrowav-preview">
    <CButton
      @click="show = true"
      color="primary"
      v-c-tooltip="'View Post'"
      variant="outline"
    >
      <CIcon name="cil-share"></CIcon>
    </CButton>

    <CModal
      title="Preview"
      :show.sync="show"
      :close-on-backdrop="false"
      centered
    >
      <div>
        <div style="display: flex; gap: 0.5rem">
          <img
            :src="mikrowav.user_profile.profile_picture"
            alt="Profile Picture"
            class="profile-pic"
          />
          <p>{{ mikrowav.user_profile.username }}</p>
        </div>

        <p>{{ mikrowav.content }}</p>
        <img
          v-if="mikrowav.media[0]"
          :src="mikrowav.media[0].url"
          class="media"
        />

        <div v-if="mikrowav.repost_content">
          <div
            v-if="mikrowav.repost_content.content_type === 'article'"
            style="display: flex"
            :class="{
              'origin-content no-thumbnail':
                !mikrowav.repost_content.original_url,
              'origin-content': mikrowav.repost_content.original_url,
            }"
          >
            <img
              :src="mikrowav.repost_content.original_url"
              alt="Thumbnail Picture"
              class="thumb-pic"
            />
            <div style="padding-top: 0.5rem">
              <div style="display: flex; gap: 0.5rem">
                <img
                  :src="mikrowav.repost_content.publisher.logo"
                  alt="Profile Picture"
                  class="profile-pic"
                />
                <p>{{ mikrowav.repost_content.publisher.name }}</p>
              </div>
              <p>{{ truncateText(mikrowav.repost_content.title) }}</p>
            </div>
          </div>
          <div
            v-if="mikrowav.repost_content.content_type === 'mikrowav'"
            style="display: flex"
            :class="{
              'origin-content no-thumbnail': !mikrowav.repost_content.media[0],
              'origin-content': mikrowav.repost_content.media[0],
            }"
          >
            <img
              v-if="mikrowav.repost_content.media[0]"
              :src="mikrowav.repost_content.media[0].url"
              alt="Thumbnail Picture"
              class="thumb-pic"
            />
            <div style="padding-top: 0.5rem">
              <div
                v-if="mikrowav.repost_content.user_profile"
                style="display: flex; gap: 0.5rem"
              >
                <img
                  :src="mikrowav.repost_content.user_profile.profile_picture"
                  alt="Profile Picture"
                  class="profile-pic"
                />
                <p>{{ mikrowav.repost_content.user_profile.username }}</p>
              </div>
              <p>{{ truncateText(mikrowav.repost_content.content) }}</p>
            </div>
          </div>
        </div>
      </div>
    </CModal>
  </div>
</template>

<script>
export default {
  name: 'MikrowavPreview',
  props: {
    mikrowav: {
      type: Object,
      required: true,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      show: false,
    };
  },
  methods: {
    truncateText(text, length = 100) {
      if (text && text.length > length) {
        return text.substring(0, length) + '...';
      }
      return text;
    },
  },
};
</script>

<style lang="scss" scoped>
.mikrowav-preview {
  display: inline-block;

  .profile-pic {
    width: 20px;
    height: 20px;
    border-radius: 50%; /* Makes the image circular */
    object-fit: cover;
  }

  .origin-content {
    display: flex;
    border: 1px solid #dedede;
    border-radius: 10px; /* Rounds the container */
    overflow: hidden;
    gap: 0.3rem;
  }

  .thumb-pic {
    width: 100px;
    height: 105px;
    object-fit: cover;
  }

  .media {
    width: 100%;
    max-height: 250px;
    object-fit: cover;
  }

  .no-thumbnail {
    padding: 0.5rem;
  }
}
</style>
