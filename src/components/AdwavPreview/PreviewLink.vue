<template>
  <div>
    <CRow>
      <CCol>
        <CCard v-show="checkAction('preview_link_create')">
          <CCardHeader>Preview Link Builder</CCardHeader>
          <CCardBody>
            <CInput
              label="Campaign IDs"
              v-model="campaign_ids"
              :is-valid="campaign_ids !== ''"
              placeholder="Input the campaign IDs separated by comma (,)"
              invalid-feedback="Please enter at least 1 campaign ID"
            />
            <CButton
              variant="outline"
              color="success"
              block
              :disabled="campaign_ids.length === 0 || load"
              @click="createPreview"
            >
              Generate Preview Link
            </CButton>
            <br />
            <div v-if="load">
              <div class="d-flex flex-row justify-content-center">
                <CSpinner color="success" />
              </div>
            </div>
            <div v-else-if="!load && preview_link !== ''">
              <CInput label="Preview Link" disabled v-model="preview_link">
                <template #append>
                  <CButton
                    @click="copyURL(preview_link)"
                    style="padding-top: 0; padding-bottom: 0"
                    v-c-tooltip="'Copy'"
                    ><CIcon name="cil-clone"></CIcon
                  ></CButton>
                </template>
              </CInput>
            </div>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader> Preview Links History </CCardHeader>
          <CCardBody>
            <CDataTable
              :sorter-value="{ column: 'created_at', asc: false }"
              :key="preview_links_list.length"
              :loading="load"
              sorter
              pagination
              small
              :items="preview_links_list"
              :fields="preview_headers"
              :items-per-page="20"
              items-per-page-select
            >
              <template #advertiser="{ item }">
                <td>
                  {{ item.advertiser !== null ? item.advertiser.name : '-' }}
                </td>
              </template>
              <template #link="{ item }">
                <td>
                  <div class="d-flex flex-row align-items-start">
                    <div class="w-100">{{ item.link }}</div>
                    <div>
                      <CButton
                        @click="copyURL(item.link)"
                        style="padding-top: 0; padding-bottom: 0"
                        v-c-tooltip="'Copy'"
                        ><CIcon name="cil-clone"></CIcon
                      ></CButton>
                    </div>
                  </div>
                </td>
              </template>
            </CDataTable>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
export default {
  name: 'PreviewLink',
  data() {
    return {
      campaign_ids: '',
      preview_link: '',
      load: false,
      preview_headers: [
        { key: 'created_at', label: 'Timestamp' },
        { key: 'advertiser', label: 'Advertiser' },
        { key: 'campaign_ids', label: 'Campaign IDs' },
        { key: 'link', label: 'Unique Preview Link' },
        { key: 'valid_until', label: 'Valid Until' },
      ],
    };
  },
  computed: {
    ...mapGetters({
      preview_links_list: 'getPreviewLinkList',
    }),
  },
  created() {
    if (this.preview_links_list.length === 0) {
      this.listPreviewLinks();
    }
  },
  methods: {
    ...mapActions({
      listPreviewLinks: 'listPreviewLink',
      generateLink: 'generateLink',
    }),
    async copyURL(link) {
      try {
        await navigator.clipboard.writeText(link);
        alert('Copied');
      } catch ($e) {
        alert('Failed to copy');
      }
    },
    createPreview() {
      this.preview_link = '';
      this.load = true;
      let pass = { campaign_ids: this.campaign_ids };
      this.generateLink(pass)
        .then((res) => {
          this.preview_link = res;
          this.load = false;
        })
        .catch((err) => {
          let errorMsg = 'Something went wrong in creating preview link';
          if (err.response.data.message) {
            errorMsg = err.response.data.message;
          }
          this.load = false;
          this.$swal.fire({
            text: errorMsg,
            icon: 'error',
            showCancelButton: false,
          });
          console.log(err);
        });
    },
  },
};
</script>
