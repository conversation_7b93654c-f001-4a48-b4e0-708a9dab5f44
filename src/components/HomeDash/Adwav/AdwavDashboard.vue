<template>
  <div>
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader>
            <strong>Adwav Activity</strong>
          </CCardHeader>
        </CCard>
      </CCol>
    </CRow>
    <CRow>
      <CCol sm="12" lg="4">
        <CRow>
          <CCol sm="12">
            <CWidgetBrand
              color="success"
              v-bind:right-header="active_splash_count"
              right-footer="Active Splash"
              v-bind:left-header="snoozed_splash_count"
              left-footer="Snoozed Splash"
            >
              <CIcon name="cil-video" height="56" class="my-4" />
            </CWidgetBrand>
          </CCol>
        </CRow>
        <CRow>
          <CCol sm="12">
            <CCard>
              <CCardHeader> Running Splash Ads </CCardHeader>
              <CCardBody>
                <CListGroup class="scrollable" v-if="splash.length > 0">
                  <CListGroupItem v-for="ads in splash" :key="ads.id">
                    {{ ads.advertiser_name }} &nbsp;:&nbsp; {{ ads.name }}
                    <CButtonToolbar class="float-right">
                      <CButton
                        v-c-tooltip="'View Campaign'"
                        @click="goToCampaign(ads.id, ads.advertiser_id)"
                        color="info"
                        variant="outline"
                      >
                        <CIcon name="cil-envelope-open"></CIcon>
                      </CButton>
                    </CButtonToolbar>
                  </CListGroupItem>
                </CListGroup>
                <h5 v-else>There aren't any running splash ads</h5>
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
      </CCol>
      <CCol sm="12" lg="4">
        <CRow>
          <CCol sm="12">
            <CWidgetBrand
              color="success"
              v-bind:right-header="active_feed_count"
              right-footer="Active Feed"
              v-bind:left-header="snoozed_feed_count"
              left-footer="Snoozed Feed"
            >
              <CIcon name="cil-view-stream" height="56" class="my-4" />
            </CWidgetBrand>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CRow>
              <CCol sm="12">
                <CCard>
                  <CCardHeader> Running Feed Ads </CCardHeader>
                  <CCardBody>
                    <CListGroup class="scrollable" v-if="feed.length > 0">
                      <CListGroupItem v-for="ads in feed" :key="ads.id">
                        {{ ads.advertiser_name }} &nbsp;:&nbsp; {{ ads.name }}
                        <CButtonToolbar class="float-right">
                          <CButton
                            v-c-tooltip="'View Campaign'"
                            @click="goToCampaign(ads.id, ads.advertiser_id)"
                            color="info"
                            variant="outline"
                          >
                            <CIcon name="cil-envelope-open"></CIcon>
                          </CButton>
                        </CButtonToolbar>
                      </CListGroupItem>
                    </CListGroup>
                    <h4 v-else>There aren't any running feed ads</h4>
                  </CCardBody>
                </CCard>
              </CCol>
            </CRow>
          </CCol>
        </CRow>
      </CCol>
      <CCol sm="12" lg="4">
        <CRow>
          <CCol sm="12">
            <CWidgetBrand
              color="success"
              v-bind:right-header="active_spotlight_count"
              right-footer="Active Spotlight"
              v-bind:left-header="snoozed_spotlight_count"
              left-footer="Snoozed Spotlight"
            >
              <CIcon name="cil-center-focus" height="56" class="my-4" />
            </CWidgetBrand>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CRow>
              <CCol sm="12">
                <CCard>
                  <CCardHeader> Running Spotlight Ads </CCardHeader>
                  <CCardBody>
                    <CListGroup class="scrollable" v-if="spotlight.length > 0">
                      <CListGroupItem v-for="ads in spotlight" :key="ads.id">
                        {{ ads.advertiser_name }} &nbsp;:&nbsp; {{ ads.name }}
                        <CButtonToolbar class="float-right">
                          <CButton
                            v-c-tooltip="'View Campaign'"
                            @click="goToCampaign(ads.id, ads.advertiser_id)"
                            color="info"
                            variant="outline"
                          >
                            <CIcon name="cil-envelope-open"></CIcon>
                          </CButton>
                        </CButtonToolbar>
                      </CListGroupItem>
                    </CListGroup>
                    <h4 v-else>There aren't any running spotlight ads</h4>
                  </CCardBody>
                </CCard>
              </CCol>
            </CRow>
          </CCol>
        </CRow>
      </CCol>
    </CRow>
  </div>
</template>
<script>
import Bugsnag from '@bugsnag/js';
import axios from 'axios';
import * as headers from '@/helpers/headers';

export default {
  name: 'AdwavDashboard',
  data() {
    return {
      campaigns: [],
    };
  },
  computed: {
    splash() {
      return this.campaigns.filter(
        (camp) => camp.ad_type === 'splash' && camp.ads_type !== 'sponsored'
      );
    },
    active_splash_count() {
      return String(
        this.campaigns.filter(
          (camp) =>
            camp.ad_type === 'splash' &&
            camp.ads_type !== 'sponsored' &&
            camp.status === 'active'
        ).length
      );
    },
    snoozed_splash_count() {
      return String(
        this.campaigns.filter(
          (camp) => camp.ad_type === 'splash' && camp.status === 'snoozed'
        ).length
      );
    },
    feed() {
      return this.campaigns.filter((camp) => camp.ad_type === 'bigcard');
    },
    active_feed_count() {
      return String(
        this.campaigns.filter(
          (camp) => camp.ad_type === 'bigcard' && camp.status === 'active'
        ).length
      );
    },
    snoozed_feed_count() {
      return String(
        this.campaigns.filter(
          (camp) => camp.ad_type === 'bigcard' && camp.status === 'snoozed'
        ).length
      );
    },
    campaign_count() {
      return String(this.campaign.length);
    },
    spotlight() {
      return this.campaigns.filter(
        (camp) => camp.ad_type === 'spotlight' || camp.ads_type === 'sponsored'
      );
    },
    active_spotlight_count() {
      return String(
        this.campaigns.filter(
          (camp) =>
            (camp.ad_type === 'spotlight' || camp.ads_type === 'sponsored') &&
            camp.status === 'active'
        ).length
      );
    },
    snoozed_spotlight_count() {
      return String(
        this.campaigns.filter(
          (camp) => camp.ad_type === 'spotlight' && camp.status === 'snoozed'
        ).length
      );
    },
  },
  created() {
    this.getAdwavHomeDashboard();
  },
  methods: {
    getAdwavHomeDashboard() {
      axios
        .get(
          process.env.VUE_APP_ADWAV_HOME_DASHBOARD,
          headers.createHeaders(this.$store.getters.getUserToken)
        )
        .then((res) => {
          this.campaigns = res.data;
        })
        .catch((err) => {
          alert('Something went wrong');
          console.log(err);
          Bugsnag.notify(err);
        });
    },
    goToCampaign(id, adv_id) {
      this.$router.push('/advertisers/' + adv_id + '/show-campaign/' + id);
    },
  },
};
</script>

<style scoped>
.scrollable {
  max-height: 300px;
  overflow-y: auto;
}
</style>
