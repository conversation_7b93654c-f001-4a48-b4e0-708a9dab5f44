<template>
  <div>
    <br />
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader> List of Videos </CCardHeader>
          <CCardBody>
            <CRow>
              <CCol>
                <div>
                  <router-link :to="'/ytvideo/add-video'">
                    <CButton
                      class="float-right"
                      variant="outline"
                      color="success"
                      shape="pill"
                    >
                      Add New Video
                    </CButton>
                  </router-link>
                </div>
              </CCol>
            </CRow>
            <br />
            <CRow>
              <CCol>
                <CDataTable
                  :loading="loading"
                  :items="formattedVideos"
                  sorter
                  small
                  pagination
                  :fields="fields"
                  :key="videos.id"
                >
                  <template #show_details="{ item }">
                    <td>
                      <CButtonToolbar>
                        <router-link :to="`ytvideo/add-video/${item.id}`">
                          <CButton
                            color="warning"
                            v-c-tooltip="'Edit Video'"
                            variant="outline"
                            ><CIcon name="cil-pencil"></CIcon
                          ></CButton>
                        </router-link>
                      </CButtonToolbar>
                    </td>
                  </template>
                </CDataTable>
                <CPagination
                  @update:activePage="goToPage"
                  :pages="pagination.last_page"
                  :activePage.sync="pagination.current_page"
                  align="center"
                />
              </CCol>
            </CRow>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';

export default {
  data() {
    return {
      fields: [
        {
          key: 'created_at',
          label: 'Created At',
          formatter: (value) => this.formatDate(value),
        },
        { key: 'id', label: 'Video ID', sorter: 'desc' },
        { key: 'title', label: 'Title' },
        { key: 'description', label: 'Description' },
        { key: 'author', label: 'Author' },
        { key: 'language', label: 'Language' },
        { key: 'topic', label: 'Topic' },
        { key: 'is_live', label: 'Live' },
        { key: 'show_details', sorter: false, label: 'Actions' },
      ],
      default_sort: {
        column: 'created_at',
        asc: false,
      },
    };
  },

  // get list of
  async created() {
    this.listVideos();
  },

  computed: {
    ...mapGetters({
      loading: 'getVideoLoading',
      videos: 'getVideos',
      pagination: 'getPagination',
    }),
    // format date time and deescription breaklines to be shown as white space
    formattedVideos() {
      return this.videos.map((video) => {
        // Parse the topic JSON string into an object
        let topicObj = video.topic ? JSON.parse(video.topic) : null;

        return {
          ...video,
          created_at: this.formatTimestamp(video.created_at),
          description: video.description.replace(/<br\s*\/?>/gi, ' '),
          // Use the parsed topic object, defaulting to '-' if topic is not available
          topic: topicObj ? topicObj.label : '-',
        };
      });
    },
    loading() {
      return this.$store.getters.getVideoLoading;
    },
  },

  name: 'ShowVideo',

  methods: {
    ...mapActions({
      removeVideo: 'removeVideo',
      listVideos: 'listVideos',
    }),

    goBack() {
      this.$router.push('/ytvideo');
    },

    redirectTo(id) {
      this.$router.push('/ytvideo/show-video/' + id);
    },

    getColor(status) {
      return status === 'archived' ? 'success' : 'danger';
    },

    getIcon(status) {
      return status == 'archived' ? 'cil-action-redo' : 'cil-book';
    },

    getBadge(status) {
      return status === 'Yes' ? 'success' : 'danger';
    },

    goToPage(currentPage) {
      this.listVideos(currentPage);
    },

    formatTimestamp(timestamp) {
      const date = new Date(timestamp * 1000);
      return date.toLocaleString();
    },
  },
};
</script>
