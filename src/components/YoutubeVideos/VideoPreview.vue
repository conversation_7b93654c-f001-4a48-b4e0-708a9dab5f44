<template>
  <div class="d-flex justify-content-center">
    <div class="card base-card">
      <div class="video-thumbnail-wrapper">
        <img
          v-if="display_cover !== ''"
          :src="display_cover"
          class="card-img-top"
          :alt="title"
        />
        <div v-else class="card-img-top empty-img"></div>
      </div>
      <div class="video-info-wrapper">
        <span class="video-title">{{ title }}</span>
        <p class="video-description">{{ description }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VideoPreview',
  props: ['videos', 'cover'],

  computed: {
    display_cover() {
      return this.cover;
    },
    title() {
      return this.videos.title || 'Title here';
    },
    description() {
      return this.videos.description || 'Description here';
    },
  },
};
</script>

<style scoped>
.base-card {
  width: 380px;
  padding: 15px;
  border-radius: 6px !important;
}
.card-img-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-title {
  font-size: 16px;
  font-weight: 700;
  line-height: 19px;
  text-overflow: ellipsis;
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.empty-img {
  background: lightblue;
  height: 100%;
  border-radius: 6px;
}

.video-thumbnail-wrapper {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
}

.video-info-wrapper {
  color: black;
  padding: 8px;
}

.video-description {
  font-size: 14px;
  color: grey;
  margin-top: 4px;
}
</style>
