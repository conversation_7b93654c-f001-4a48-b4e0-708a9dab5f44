<template>
  <div>
    <CInput
      v-model="video.title"
      :is-valid="isValidTitleField(video.title)"
      placeholder="Max 120 characters"
      :label="'Video Title'"
      invalid-feedback="Please enter the video title"
    />

    <CTextarea
      v-model="formattedDescription"
      :label="'Video Description'"
      :is-valid="isValidDescField(video.description)"
      placeholder="Max 1000 characters"
      invalid-feedback="Please enter the video description"
    />

    <CInput
      v-model="video.publisher_id"
      placeholder="Must be an existing Publisher"
      :label="'Publisher ID'"
      :is-valid="isValidPublisherId(video.publisher_id)"
      invalid-feedback="Publisher does not exist"
    />

    <CInput
      v-model="video.youtube_link"
      :label="'Youtube Link'"
      :is-valid="isValidYoutubeLink(video.youtube_link)"
      placeholder="Must be a Youtube URL"
      invalid-feedback="Please enter a valid Youtube URL"
    />
    <!-- upload thumbnail element -->
    <label>Video Thumbnail</label>
    <div>
      <label class="custom-file-upload">
        <input
          type="file"
          @change="onFileChange($event)"
          ref="inputThumbnail"
          class="form-control-file"
          data-content="thumbnail"
          accept=".png, .jpg, .jpeg"
        />
        <i class="fa fa-cloud-upload"></i> Upload
      </label>
      <i
        class="fa fa-times"
        style="margin-left: 10px; cursor: pointer"
        @click="removeThumbnail()"
      ></i>
    </div>
    <span
      v-if="uploadedFileName != ''"
      style="color: gray; font-size: 12px; margin-bottom: 10px"
      >Uploaded: {{ uploadedFileName }}</span
    >

    <CRow>
      <CCol lg="12" md="12" sm="12">
        <label for="feedOpt">Topic</label>
        <multiselect
          :class="[{ 'invalid-border': video.topic.value === -1 }]"
          id="feedOpt"
          v-model="video.topic"
          track-by="value"
          label="label"
          :options="topics"
          placeholder="Search"
          @input="onChangeTopic"
        >
          <template v-if="video.topic.value > 0" slot="singleLabel">
            {{ video.topic.label }}
          </template>
          <span slot="noResult"
            >Oops! No Topic with the name. Consider changing the search
            query.</span
          >
        </multiselect>
        <span
          v-if="video.topic === -1"
          v-text="'Please select at least 1 Topic'"
          class="invalid-error"
        />
      </CCol>
    </CRow>

    <CRow>
      <CCol lg="12" md="12" sm="12">
        <label>Language</label>
        <multiselect
          :class="[{ 'invalid-border': video.language === null }]"
          v-model="video.language"
          track-by="value"
          label="label"
          :options="languages"
          @input="onChangeLanguage"
        >
          <template
            v-if="video.language != null"
            slot="singleLabel"
            slot-scope=""
          >
            {{ video.language }}
          </template>
          <span slot="noResult"
            >Oops! No Topic with the name. Consider changing the search
            query.</span
          >
        </multiselect>
        <span
          v-if="video.language === null"
          v-text="'Please select at least 1 Topic'"
          class="invalid-error"
        />
      </CCol>
    </CRow>

    <br />
    <div class="form-check form-check-inline mb-3">
      <input type="checkbox" class="form-check-input" v-model="isLiveBoolean" />
      <label class="form-check-label">Video is Live</label>
    </div>
  </div>
</template>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<script>
import { mapActions, mapGetters } from 'vuex';
import Multiselect from 'vue-multiselect';

export default {
  name: 'BaseAddVideoForm',
  components: { Multiselect },
  props: {
    video: {
      type: Object,
      required: true,
    },
    isLoading: {
      type: Boolean,
      required: true,
      default: false,
    },
    topics: {
      type: Array,
      required: true,
    },
  },

  data() {
    return {
      // check if form should be updating or creating new video
      edit: this.$route.params.id !== undefined,
      selectedTopic: null,
      languages: [
        { value: 'en', label: 'EN' },
        { value: 'ms', label: 'MS' },
        { value: 'zh', label: 'ZH' },
      ],
      uploadedFileName: '',
    };
  },

  // watch new values for fields
  watch: {
    'video.youtube_link': function (newVal, oldVal) {
      if (newVal !== oldVal) {
        // Update the YouTube link value
        this.video.youtube_link = newVal;

        // If no thumbnail is uploaded, generate thumbnail link from YouTube link
        if (!this.$refs.inputThumbnail.value) {
          this.generateThumbnailLink(newVal);
        }
      }
    },

    'video.is_live': function (newVal, oldVal) {
      if (newVal !== oldVal) {
        this.$emit('update:is_live', newVal);
      }
    },
  },

  computed: {
    ...mapGetters({
      publisherIds: 'getPublisherIds',
    }),

    isLiveBoolean: {
      get() {
        // check if is_live is checked
        return !!Number(this.video.is_live);
      },
      set(value) {
        // converting true to 1 and false to 0
        this.video.is_live = value ? 1 : 0;
      },
    },

    selectedTopicLabel() {
      const topic = this.topics.find(
        (topic) => topic.value === this.video.topic.value
      );
      return topic ? topic.label : '';
    },

    formattedDescription: {
      get() {
        // Convert <br> to newline characters for display in the textarea
        return this.video.description.replace(/<br\s*\/?>/gi, '\n');
      },
      set(value) {
        // Convert newline characters back to <br> for storage or further processing
        this.video.description = value.replace(/\n/g, '<br>');
      },
    },
  },

  async created() {
    console.log(this.video);

    // get publishers ids for checking publisher id input
    this.getPublisherIds();
  },

  methods: {
    ...mapActions({
      getPublisherIds: 'listPublisherIds',
      getTopics: 'listVideoTopics',
      getThumbnailLink: 'getVideoThumbnailLink',
      listVideosById: 'listVideosById',
    }),

    // methods to check fields validity
    isValidTitleField(value) {
      return value !== '' && value.length <= 120;
    },
    isValidDescField(value) {
      return value !== '' && value.length <= 1000;
    },
    isValidYoutubeLink(link) {
      return link.startsWith('https://www.youtube.com/watch?v=');
    },
    isValidPublisherId(publisherId) {
      // Check if the inputted publisher ID exists in the preloaded list
      return this.publisherIds.includes(publisherId);
    },

    // method to generate thumbnail link
    generateThumbnailLink(youtubeLink) {
      const regex =
        /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
      const match = youtubeLink.match(regex);
      if (match && match[1]) {
        const videoId = match[1];

        // Set the thumbnail link using the extracted video ID.
        this.video.source_thumb_url = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
        console.log(this.video.source_thumb_url);
      } else {
        // YouTube link doesn't match the expected format.
        console.error(
          'Failed to extract video ID from YouTube link:',
          youtubeLink
        );
      }
    },

    onChangeTopic(selectedTopic) {
      if (selectedTopic) {
        this.video.topic.value = selectedTopic.value;
        this.video.topic.label = selectedTopic.label;
      } else {
        // Handle the case where no valid topic is selected
        this.video.topic = { value: -1, label: '' };
      }
    },

    onChangeLanguage(selectedLanguage) {
      if (selectedLanguage) {
        this.selectedLanguage = selectedLanguage;
        this.video.language = selectedLanguage.value;
      } else {
        // Handle the case where no valid topic is selected
        this.video.language = null;
      }
    },

    onFileChange(event) {
      this.$emit('loading', true);

      // get input
      let input = event.target;

      if (input.files && input.files[0]) {
        let iSize = input.files[0].size / 1024 / 1024;

        if (iSize <= 0.5) {
          // If file size does not exceed 5 mb
          // Get the file name from the file input
          const fileName = event.target.files[0].name;
          // Update the uploadedFileName data property
          this.uploadedFileName = fileName;
          this.getThumbnailLink(input).then((res) => {
            this.video.source_thumb_url = res;
          });
        } else {
          // Display warning and reset thumbnail value
          alert('File size exceeded. Max 500kb');

          // use youtube link to generate thumbnail if link is provided
          if (this.video.youtube_link) {
            this.generateThumbnailLink(this.video.youtube_link);
          } else {
            this.video.source_thumb_url = '';
          }
        }
        this.$emit('loading', false);
      }
    },

    // method to remove thumbnail
    removeThumbnail() {
      // Reset the input element value to allow uploading a new thumbnail
      this.$refs.inputThumbnail.value = '';
      this.uploadedFileName = '';

      // Generate a new thumbnail if a YouTube link is present
      if (this.video.youtube_link) {
        console.log(this.video.youtube_link);
        this.generateThumbnailLink(this.video.youtube_link);
      } else {
        // Reset the thumbnail to an empty string if no YouTube link is present
        this.video.source_thumb_url = '';
      }

      alert('Thumbnail Removed');
    },
  },
};
</script>

<style scoped>
.invalid-border {
  border: 1px solid #e55353;
  border-radius: 6px;
}
.invalid-error {
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #e55353;
}

input[type='file'] {
  display: none;
}

.custom-file-upload {
  border: 1px solid #ccc;
  display: inline-block;
  padding: 6px 12px;
  cursor: pointer;
}
</style>
