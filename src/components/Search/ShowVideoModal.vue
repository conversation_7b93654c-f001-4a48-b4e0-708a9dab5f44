<template>
  <div>
    <CButton
      class="float-right"
      variant="outline"
      color="primary"
      @click="show = true"
      v-c-tooltip="'Show Video'"
      ><CIcon name="cil-envelope-open"></CIcon
    ></CButton>
    <CModal
      title="Video Details"
      color="default"
      size="lg"
      :show.sync="show"
      centered
    >
      <CImg :src="video.channelImageURL" height="50" />
      <span class="font-weight-bold"> {{ video.publisher }} </span>
      <hr />

      <CRow>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Author</small><br />
            <strong class="h4">{{ video.author || video.publisher }}</strong>
          </CCallout>
        </CCol>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Published Date</small><br />
            <strong class="h4">{{ convertTime(video.publishedDate) }}</strong>
          </CCallout>
        </CCol>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Published Date</small><br />
            <strong class="h4">{{ convertTime(video.createdDate) }}</strong>
          </CCallout>
        </CCol>
      </CRow>
      <h4 class="mt-0">{{ video.title }}</h4>
      <!-- <CEmbed
            tag="p"
            aspect="16by9"
            :src="video.media"
            frameborder="0"
            allow="accelerometer;autoplay; encrypted-media; gyroscope; picture-in-picture"
        /> -->

      <div v-if="show" class="embed-responsive embed-responsive-16by9">
        <video autoplay="false" controls="true" class="embed-responsive-item">
          <source :src="video.media" type="video/mp4" />
        </video>
      </div>
      <hr />
      <p>
        {{ video.description }}
      </p>
      <hr />
      <CRow>
        <CCol>
          <CWidgetIcon
            text="Total Views"
            v-bind:header="video.viewCount"
            color="primary"
            :icon-padding="false"
          >
            <CIcon name="cil-people" width="24"></CIcon>
          </CWidgetIcon>
        </CCol>
        <CCol>
          <CWidgetIcon
            text="Total Reactions"
            v-bind:header="video.reactionCount"
            color="warning"
            :icon-padding="false"
          >
            <CIcon name="cil-happy" width="24"></CIcon>
          </CWidgetIcon>
        </CCol>
        <CCol>
          <CWidgetIcon
            text="Total Comments"
            v-bind:header="video.commentCount"
            color="info"
            :icon-padding="false"
          >
            <CIcon name="cil-comment-square" width="24"></CIcon>
          </CWidgetIcon>
        </CCol>
      </CRow>
      <template slot="footer">
        <CButton
          :href="linkArticle(video.permalink)"
          target="_blank"
          color="danger"
          variant="outline"
          >Newswav Link</CButton
        >
        <CButton
          :href="video.url"
          target="_blank"
          color="primary"
          variant="outline"
          type="submit"
          >Source</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
import { format, fromUnixTime } from 'date-fns';
export default {
  name: 'ShowArticleModal',
  components: '',
  props: {
    video: {
      type: Object,
      required: true,
    },
    section: {
      type: Boolean,
    },
  },
  data() {
    return {
      show: false,
    };
  },
  methods: {
    linkArticle(permalink) {
      let link = 'https://newswav.com/video/' + permalink; //prod
      // let link = "https://dev-website.newswav.dev/video/"+permalink //dev
      return link;
    },
    convertTime(time) {
      var date = fromUnixTime(time);
      return format(date, 'yyyy/MM/dd');
    },
  },
};
</script>

<style scoped></style>
