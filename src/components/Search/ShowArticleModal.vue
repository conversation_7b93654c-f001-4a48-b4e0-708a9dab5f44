<template>
  <div>
    <CButton
      class="float-right"
      variant="outline"
      color="primary"
      @click="show = true"
      v-c-tooltip="'Show Article'"
      ><CIcon name="cil-envelope-open"></CIcon
    ></CButton>
    <CModal
      title="Article Details"
      color="default"
      size="lg"
      :show.sync="show"
      centered
    >
      <CImg :src="article.channelImageURL" height="50" />
      <span class="font-weight-bold"> {{ article.publisher }} </span>
      <hr />

      <CRow>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Author</small><br />
            <strong class="h4">{{
              article.author || article.publisher
            }}</strong>
          </CCallout>
        </CCol>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Published Date</small><br />
            <strong class="h4">{{ convertTime(article.publishedDate) }}</strong>
          </CCallout>
        </CCol>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Created Date</small><br />
            <strong class="h4">{{ convertTime(article.createdDate) }}</strong>
          </CCallout>
        </CCol>
      </CRow>
      <h4 class="mt-0">{{ article.title }}</h4>
      <img
        :src="returnMedia(article.media)"
        height="200"
        class="mb-2 rounded"
      />
      <p>
        {{ article.description }}
      </p>
      <hr />
      <CRow>
        <CCol>
          <CWidgetIcon
            text="Total Views"
            v-bind:header="article.viewCount"
            color="primary"
            :icon-padding="false"
          >
            <CIcon name="cil-people" width="24"></CIcon>
          </CWidgetIcon>
        </CCol>
        <CCol>
          <CWidgetIcon
            text="Total Reactions"
            v-bind:header="article.reactionCount"
            color="warning"
            :icon-padding="false"
          >
            <CIcon name="cil-happy" width="24"></CIcon>
          </CWidgetIcon>
        </CCol>
        <CCol>
          <CWidgetIcon
            text="Total Comments"
            v-bind:header="article.commentCount"
            color="info"
            :icon-padding="false"
          >
            <CIcon name="cil-comment-square" width="24"></CIcon>
          </CWidgetIcon>
        </CCol>
      </CRow>
      <template slot="footer">
        <CButton
          :href="linkArticle(article.permalink)"
          target="_blank"
          color="danger"
          variant="outline"
          >Newswav Link</CButton
        >
        <CButton
          :href="article.url"
          target="_blank"
          color="primary"
          variant="outline"
          type="submit"
          >Source</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
import { format, parseISO } from 'date-fns';
export default {
  name: 'ShowArticleModal',
  components: '',
  props: {
    article: {
      type: Object,
      required: true,
    },
    section: {
      type: Boolean,
    },
  },
  data() {
    return {
      show: false,
    };
  },
  methods: {
    linkArticle(permalink) {
      let link = 'https://newswav.com/article/' + permalink;
      return link;
    },
    convertTime(time) {
      let date = parseISO(time);

      return format(date, 'yyyy/MM/dd');
    },
    returnMedia(media) {
      let m = media[0] ? media[0] : '';
      let url = m.url ? m.url : '';
      return url;
    },
  },
};
</script>

<style scoped></style>
