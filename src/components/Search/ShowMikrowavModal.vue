<template>
  <div>
    <CButton
      class="float-right"
      variant="outline"
      color="primary"
      @click="show = true"
      v-c-tooltip="'Show Mikrowav'"
    >
      <CIcon name="cil-envelope-open" />
    </CButton>
    <CModal
      title="Mikrowav Details"
      color="default"
      size="lg"
      :show.sync="show"
      centered
    >
      <CImg :src="mikrowav.channelImageURL" height="50" />
      <span class="font-weight-bold"> {{ mikrowav.publisher }} </span>

      <hr />

      <CRow>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Author</small><br />
            <strong class="h4">{{
              mikrowav.author || mikrowav.publisher
            }}</strong>
          </CCallout>
        </CCol>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Created Date</small><br />
            <strong class="h4">{{ convertTime(mikrowav.createdDate) }}</strong>
          </CCallout>
        </CCol>
      </CRow>
      <p>
        {{ mikrowav.description }}
      </p>
      <!--              <img :src="returnMedia(mikrowav.media)" height="200" class="mb-2 mr-1 rounded"/>-->
      <div v-for="(mediaItem, index) in mikrowav.media" :key="index">
        <img :src="mediaItem.url" height="200" class="mb-2 mr-1 rounded" />
      </div>
      <hr />
      <CRow>
        <CCol>
          <CWidgetIcon
            text="Total Views"
            v-bind:header="`${mikrowav.viewCount}`"
            color="primary"
            :icon-padding="false"
          >
            <CIcon name="cil-people" width="24" />
          </CWidgetIcon>
        </CCol>
        <CCol>
          <CWidgetIcon
            text="Total Reactions"
            v-bind:header="`${mikrowav.reactionCount}`"
            color="warning"
            :icon-padding="false"
          >
            <CIcon name="cil-happy" width="24" />
          </CWidgetIcon>
        </CCol>
        <CCol>
          <CWidgetIcon
            text="Total Comments"
            v-bind:header="`${mikrowav.commentCount}`"
            color="info"
            :icon-padding="false"
          >
            <CIcon name="cil-comment-square" width="24" />
          </CWidgetIcon>
        </CCol>
      </CRow>

      <template slot="footer">
        <CButton
          :href="linkMikrowav(mikrowav.uniqueID)"
          target="_blank"
          color="danger"
          variant="outline"
        >
          Newswav Link
        </CButton>
      </template>
    </CModal>
  </div>
</template>

<script>
import { format, parseISO } from 'date-fns';
export default {
  name: 'ShowMikrowavModal',
  props: {
    mikrowav: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      show: false,
    };
  },
  methods: {
    linkMikrowav(permalink) {
      return `https://newswav.com/mikrowav/${permalink}`;
    },
    convertTime(time) {
      let date = parseISO(time);

      return format(date, 'yyyy/MM/dd');
    },
    returnMedia(media) {
      console.log('aaa');
      let m = media[0] ? media[0] : '';
      console.log(m);
      let url = m.url ? m.url : '';

      return url;
    },
  },
};
</script>
