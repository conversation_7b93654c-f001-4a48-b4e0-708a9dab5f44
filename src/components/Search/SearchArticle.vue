<template>
  <div>
    <CRow>
      <CCol></CCol>
    </CRow>
    <br />
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader> Search</CCardHeader>
          <CCardBody>
            <CRow>
              <CCol>
                <CRow>
                  <CCol sm="2">
                    <CSelect
                      :options="type"
                      :value.sync="selected"
                      class="mx-auto"
                    />
                  </CCol>
                  <CCol sm="8">
                    <input
                      class="form-control mx-auto"
                      type="text"
                      placeholder="Search something"
                      v-model="key"
                      v-on:keyup.enter="search(key)"
                    />
                  </CCol>
                  <CCol sm="2">
                    <CButton
                      class="float mx-auto"
                      variant="outline"
                      color="success"
                      @click="search(key)"
                    >
                      Search
                      <CIcon name="cil-magnifying-glass"></CIcon>
                    </CButton>
                  </CCol>
                </CRow>
              </CCol>
            </CRow>
            <br />
            <CRow>
              <CCol>
                <span v-show="keyword !== ''">
                  Search result for : {{ keyword }}
                  <CBadge color="primary">{{ t }}</CBadge>
                </span>
                <CDataTable
                  v-bind:loading="loading"
                  :items="results"
                  :fields="fields"
                  table-filter
                  hover
                  sorter
                >
                  <template #publishedDate="{ item }">
                    <td style="max-width: 500px">
                      {{ convertTime(item) }}
                    </td>
                  </template>
                  <template #show_details="{ item }">
                    <td>
                      <CButtonToolbar>
                        <ShowArticleModal
                          v-if="item.type === 'article'"
                          :article="item"
                          :section="true"
                        />
                        <ShowVideoModal
                          v-else-if="item.type === 'video'"
                          :video="item"
                          :section="true"
                        />
                        <ShowPodcastModal
                          v-else-if="item.type === 'podcast'"
                          :podcast="item"
                        />
                        <ShowMikrowavModal
                          v-else-if="item.type === 'mikrowav'"
                          :mikrowav="item"
                        />
                        <div
                          v-if="
                            checkAction('search_hide') && item.deleted === '0'
                          "
                        >
                          <CButton
                            :key="`${item.uniqueID}_${item.deleted}`"
                            color="danger"
                            variant="outline"
                            @click="hide(item)"
                            v-c-tooltip="'Hide'"
                          >
                            <CIcon name="cil-x-circle"></CIcon>
                          </CButton>
                        </div>
                        <div
                          v-if="
                            checkAction('search_show') && item.deleted === '1'
                          "
                        >
                          <CButton
                            :key="`${item.uniqueID}_${item.deleted}`"
                            color="dark"
                            variant="outline"
                            @click="unhide(item)"
                            v-c-tooltip="'Unhide'"
                          >
                            <CIcon name="cil-check-circle"></CIcon>
                          </CButton>
                        </div>
                        <CButton
                          v-if="item.comments_disabled === '0'"
                          color="danger"
                          variant="outline"
                          @click="disable(item)"
                          v-c-tooltip="'Disable comment'"
                          :key="item.comments_disabled"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="21"
                            height="21"
                            viewBox="0 0 21 21"
                          >
                            <g
                              id="Disable_comment"
                              data-name="Disable comment"
                              transform="translate(12598.5 -10068.5)"
                            >
                              <rect
                                id="Rectangle_737"
                                data-name="Rectangle 737"
                                width="20"
                                height="20"
                                transform="translate(-12598 10069)"
                                fill="none"
                              />
                              <g
                                id="COMMENT_Icon"
                                data-name="COMMENT Icon"
                                transform="translate(-12593.177 10075.421)"
                              >
                                <path
                                  id="Comment"
                                  d="M2.993,7.5H2a2,2,0,0,1-2-2V2A2,2,0,0,1,2,0H9a2,2,0,0,1,2,2V5.5a2,2,0,0,1-2,2H5.928L2.993,10Z"
                                  transform="translate(0.177 -0.421)"
                                  fill="none"
                                  stroke="#ec3535"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="1"
                                />
                              </g>
                              <g
                                id="Group_1219"
                                data-name="Group 1219"
                                transform="translate(-15823.999 9964.386)"
                              >
                                <circle
                                  id="Ellipse_20"
                                  data-name="Ellipse 20"
                                  cx="10"
                                  cy="10"
                                  r="10"
                                  transform="translate(3225.999 104.614)"
                                  fill="none"
                                  stroke="#ec3535"
                                  stroke-width="1"
                                />
                                <path
                                  id="Path_1896"
                                  data-name="Path 1896"
                                  d="M1479.768,110.5,1464,121.942"
                                  transform="translate(1764.136 -1.091)"
                                  fill="none"
                                  stroke="#ec3535"
                                  stroke-linecap="round"
                                  stroke-width="1"
                                />
                              </g>
                            </g>
                          </svg>
                        </CButton>
                        <CButton
                          v-else
                          color="dark"
                          variant="outline"
                          @click="enable(item)"
                          v-c-tooltip="'Enable comment'"
                          :key="item.comments_disabled"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                          >
                            <g
                              id="COMMENT_Icon"
                              data-name="COMMENT Icon"
                              transform="translate(-3929 -9367)"
                            >
                              <rect
                                id="Rectangle_736"
                                data-name="Rectangle 736"
                                width="20"
                                height="20"
                                transform="translate(3929 9367)"
                                fill="none"
                              />
                              <g
                                id="COMMENT_Icon-2"
                                data-name="COMMENT Icon"
                                transform="translate(3930.613 9370.443)"
                              >
                                <path
                                  id="Comment"
                                  d="M4.354,10.5H2a2,2,0,0,1-2-2V2A2,2,0,0,1,2,0H14a2,2,0,0,1,2,2V8.5a2,2,0,0,1-2,2H8.622L4.354,14Z"
                                  transform="translate(0.387 -0.444)"
                                  fill="none"
                                  stroke="#939292"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="1"
                                />
                              </g>
                            </g>
                          </svg>
                        </CButton>
                        <TranslationPreview
                          v-if="item.type === 'article'"
                          :item="item"
                        />
                      </CButtonToolbar>
                    </td>
                  </template>
                </CDataTable>
                <CPagination
                  :activePage.sync="currentPage"
                  :pages="pages"
                  @update:activePage="searchPage(keyword, currentPage)"
                  v-show="results.length > 0 || currentPage > 1"
                  align="center"
                />
                <!-- @update:activePage="searchPage(keyword, currentPage)" -->
              </CCol>
            </CRow>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import ShowArticleModal from './ShowArticleModal';
import ShowVideoModal from './ShowVideoModal';
import ShowPodcastModal from './ShowPodcastModal';
import ShowMikrowavModal from './ShowMikrowavModal.vue';
import { format, parseISO, fromUnixTime } from 'date-fns';
import TranslationPreview from '../AllContent/TranslationPreview.vue';

export default {
  name: 'Search',
  components: {
    ShowArticleModal,
    ShowVideoModal,
    ShowPodcastModal,
    ShowMikrowavModal,
    TranslationPreview,
  },
  data() {
    return {
      key: '',
      keyword: '',
      currentPage: 1,
      pages: 2,
      t: '',
      selected: 'articles',
      type: [
        { value: 'unique_id', label: 'Unique ID' },
        { value: 'articles', label: 'Articles' },
        { value: 'videos', label: 'Videos' },
        { value: 'podcasts', label: 'Podcasts' },
      ],
      fields: [
        { key: 'id', label: 'ID' },
        { key: 'uniqueID', label: 'Unique ID' },
        { key: 'title', label: 'Title' },
        { key: 'publisher', label: 'Publisher' },
        { key: 'publishedDate', label: 'Published Date' },
        { key: 'show_details', sorter: false, label: 'Action' },
      ],
    };
  },
  created() {
    this.$store.dispatch('resetResult');
  },
  computed: {
    loading() {
      return this.$store.getters.getSearchLoading;
    },
    results() {
      return this.$store.getters.getResults;
    },
  },
  watch: {
    'results.length': function () {
      if (this.results.length == 0) {
        this.pages = this.currentPage;
      } else {
        this.pages = this.currentPage + 1;
      }
    },
  },
  methods: {
    search(keyword) {
      this.currentPage = 1;
      this.pages = 1;
      if (this.key !== '') {
        this.keyword = this.key;
        this.t = this.selected;
        if (this.t === 'articles') {
          this.$store.dispatch('getArticle', {
            keyword: this.keyword,
            type: this.t,
          });
        } else if (this.t === 'videos') {
          this.$store.dispatch('getVideo', {
            keyword: keyword,
            type: this.t,
          });
        } else if (this.t === 'podcasts') {
          this.$store.dispatch('getPodcast', {
            keyword: keyword,
            type: this.t,
          });
        } else {
          this.pages = 1;
          this.$store.dispatch('getResourceByID', this.keyword);
        }
      } else {
        alert('search box empty');
      }
    },
    searchPage(keyword, currentPage) {
      if (this.results.length > 0) {
        this.pages = this.pages + 1;
      } else {
        this.pages = currentPage;
      }
      if (this.t === 'articles') {
        this.$store.dispatch('getArticlePage', {
          keyword: keyword,
          page: currentPage,
          type: this.t,
        });
      } else if (this.t === 'podcasts') {
        this.$store.dispatch('getPodcast', {
          type: this.t,
          page: currentPage,
          keyword: keyword,
        });
      } else {
        this.$store.dispatch('getVideoPage', {
          keyword: keyword,
          page: currentPage,
          type: this.t,
        });
      }
    },
    convertTime(item) {
      let date = '';
      if (item.type === 'article' || item.type === 'mikrowav') {
        let pubDate = parseISO(item.publishedDate);
        date = format(pubDate, 'yyyy/MM/dd');
      } else {
        let t = fromUnixTime(item.publishedDate);
        date = format(t, 'yyyy/MM/dd');
      }
      return date;
    },
    redirectTo(id) {
      this.$router.push('/search/show-article/' + id);
    },
    hide(item) {
      let type = item.type;
      let msg = `This will hide ${type} from users. Continue?`;
      if (confirm(msg)) {
        let send = { unique_id: item.uniqueID };
        this.$store
          .dispatch('hideResource', send)
          .then((res) => {
            alert(res.data.msg);
          })
          .catch((err) => {
            alert(err.response.data.msg);
          });
      }
    },
    unhide(item) {
      let type = item.type;
      let msg = `This will unhide ${type} from users. Continue?`;
      if (confirm(msg)) {
        let send = { unique_id: item.uniqueID, channelID: item.channelID };
        this.$store
          .dispatch('showResource', send)
          .then((res) => {
            alert(res.data.msg);
          })
          .catch((err) => {
            alert(err.response.data.msg);
          });
      }
    },
    enable(item) {
      let type = item.type;
      let msg = `This will enable comments for the ${type}. Continue?`;
      if (confirm(msg)) {
        if (type === 'mikrowav') {
          this.$store
            .dispatch('commentToggle', item.uniqueID)
            .then(() => {
              alert(`Comments are enabled for ${item.uniqueID}`);
            })
            .catch((err) => {
              alert(
                `Something went wrong in enabling comments for ${item.uniqueID}`
              );
              console.log(err);
            });
        } else {
          let send = { unique_id: item.uniqueID, status: true };
          this.$store
            .dispatch('toggleComment', send)
            .then(() => {
              alert(`Comments are enabled for ${item.uniqueID}`);
            })
            .catch((err) => {
              alert(
                `Something went wrong in enabling comments for ${item.uniqueID}`
              );
              console.log(err);
            });
        }
      }
    },
    disable(item) {
      let type = item.type;
      let msg = `This will disable comments for the ${type}. Continue?`;
      if (confirm(msg)) {
        if (type === 'mikrowav') {
          this.$store
            .dispatch('commentToggle', item.uniqueID)
            .then(() => {
              alert(`Comments are disabled for ${item.uniqueID}`);
            })
            .catch((err) => {
              alert(
                `Something went wrong in disabling comments for ${item.uniqueID}`
              );
              console.log(err);
            });
        } else {
          let send = { unique_id: item.uniqueID, status: false };
          this.$store
            .dispatch('toggleComment', send)
            .then(() => {
              alert(`Comments are disabled for ${item.uniqueID}`);
            })
            .catch((err) => {
              alert(
                `Something went wrong in disabling comments for ${item.uniqueID}`
              );
              console.log(err);
            });
        }
      }
    },
  },
};
</script>
