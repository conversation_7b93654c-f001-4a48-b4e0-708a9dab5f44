<template>
  <div>
    <CButton
      v-c-tooltip="'Show Podcast'"
      variant="outline"
      color="primary"
      @click="show = !show"
      ><CIcon name="cil-envelope-open"></CIcon
    ></CButton>
    <CModal
      title="Podcast Details"
      color="default"
      size="lg"
      :show.sync="show"
      centered
    >
      <div>
        <div class="d-flex flex-row align-items-center">
          <div class="mr-4">
            <img
              :src="podcast.channelImageURL"
              alt=""
              class="rounded-circle"
              height="40"
              width="40"
            />
          </div>
          <div>{{ podcast.publisher }}</div>
        </div>
        <hr />
        <div class="d-flex flex-row">
          <div class="mr-3">
            <img :src="podcast.media" alt="" height="150" width="150" />
          </div>
          <div>
            <div>
              <strong>{{ podcast.title }}</strong>
            </div>
            <div>{{ podcast.show_name }}</div>
          </div>
        </div>
        <div>{{ podcast.description }}</div>
        <div>
          <audio :src="podcast.pod_url" controls></audio>
        </div>
      </div>
      <template slot="footer">
        <CButton
          :href="linkPodcast(podcast.permalink)"
          target="_blank"
          color="danger"
          variant="outline"
          >Newswav Link</CButton
        >
        <CButton
          :href="podcast.url"
          target="_blank"
          color="primary"
          variant="outline"
          type="submit"
          >Source</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
export default {
  name: 'ShowPodcastModal',
  components: '',
  props: {
    podcast: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      show: false,
    };
  },
  methods: {
    linkPodcast(permalink) {
      let link = 'https://newswav.com/podcast/' + permalink; //prod
      // let link = "https://dev-website.newswav.dev/podcast/" + permalink //dev
      return link;
    },
  },
};
</script>
<style scoped></style>
