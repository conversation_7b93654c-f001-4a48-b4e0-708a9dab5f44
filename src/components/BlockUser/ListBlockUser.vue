<template>
  <div>
    <CCard>
      <CCardHeader>List of Blocked Users</CCardHeader>
      <CCardBody>
        <CDataTable
          :loading="blocked_loading"
          sorter
          table-filter
          small
          column-filter
          :items-per-page="10"
          pagination
          :items="blocked_list"
          :fields="blocked_fields"
        >
          <template #action="{ item }">
            <td>
              <CButtonToolbar>
                <CButton color="warning" @click="openModal(item)">
                  Unblock
                </CButton>
              </CButtonToolbar>
            </td>
          </template>
        </CDataTable>
      </CCardBody>
    </CCard>
    <CModal
      :show.sync="unblock_show"
      title="Unblock Confirmation"
      color="warning"
      centered
    >
      Unblock user ID <strong>{{ unblock_id }}</strong> ?
      <template slot="footer">
        <CButton @click="showUnblock = !showUnblock">Cancel</CButton>
        <CButton @click="unblockUser" color="warning">Yes</CButton>
      </template>
    </CModal>
  </div>
</template>

<script>
export default {
  name: 'ListBlockUser',
  data() {
    return {
      blocked_fields: [
        { key: 'updated_date', label: 'Date' },
        { key: 'user_id', label: 'User ID' },
        { key: 'username', label: 'User Name' },
        { key: 'email', label: 'User Email' },
        { key: 'action', label: 'Actions', sorter: false, filter: false },
      ],
      unblock_id: '',
      unblock_show: false,
    };
  },
  computed: {
    blocked_list() {
      return this.$store.getters.getBlockedUsers;
    },
    blocked_loading() {
      return this.$store.getters.getUserLoading;
    },
  },
  created() {
    this.$store.dispatch('listBlockedUsers');
  },
  methods: {
    changeShow(val) {
      if (val === 'unblock') {
        this.unblock_show = !this.unblock_show;
      } else {
        this.block_show = !this.block_show;
      }
    },
    unblockUser() {
      let user = { user_id: this.unblock_id };
      this.$store
        .dispatch('unbanUser', user)
        .then((res) => {
          alert(res);
          this.unblock_show = !this.unblock_show;
        })
        .catch((err) => {
          alert('Something went wrong with unbanning the user');
          console.log(err);
        });
    },
    openModal(item) {
      this.unblock_id = item.user_id;
      this.unblock_show = !this.unblock_show;
    },
  },
};
</script>

<style scoped></style>
