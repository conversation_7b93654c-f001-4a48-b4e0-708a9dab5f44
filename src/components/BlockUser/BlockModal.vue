<template>
  <div>
    <CButton
      color="danger"
      class="float-right"
      shape="pill"
      @click="show = !show"
      >Block</CButton
    >
    <CModal :title="'Block User'" size="lg" :show.sync="show">
      <StartFormWizard :title="'Block User'" />
      <template slot="footer">
        <CButtonToolbar>
          <CButton type="button" class="float-right" variant="outline"
            >Cancel</CButton
          >
          <CButton
            type="submit"
            class="float-right"
            color="danger"
            variant="outline"
            >Block</CButton
          >
        </CButtonToolbar>
      </template>
    </CModal>
  </div>
</template>

<script>
import StartFormWizard from '@/components/FormWizard/BlockUser/StartFormWizard';
export default {
  name: 'BlockModal',
  components: { StartFormWizard },
  data() {
    return {
      show: false,
    };
  },
};
</script>

<style scoped></style>
