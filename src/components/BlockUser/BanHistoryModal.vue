<template>
  <div>
    <div v-show="checkAction('user_list')">
      <CButton
        color="dark"
        @click="openModal"
        v-c-tooltip="'History'"
        variant="outline"
      >
        <CIcon name="cil-list-rich"></CIcon>
      </CButton>
    </div>
    <CModal
      :title="'User ID ' + user.id + ' Blocked History'"
      :show.sync="show"
      centered
      size="lg"
      :close-on-backdrop="false"
    >
      <CDataTable
        :loading="ban_loading"
        :sorter-value="{ column: 'created_at', asc: false }"
        pagination
        :items-per-page="5"
        column-filter
        sorter
        :items="ban_history"
        :fields="table"
      >
        <template #deleted_at="{ item }">
          <td>
            {{ getDate(item.deleted_at) }}
          </td>
        </template>
        <template #created_at="{ item }">
          <td>
            {{ getDate(item.created_at) }}
          </td>
        </template>
        <template #unbanned_by="{ item }">
          <td>
            {{ item.unbanned_by !== '' ? item.unbanned_by : '-' }}
          </td>
        </template>
      </CDataTable>
      <template slot="footer">
        <div>
          <span></span>
        </div>
      </template>
    </CModal>
  </div>
</template>

<script>
export default {
  name: 'BanHistoryModal',
  props: {
    user: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      show: false,
      table: [
        { key: 'created_at', label: 'Blocked Date' },
        { key: 'banned_by', label: 'Blocked By' },
        { key: 'deleted_at', label: 'Unblocked Date' },
        { key: 'unbanned_by', label: 'Unblocked By' },
      ],
    };
  },
  filters: {
    formatDeletedAt(val) {
      return val !== null && val !== '' ? val : '-';
    },
  },
  computed: {
    ban_history() {
      return this.$store.getters.getBanHistory;
    },
    user_name() {
      return this.user.loginDisplayName + ' (' + this.user.id + ')';
    },
    ban_loading() {
      return this.$store.getters.getBanLoading;
    },
  },
  methods: {
    openModal() {
      if (this.checkAction('user_list')) {
        this.$store.dispatch('userBanHistory', this.user.id);
        this.show = !this.show;
      } else {
        this.$notify({
          group: 'error',
          title: 'Unauthorized',
          type: 'error',
          text: 'No permission to view',
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
      }
    },
    getStatus(item) {
      let status = '';
      if (item !== '' && item !== null) {
        status = 'Unblocked';
      } else {
        status = 'Blocked';
      }
      return status;
    },
    getDate(time) {
      if (time > 0 || time !== null) {
        let base = new Date(time * 1000);
        let yy = Intl.DateTimeFormat('en', { year: 'numeric' }).format(base);
        let mn = Intl.DateTimeFormat('en', { month: '2-digit' }).format(base);
        let dd = Intl.DateTimeFormat('en', { day: '2-digit' }).format(base);
        let hh = Intl.DateTimeFormat('en', {
          hour: 'numeric',
          hour12: false,
        }).format(base);
        let mm = base.getMinutes();
        if (mm < 10) {
          mm = '0' + mm;
        }
        let ss = base.getSeconds();
        if (ss < 10) {
          ss = '0' + ss;
        }
        return `${yy}/${mn}/${dd} ${hh}:${mm}:${ss}`;
      } else {
        return '-';
      }
    },
  },
};
</script>

<style scoped></style>
