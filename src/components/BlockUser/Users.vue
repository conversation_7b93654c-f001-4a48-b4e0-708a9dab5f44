<template>
  <div>
    <CCard>
      <CCardHeader>User List</CCardHeader>
      <CCardBody>
        <CForm @submit.prevent="searchForUser">
          <CRow class="search_fields">
            <CCol>
              <CInput v-model="search.id" label="User ID" />
            </CCol>
            <CCol>
              <CInput v-model="search.username" label="Username" />
            </CCol>
            <CCol>
              <CInput v-model="search.loginDisplayName" label="Name" />
            </CCol>
            <CCol>
              <CInput v-model="search.loginEmail" label="User Email" />
            </CCol>
            <CCol>
              <CSelect
                :value.sync="search.isBlocked"
                label="Status"
                :options="blockOptions"
              />
            </CCol>
            <CCol>
              <CSelect
                :value.sync="search.isVerified"
                label="Verified"
                :options="verifiedOptions"
              />
            </CCol>
            <CCol>
              <CButtonToolbar style="margin-top: 27px">
                <CButton type="submit" variant="outline" color="success">
                  Search
                  <CIcon name="cil-magnifying-glass"></CIcon>
                </CButton>
                <CButton
                  type="button"
                  variant="outline"
                  color="warning"
                  @click="resetList"
                >
                  Reset
                  <CIcon name="cil-action-undo"></CIcon>
                </CButton>
              </CButtonToolbar>
            </CCol>
          </CRow>
        </CForm>
        <CDataTable
          :sorter-value="sortValue"
          :loading="userLoading"
          sorter
          :fields="userTableFields"
          :items="users"
        >
          <template #isBlocked="{ item }">
            <td>
              <CBadge
                v-if="item.isBlocked === 'Blocked'"
                :color="getBadge(item.isBlocked)"
              >
                {{ item.isBlocked }}
              </CBadge>
              <div v-else>
                {{ item.isBlocked }}
              </div>
            </td>
          </template>
          <template #isVerified="{ item }">
            <td>
              <CBadge :color="getBadge(item.isVerified)">
                {{ item.isVerified }}
              </CBadge>
            </td>
          </template>
          <template #username="{ item }">
            <td>
              {{ item.username | name_email }}
            </td>
          </template>
          <template #loginDisplayName="{ item }">
            <td>
              {{ item.loginDisplayName | name_email }}
            </td>
          </template>
          <template #loginEmail="{ item }">
            <td>
              {{ item.loginEmail | name_email }}
            </td>
          </template>
          <template #actions="{ item }">
            <td>
              <CButtonToolbar>
                <CButton
                  v-if="item.isBlocked === '-'"
                  color="danger"
                  variant="outline"
                  @click="updateUserData(item, 'block')"
                  v-show="checkAction('block_toggle')"
                  :key="item.isBlocked"
                  v-c-tooltip="'Block User'"
                >
                  <CIcon name="cil-ban"></CIcon>
                </CButton>
                <CButton
                  v-else
                  color="warning"
                  @click="updateUserData(item, 'unblock')"
                  variant="outline"
                  v-show="checkAction('unblock_toggle')"
                  :key="item.isBlocked"
                  v-c-tooltip="'Unblock User'"
                >
                  <CIcon name="cil-check-circle"></CIcon>
                </CButton>
                <CButton
                  v-if="item.isVerified === 'Yes'"
                  color="info"
                  variant="outline"
                  @click="updateUserData(item, 'unwhitelist')"
                  v-c-tooltip="'Unverify User'"
                  :key="item.id + item.isVerified"
                >
                  <CIcon name="cil-user-unfollow"></CIcon>
                </CButton>
                <CButton
                  v-else
                  color="primary"
                  variant="outline"
                  @click="updateUserData(item, 'whitelist')"
                  v-c-tooltip="'Verify User'"
                  :key="item.id + item.isVerified"
                >
                  <CIcon name="cil-user-follow"></CIcon>
                </CButton>
                <UserHistoryModal
                  :user="item"
                  v-show="checkAction('user_list')"
                />
              </CButtonToolbar>
            </td>
          </template>
        </CDataTable>
        <CPagination
          @update:activePage="lookAtPage"
          :pages="pages"
          :activePage.sync="currentPage"
        >
          <template slot="previous-button"> &lsaquo; Prev </template>
          <template slot="next-button"> Next &rsaquo; </template>
        </CPagination>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import { mapGetters, mapActions, mapMutations } from 'vuex';
import UserHistoryModal from '@/components/BlockUser/UserHistoryModal';

export default {
  name: 'Users',
  components: { UserHistoryModal },
  data() {
    return {
      currentId: '',
      userTableFields: [
        { key: 'id', label: 'User ID' },
        { key: 'profile_id', label: 'Profile ID' },
        { key: 'username', label: 'Username' },
        { key: 'loginDisplayName', label: 'Name' },
        { key: 'loginEmail', label: 'User Email' },
        { key: 'isBlocked', label: 'Status' },
        { key: 'isVerified', label: 'Verified' },
        { key: 'actions', label: 'Actions', sorter: false, filter: false },
      ],
      search: {
        id: '',
        username: '',
        loginDisplayName: '',
        loginEmail: '',
        isBlocked: '',
        isVerified: '',
        page: parseInt(this.currentPage),
      },
      suedo: {
        id: '',
        username: '',
        loginDisplayName: '',
        loginEmail: '',
        isBlocked: '',
        page: parseInt(this.currentPage),
      },
      blockOptions: [
        { label: 'No Filter', value: '' },
        { label: 'Blocked', value: true },
        { label: '-', value: false },
      ],
      verifiedOptions: [
        { label: 'No Filter', value: '' },
        { label: 'Yes', value: true },
        { label: 'No', value: false },
      ],
      sortValue: { column: 'id', asc: false },
    };
  },
  filters: {
    name_email(val) {
      return val !== null && val !== '' ? val : '-';
    },
  },
  computed: {
    ...mapGetters({
      users: 'getListOfUsers',
      userLoading: 'getUserLoading',
      pages: 'getPages',
      nowPage: 'getCurrentPage',
    }),
    currentPage: {
      get() {
        return this.nowPage;
      },
      set(val) {
        this.setNewPage(val);
      },
    },
  },
  created() {
    this.resetList();
  },
  methods: {
    ...mapMutations({
      setNewPage: 'SET_CURRENT_PAGE',
    }),
    ...mapActions({
      listUsers: 'getUserData',
      updateUser: 'updateUserAction',
    }),
    searchForUser() {
      this.suedo.id = this.search.id ?? '';
      this.suedo.username = this.search.username ?? '';
      this.suedo.loginDisplayName = this.search.loginDisplayName ?? '';
      this.suedo.loginEmail = this.search.loginEmail ?? '';
      this.suedo.isBlocked = this.search.isBlocked ?? '';
      this.suedo.isVerified = this.search.isVerified ?? '';
      this.suedo.page = this.search.page ?? 1;
      this.listUsers(this.suedo);
    },
    resetList() {
      const defaultSetting = {
        id: '',
        username: '',
        loginDisplayName: '',
        loginEmail: '',
        isBlocked: '',
        isVerified: '',
        page: this.currentPage,
      };
      this.suedo = defaultSetting;
      this.search = defaultSetting;
      this.listUsers(defaultSetting);
    },
    updateUserData(item, action) {
      let payload = {
        ban: false,
        whitelist: false,
        firebaseId: item.loginProviderUID,
      };
      let status = '';
      let swalConfig = {};
      switch (action) {
        case 'block':
          swalConfig = {
            title: 'Block Confirmation',
            icon: 'warning',
            html: `Block User ID <strong>${item.id}</strong> ?`,
          };
          status = 'banned';
          payload.ban = true;
          break;
        case 'unblock':
          swalConfig = {
            title: 'Unblock Confirmation',
            icon: 'warning',
            html: `Unblock User ID <strong>${item.id}</strong> ?`,
          };
          status = 'unbanned';
          payload.ban = false;
          break;
        case 'whitelist':
          swalConfig = {
            title: 'Verify User Confirmation',
            icon: 'warning',
            html: `Verify User ID <strong>${item.id}</strong> ?`,
          };
          status = 'verified';
          payload.whitelist = true;
          break;
        case 'unwhitelist':
          swalConfig = {
            title: 'Unverify User Confirmation',
            icon: 'warning',
            html: `Unverify User ID <strong>${item.id}</strong> ?`,
          };
          status = 'unverified';
          payload.whitelist = false;
          break;
        default:
          break;
      }
      this.$swal
        .fire({
          ...swalConfig,
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Yes',
          cancelButtonText: 'Cancel',
          buttonsStyling: true,
        })
        .then(({ isConfirmed }) => {
          if (isConfirmed) {
            this.updateUser({ payload, status });
          }
        });
    },
    lookAtPage(number) {
      let newPage = {
        id: this.suedo.id ?? '',
        username: this.suedo.username ?? '',
        loginDisplayName: this.suedo.loginDisplayName ?? '',
        loginEmail: this.suedo.loginEmail ?? '',
        page: number,
        isBlocked: this.suedo.isBlocked ?? '',
        isVerified: this.suedo.isVerified ?? '',
      };
      this.listUsers(newPage);
    },
    getBadge(status) {
      return status === 'Blocked' || status === 'No' ? 'danger' : 'success';
    },
  },
};
</script>

<style scoped>
@media (max-width: 750px) {
  .search_fields {
    display: flex;
    flex-direction: column;
  }
}
</style>
