<template>
  <div>
    <div v-show="checkAction('user_list')">
      <CButton
        color="dark"
        @click="openModal"
        v-c-tooltip="'History'"
        variant="outline"
      >
        <CIcon name="cil-list-rich"></CIcon>
      </CButton>
    </div>
    <CModal
      :title="'User ID ' + user.id + ' Changes History'"
      :show.sync="show"
      centered
      size="lg"
      :close-on-backdrop="false"
    >
      <CDataTable
        :loading="isLoading"
        :sorter-value="{ column: 'created_at', asc: false }"
        pagination
        :items-per-page="5"
        column-filter
        sorter
        :items="results"
        :fields="table"
      >
        <template #action="{ item }">
          <td style="text-transform: capitalize">
            {{ item.action }}
          </td>
        </template>
        <template #user="{ item }">
          <td>
            {{ item.user.name }}
          </td>
        </template>
        <template #createdAt="{ item }">
          <td>
            {{ item.createdAt | dateTime }}
          </td>
        </template>
      </CDataTable>
      <template slot="footer">
        <div>
          <span></span>
        </div>
      </template>
    </CModal>
  </div>
</template>

<script>
import { format, parseISO } from 'date-fns';
import axios from 'axios';
import * as headers from '@/helpers/headers';
export default {
  name: 'UserHistoryModal',
  props: {
    user: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  data() {
    return {
      show: false,
      results: [],
      isLoading: false,
      table: [
        { key: 'action', label: 'Action' },
        { key: 'user', label: 'Performed By' },
        { key: 'createdAt', label: 'Date' },
      ],
    };
  },
  filters: {
    dateTime(val) {
      return format(parseISO(val), 'dd-MM-yyyy HH:mm:ss');
    },
  },
  methods: {
    async openModal() {
      this.isLoading = true;
      const url = `${process.env.VUE_APP_COMMENTSERVICE_USER_ACTION_HISTORY}${this.user.loginProviderUID}`;
      const { data } = await axios.get(
        url,
        headers.createHeaders(
          this.$store.getters.getUserToken,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      );
      this.results = data.data;
      this.isLoading = false;
      this.show = !this.show;
    },
  },
};
</script>

<style scoped></style>
