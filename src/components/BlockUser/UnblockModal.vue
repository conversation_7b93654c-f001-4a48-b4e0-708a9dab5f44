<template>
  <div>
    <CButton color="warning" shape="pill" @click="show = !show"
      >Unblock</CButton
    >
    <CModal :title="'Unblock User'" size="lg" :show.sync="show">
      <StartFormWizard :title="'Unblock User'" />
      <template slot="footer">
        <CButtonToolbar>
          <CButton type="button" class="float-right" variant="outline"
            >Cancel</CButton
          >
          <CButton
            type="submit"
            class="float-right"
            color="danger"
            variant="outline"
            >Unblock</CButton
          >
        </CButtonToolbar>
      </template>
    </CModal>
  </div>
</template>

<script>
import StartFormWizard from '@/components/FormWizard/BlockUser/StartFormWizard';
export default {
  name: 'UnblockModal',
  components: { StartFormWizard },
  data() {
    return {
      show: false,
    };
  },
};
</script>

<style scoped></style>
