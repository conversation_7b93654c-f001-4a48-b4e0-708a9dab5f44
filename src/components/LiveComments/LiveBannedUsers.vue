<template>
  <CCard>
    <CCardHeader>List of Live Banned Users</CCardHeader>
    <CCardBody>
      <CRow>
        <CCol>
          <CDataTable
            :loading="loading"
            small
            :sorter-value="{ column: 'createdAt', asc: false }"
            sorter
            :fields="list_fields"
            :items="liveBannedUsersList"
          >
            <template #user="{ item }">
              <td>{{ item.name }}({{ item.profileId }})</td>
            </template>
            <template #createdAt="{ item }">
              <td>{{ formatDate(item.createdAt) }}</td>
            </template>
            <template #show_details="{ item }">
              <td>
                <CButtonToolbar>
                  <CButton
                    v-show="checkAction('unban_user_live_comments')"
                    @click="unbanLiveUser(item)"
                    variant="outline"
                    color="primary"
                    v-c-tooltip="'Unban User'"
                    ><CIcon name="cil-user-follow"></CIcon
                  ></CButton>
                </CButtonToolbar>
              </td>
            </template>
          </CDataTable>
        </CCol>
      </CRow>
    </CCardBody>
  </CCard>
</template>

<script>
import axios from 'axios';
import * as headers from '@/helpers/headers';
import { format, parseISO } from 'date-fns';
import Vue from 'vue';

export default {
  data() {
    return {
      loading: false,
      list_fields: [
        { key: 'firebaseId', label: 'Firebase ID' },
        { key: 'id', label: 'ID' },
        { key: 'user', label: 'User' },
        { key: 'createdAt', label: 'Created Time' },
        { key: 'show_details', sorter: false, filter: false, label: 'Action' },
      ],
      liveBannedUsers: [],
    };
  },
  computed: {
    liveBannedUsersList() {
      return this.liveBannedUsers;
    },
  },
  created() {
    return new Promise((resolve, reject) => {
      let url = process.env.VUE_APP_LIST_LIVE_BANNED_USERS;
      axios
        .get(url, headers.createHeaders(this.$store.getters.getUserToken))
        .then((res) => {
          this.liveBannedUsers = res.data.data;
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  methods: {
    formatDate(date) {
      return format(parseISO(date), 'yyyy-MM-dd HH:mm:ss');
    },
    unbanLiveUser(item) {
      this.$swal
        .fire({
          icon: 'question',
          html: '<p>Unban this user?</p><p>' + item.firebaseId + '</p>',
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            let userObj = { firebaseId: item.firebaseId };
            axios
              .post(
                process.env.VUE_APP_LIVE_COMMENTS_UNBAN_USER,
                userObj,
                headers.createHeaders(this.$store.getters.getUserToken)
              )
              .then(() => {
                Vue.swal({
                  toast: true,
                  position: 'top-end',
                  showConfirmButton: false,
                  showCancelButton: false,
                  confirmButtonText: 'Ok',
                  timer: 5000,
                  icon: 'success',
                  title: 'Success',
                  text: `Successfully Unband User`,
                });
              })
              .catch((err) => {
                console.log(err);
                Vue.swal({
                  toast: true,
                  position: 'top-end',
                  showConfirmButton: false,
                  showCancelButton: false,
                  confirmButtonText: 'Ok',
                  timer: 5000,
                  icon: 'error',
                  title: 'Error',
                  text: `Failed to unband user`,
                });
              });
          }
        });
    },
  },
};
</script>
