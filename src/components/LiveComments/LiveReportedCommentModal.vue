<template>
  <div>
    <CButton
      class="float-right"
      variant="outline"
      color="primary"
      @click="show = true"
      v-c-tooltip="'View Comment'"
      ><CIcon name="cil-speech"></CIcon
    ></CButton>
    <CModal
      title="Comment Details"
      color="default"
      size="lg"
      :show.sync="show"
      centered
    >
      <CRow>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Comment By</small><br />
            <strong class="h4"
              >{{ report.comment.user.name }}({{
                report.comment.user.profileId
              }})</strong
            >
          </CCallout>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CCallout color="warning">
            <small class="text-muted">Comment</small><br />
            <strong class="h6">{{ report.comment.message }}</strong>
          </CCallout>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CDataTable
            :sorter-value="{ column: 'reportedAt', asc: false }"
            :items="report.reported"
            :fields="fields"
            hover
            sorter
          >
            <template #reportedAt="{ item }">
              <td>
                {{ item.reportedAt | dateTime }}
              </td>
            </template>
          </CDataTable>
        </CCol>
      </CRow>
    </CModal>
  </div>
</template>

<script>
import { format } from 'date-fns';
export default {
  name: 'LiveReportedCommentModal',
  components: '',
  props: {
    report: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      fields: [
        { key: 'reportedAt', sorter: true, label: 'Report Date' },
        { key: 'reasonName', sorter: true, label: 'Report Reason' },
      ],
      show: false,
    };
  },
  filters: {
    dateTime(val) {
      return format(new Date(val), 'yyyy-MM-dd HH:mm:ss');
    },
  },
  methods: {},
};
</script>

<style scoped></style>
