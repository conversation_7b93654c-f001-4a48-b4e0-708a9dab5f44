<template>
  <div>
    <CCard>
      <CCardHeader> Live Comments - Reported Comments </CCardHeader>
      <CCardBody>
        <CDataTable
          :loading="commentsLoading"
          :key="reportedComments.length"
          :items="reportedComments"
          :fields="tableHeaders"
          items-per-page-select
          :items-per-page="itemsPerPage"
          small
          @pagination-change="changeItemsPerPage"
        >
          <template #id="{ item }">
            <td>
              {{ item.comment.id }}
            </td>
          </template>

          <template #comment="{ item }">
            <td style="word-break: break-word">
              {{ item.comment.message }}
            </td>
          </template>
          <template #status="{ item }">
            <td>
              <span v-if="item.status === 1">Hidden</span>
              <span v-else>-</span>
            </td>
          </template>

          <template #createdAt="{ item }">
            <td>
              {{ formatDt(item.createdAt) }}
            </td>
          </template>

          <template #user="{ item }">
            <td>
              {{ item.comment.user.name }}({{ item.comment.user.profileId }})
            </td>
          </template>
          <template #actions="{ item }">
            <td>
              <CButtonToolbar>
                <LiveReportedCommentModal :report="item" />
                <CButton
                  v-show="checkAction('hide_comment_live_comments')"
                  color="warning"
                  v-c-tooltip="'Hide Comment'"
                  :disabled="item.status === 1"
                  :key="`{{item.id}}-hide-comment`"
                  @click="hideReportedComment(item.id)"
                >
                  <font-awesome-icon icon="eye-slash" />
                </CButton>
                <CButton
                  v-show="checkAction('ban_user_live_comments')"
                  color="primary"
                  v-if="item.isUserBanned === false"
                  v-c-tooltip="'Ban User'"
                  :key="`{{item.id}}-ban-user`"
                  @click="banUser(item.comment.user)"
                >
                  <CIcon name="cil-user-unfollow"></CIcon>
                </CButton>
                <CButton
                  v-show="checkAction('unban_user_live_comments')"
                  color="info"
                  v-else-if="item.isUserBanned === true"
                  v-c-tooltip="'Unban User'"
                  :key="`{{item.id}}-unban-user`"
                  @click="unbanUser(item.comment.user.firebaseId)"
                >
                  <CIcon name="cil-user-follow"></CIcon>
                </CButton>
              </CButtonToolbar>
            </td>
          </template>
        </CDataTable>
      </CCardBody>
      <CPagination
        @update:activePage="goToPage"
        :pages="pagination.totalPages"
        :activePage.sync="pagination.currentPage"
        align="center"
      />
    </CCard>
    <CToaster position="bottom-center">
      <CToast :key="'toast'" :show="showToast" color="info">
        There are no more reported comments
      </CToast>
    </CToaster>
  </div>
</template>
<script>
import * as headers from '@/helpers/headers';
import axios from 'axios';
import { format } from 'date-fns';
import parseISO from 'date-fns/parseISO';
import { mapGetters } from 'vuex';
import LiveReportedCommentModal from './LiveReportedCommentModal';
export default {
  name: 'LiveReportedComment',
  components: { LiveReportedCommentModal },
  data() {
    return {
      showToast: false,
      commentsLoading: false,
      tableHeaders: [
        { key: 'id', label: 'Report ID' },
        { key: 'comment', label: 'Comment' },
        { key: 'reasonName', label: 'Reason' },
        { key: 'status', label: 'Status' },
        { key: 'user', label: 'Commented By' },
        { key: 'createdAt', label: 'Reported At' },
        { key: 'actions', label: 'Actions', sorter: false, filter: false },
      ],
      reportedComments: [],
      toastTimer: null,
      pagination: {
        currentPage: 1,
        totalResults: null,
        totalPages: null,
        hasNextPage: false,
      },
      itemsPerPage: 10,
    };
  },
  computed: {
    ...mapGetters({
      userToken: 'getUserToken',
    }),
  },
  created() {
    if (this.reportedComments.length === 0) {
      this.listLiveReportedComments();
    }
  },
  methods: {
    listLiveReportedComments(currentPage) {
      let page = currentPage ? currentPage : 1;
      let limit = this.itemsPerPage;
      this.commentsLoading = true;
      axios
        .get(
          process.env.VUE_APP_LIVE_COMMENTS_REPORTED +
            '?page=' +
            page +
            '&limit=' +
            limit,
          headers.createHeaders(this.userToken, process.env.VUE_APP_ADMIN_TOKEN)
        )
        .then((res) => {
          if (res.data.data.length === 0) {
            this.showToaster();
            return;
          }
          this.reportedComments = res.data.data;
          this.pagination = res.data.pagination;
          this.commentsLoading = false;
        })
        .catch((err) => {
          this.commentsLoading = false;
          console.error(err);
        });
    },
    hideReportedComment(id) {
      this.$swal
        .fire({
          icon: 'question',
          title: 'Hide Comment?',
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            const url = process.env.VUE_APP_LIVE_COMMENTS_HIDE_COMMENT.replace(
              '{id}',
              id
            );
            axios
              .post(
                url,
                {},
                headers.createHeaders(
                  this.userToken,
                  process.env.VUE_APP_ADMIN_TOKEN
                )
              )
              .then(() => {
                const index = this.reportedComments.findIndex(
                  (comment) => comment.id === id
                );
                if (index > -1) {
                  this.$set(this.reportedComments[index], 'status', 1);
                }
                this.$swal.fire({
                  icon: 'success',
                  title: 'Success!',
                  text: 'Successfully hidden comment',
                  timer: 1500,
                  showCancelButton: false,
                });
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'error',
                  title: 'Error!',
                  text: 'Failed to hide comment',
                  timer: 1500,
                  showCancelButton: false,
                });
              });
          }
        });
    },
    banUser(user) {
      this.$swal
        .fire({
          icon: 'question',
          title: 'Ban User',
          text: 'Ban User from Live Comments?',
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            let userObj = {
              firebaseId: user.firebaseId,
              name: user.name,
              profileId: user.profileId,
            };
            axios
              .post(
                process.env.VUE_APP_LIVE_COMMENTS_BAN_USER,
                userObj,
                headers.createHeaders(
                  this.userToken,
                  process.env.VUE_APP_ADMIN_TOKEN
                )
              )
              .then(() => {
                for (let i = 0; i < this.reportedComments.length; i++) {
                  if (
                    this.reportedComments[i]?.comment?.user?.firebaseId ===
                    user.firebaseId
                  ) {
                    this.$set(this.reportedComments[i], 'isUserBanned', true);
                  }
                }
                this.$swal.fire({
                  icon: 'success',
                  title: 'Success!',
                  text: 'Successfully banned user from live comments',
                  timer: 1500,
                  showCancelButton: false,
                });
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'error',
                  title: 'Error!',
                  text: 'Failed to ban user from live comments',
                  timer: 1500,
                  showCancelButton: false,
                });
              });
          }
        });
    },
    unbanUser(firebaseId) {
      this.$swal
        .fire({
          icon: 'question',
          title: 'Unban User',
          text: 'Unban User from Live Comments?',
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            axios
              .post(
                process.env.VUE_APP_LIVE_COMMENTS_UNBAN_USER,
                { firebaseId },
                headers.createHeaders(
                  this.userToken,
                  process.env.VUE_APP_ADMIN_TOKEN
                )
              )
              .then(() => {
                for (let i = 0; i < this.reportedComments.length; i++) {
                  if (
                    this.reportedComments[i]?.comment?.user?.firebaseId ===
                    firebaseId
                  ) {
                    this.$set(this.reportedComments[i], 'isUserBanned', false);
                  }
                }
                this.$swal.fire({
                  icon: 'success',
                  title: 'Success!',
                  text: 'Successfully ubanned user from live comments',
                  timer: 1500,
                  showCancelButton: false,
                });
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'error',
                  title: 'Error!',
                  text: 'Failed to unban user from live comments',
                  timer: 1500,
                  showCancelButton: false,
                });
              });
          }
        });
    },
    formatDt(date) {
      return format(parseISO(date), 'dd/MM/yyyy HH:mm');
    },
    showToaster() {
      if (this.toastTimer) {
        clearInterval(this.toastTimer);
        this.showToast = false;
      }

      this.showToast = true;
      this.toastTimer = setInterval(() => {
        this.showToast = false;
      }, 2000);
    },
    goToPage(currentPage) {
      this.listLiveReportedComments(currentPage);
    },
    changeItemsPerPage(limit) {
      this.itemsPerPage = limit;
      this.listLiveReportedComments();
    },
  },
};
</script>
