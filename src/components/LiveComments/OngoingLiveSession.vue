<template>
  <div>
    <CCard>
      <CCardHeader>Live Session</CCardHeader>
      <CDataTable
        :loading="loading"
        :key="liveSessions.length"
        :items="liveSessions"
        :fields="tableHeaders"
        small
        pagination
      >
        <template #startDate="{ item }">
          <td>
            {{ formatDt(item.startDate) }}
          </td>
        </template>
        <template #endDate="{ item }">
          <td>
            {{ formatDt(item.endDate) }}
          </td>
        </template>

        <template #contentId="{ item }">
          <td>
            {{ item.contentId }}
          </td>
        </template>

        <template #actions="{ item }">
          <td>
            <CButton
              v-show="checkAction('end_session_live_comments')"
              variant="outline"
              color="danger"
              v-c-tooltip="'End Session'"
              :disabled="item.status === 0"
              :key="`{{item._id}}-end-live-session`"
              @click="endLiveSession(item._id)"
            >
              <CIcon name="cil-media-stop"></CIcon>
            </CButton>
          </td>
        </template>
      </CDataTable>
    </CCard>
    <CToaster position="bottom-center">
      <CToast :key="'toast'" :show="showToast" color="info">
        There are no more ongoing live sessions
      </CToast>
    </CToaster>
  </div>
</template>
<script>
import * as headers from '@/helpers/headers';
import axios from 'axios';
import { format } from 'date-fns';
import parseISO from 'date-fns/parseISO';
import { mapGetters } from 'vuex';

export default {
  name: 'OngoingLiveSession',
  data() {
    return {
      showToast: false,
      loading: false,
      tableHeaders: [
        { key: 'startDate', label: 'Start Date' },
        { key: 'endDate', label: 'End Date' },
        { key: 'contentId', label: 'Feed ID' },
        { key: 'actions', label: 'Actions', sorter: false, filter: false },
      ],
      liveSessions: [],
      toastTimer: null,
    };
  },
  computed: {
    ...mapGetters({
      userToken: 'getUserToken',
    }),
  },
  created() {
    if (this.liveSessions.length === 0) {
      this.listLiveSessions();
    }
  },
  methods: {
    listLiveSessions() {
      axios
        .get(
          process.env.VUE_APP_LIVE_COMMENTS_GET_LIVE_SESSION,
          headers.createHeaders(this.userToken, process.env.VUE_APP_ADMIN_TOKEN)
        )
        .then((res) => {
          this.liveSessions = res.data.data;
          if (this.liveSessions.length === 0) {
            this.showToaster();
            return;
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },
    endLiveSession(id) {
      this.$swal
        .fire({
          icon: 'question',
          title: 'End Live Comment Session?',
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            const url = process.env.VUE_APP_LIVE_COMMENTS_SESSION_END_LIVE;
            axios
              .post(
                url,
                { sessionId: id },
                headers.createHeaders(
                  this.userToken,
                  process.env.VUE_APP_ADMIN_TOKEN
                )
              )
              .then(() => {
                this.listLiveSessions();
                this.$swal.fire({
                  icon: 'success',
                  title: 'Success!',
                  text: 'Successfully end live comment session',
                  timer: 1500,
                  showCancelButton: false,
                });
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'error',
                  title: 'Error!',
                  text: 'Failed to end live comment session',
                  timer: 1500,
                  showCancelButton: false,
                });
              });
          }
        });
    },
    formatDt(date) {
      return format(parseISO(date), 'dd/MM/yyyy HH:mm');
    },
    showToaster() {
      if (this.toastTimer) {
        clearInterval(this.toastTimer);
        this.showToast = false;
      }

      this.showToast = true;
      this.toastTimer = setInterval(() => {
        this.showToast = false;
      }, 2000);
    },
  },
};
</script>
