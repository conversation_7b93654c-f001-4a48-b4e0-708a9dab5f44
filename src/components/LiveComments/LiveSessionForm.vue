<template>
  <CCard>
    <CCardHeader>Create Live Session</CCardHeader>
    <CCardBody>
      <CRow>
        <CCol>
          <CRow class="mb-3">
            <CCol lg="6" md="12" sm="12">
              <label for="">Start Time</label>
              <vc-date-picker
                v-model="session.startDate"
                mode="dateTime"
                :masks="{ inputDateTime24hr: 'YYYY-MM-DD HH:mm:ss' }"
                :model-config="{ type: 'string', mask: 'YYYY-MM-DD HH:mm:ss' }"
                :min-date="new Date()"
                is24hr
              >
                <template v-slot="{ inputValue, inputEvents }">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text">
                        <CIcon name="cil-calendar"></CIcon>
                      </span>
                    </div>
                    <input
                      class="form-control"
                      :value="inputValue"
                      v-on="inputEvents"
                    />
                  </div>
                </template>
              </vc-date-picker>
              <span
                v-if="start_errors"
                v-text="start_errors_text"
                class="invalid-error"
              />
            </CCol>
            <CCol lg="6" md="12" sm="12">
              <label for="">End Time</label>
              <vc-date-picker
                v-model="session.endDate"
                mode="dateTime"
                :masks="{ inputDateTime24hr: 'YYYY-MM-DD HH:mm:ss' }"
                :model-config="{ type: 'string', mask: 'YYYY-MM-DD HH:mm:ss' }"
                :min-date="session.startDate"
                :valid-hours="{ min: endValidHours }"
                is24hr
              >
                <template v-slot="{ inputValue, inputEvents }">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text">
                        <CIcon name="cil-calendar"></CIcon>
                      </span>
                    </div>
                    <input
                      class="form-control"
                      :value="inputValue"
                      v-on="inputEvents"
                    />
                  </div>
                  <span
                    v-if="end_errors"
                    v-text="end_error_text"
                    class="invalid-error"
                  />
                </template>
              </vc-date-picker>
            </CCol>
          </CRow>
        </CCol>
      </CRow>
      <br />
      <CRow>
        <CCol>
          <label for="feedOpt">Feed</label>
          <multiselect
            :class="[{ 'invalid-border': session.contentId.length === 0 }]"
            id="feedOpt"
            v-model="session.contentId"
            :multiple="false"
            track-by="value"
            label="label"
            :options="feeds"
            placeholder=""
            @input="onChangeFeed"
          >
            <span slot="noResult"
              >Oops! No Feed with the name. Consider changing the search
              query.</span
            >
          </multiselect>
          <span
            v-if="session.contentId.length === 0"
            v-text="'Please select at least 1 feed'"
            class="invalid-error"
          />
        </CCol>
      </CRow>
      <br />
      <CRow>
        <CCol>
          <CButton
            color="success"
            variant="outline"
            block
            @click="createLiveSession"
            >Submit</CButton
          >
        </CCol>
      </CRow>
    </CCardBody>
  </CCard>
</template>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>

<script>
import * as headers from '@/helpers/headers';
import axios from 'axios';
import { isBefore, parseISO, compareAsc } from 'date-fns';
import Multiselect from 'vue-multiselect';
import { mapGetters } from 'vuex';

export default {
  name: 'CreateLiveSessionForm',
  components: { Multiselect },
  data() {
    return {
      session: {
        startDate: '',
        endDate: '',
        contentId: '',
        type: 'feed',
      },
    };
  },
  computed: {
    ...mapGetters({
      userToken: 'getUserToken',
    }),
    feeds() {
      let use = [];
      let feed = this.$store.getters.getFeeds;
      feed.forEach((f) => {
        let data = {
          value: f.id,
          label: f.name,
        };
        use.unshift(data);
      });
      return use.sort((a, b) => a.label.localeCompare(b.label));
    },
    start_errors() {
      if (isBefore(parseISO(this.session.startDate), parseISO(this.now))) {
        return true;
      } else if (this.session.startDate === '') {
        return true;
      } else {
        return false;
      }
    },
    start_errors_text() {
      if (isBefore(parseISO(this.session.startDate), parseISO(this.now))) {
        return 'Start time must be AFTER current time';
      } else if (this.session.startDate === '') {
        return 'Please select a start time';
      } else {
        return '';
      }
    },
    end_errors() {
      if (
        isBefore(
          parseISO(this.session.endDate),
          parseISO(this.session.startDate)
        )
      ) {
        return true;
      } else if (
        compareAsc(
          parseISO(this.session.endDate),
          parseISO(this.session.startDate)
        ) === 0
      ) {
        return true;
      } else if (
        compareAsc(parseISO(this.session.endDate), parseISO(this.now)) === -1
      ) {
        return true;
      } else if (this.session.endDate === '') {
        return true;
      } else {
        return false;
      }
    },
    end_error_text() {
      if (
        isBefore(
          parseISO(this.session.endDate),
          parseISO(this.session.startDate)
        )
      ) {
        return 'End time must be AFTER start time';
      } else if (
        compareAsc(
          parseISO(this.session.endDate),
          parseISO(this.session.startDate)
        ) === 0
      ) {
        return 'End time must be AFTER start time';
      } else if (
        compareAsc(parseISO(this.session.endDate), parseISO(this.now)) === -1
      ) {
        return 'End time must be AFTER current time';
      } else if (this.session.endDate === '') {
        return 'Please select an end time';
      } else {
        return '';
      }
    },
    endValidHours() {
      if (
        new Date(this.session.startDate).getDay() ==
        new Date(this.session.endDate).getDay()
      ) {
        return new Date(this.session.startDate).getHours();
      } else {
        return 0;
      }
    },
  },
  async created() {
    if (this.$store.getters.getFeeds.length === 0) {
      await this.$store.dispatch('listFeeds');
    }
  },
  methods: {
    onChangeFeed(input) {
      if (input) {
        this.session.contentId = input;
      } else {
        this.session.contentId = '';
      }
    },
    createLiveSession() {
      let session = {};
      Object.entries(this.session).forEach(([prop, val]) => {
        if (['contentId'].includes(prop)) {
          session[prop] = this.session[prop].value;
        } else if (['startDate', 'endDate'].includes(prop)) {
          session[prop] = parseISO(val).toISOString();
        } else {
          session[prop] = val;
        }
      });
      this.$swal
        .fire({
          icon: 'question',
          title: 'Live Session',
          text: 'Create Live Session?',
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            axios
              .post(
                process.env.VUE_APP_LIVE_COMMENTS_CREATE_SESSION,
                session,
                headers.createHeaders(
                  this.userToken,
                  process.env.VUE_APP_ADMIN_TOKEN
                )
              )
              .then(() => {
                this.$swal
                  .fire({
                    icon: 'success',
                    text: 'Live Comment Session successfully created!',
                    showCancelButton: false,
                  })
                  .then((res) => {
                    if (res.isConfirmed) {
                      this.$router.push('/live-session');
                    }
                  });
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'error',
                  title: 'Error!',
                  text: 'Failed to create live comment session',
                  timer: 1500,
                  showCancelButton: false,
                });
              });
          }
        });
    },
  },
};
</script>
