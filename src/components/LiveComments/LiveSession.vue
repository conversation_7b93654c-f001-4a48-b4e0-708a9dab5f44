<template>
  <div>
    <CCard>
      <CCardHeader>Upcoming Live Session</CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <router-link :to="'live-session/create'">
              <CButton
                v-show="checkAction('create_session_live_comments')"
                class="float-right mb-2"
                variant="outline"
                color="success"
                shape="pill"
                >Create Live Session</CButton
              >
            </router-link>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CDataTable
              :loading="loading"
              :key="upcomingLiveSessions.length"
              :items="upcomingLiveSessions"
              :fields="tableHeaders"
              small
              pagination
            >
              <template #startDate="{ item }">
                <td>
                  {{ formatDt(item.startDate) }}
                </td>
              </template>
              <template #endDate="{ item }">
                <td>
                  {{ formatDt(item.endDate) }}
                </td>
              </template>

              <template #contentId="{ item }">
                <td>
                  {{ item.contentId }}
                </td>
              </template>

              <template #actions="{ item }">
                <td>
                  <CButton
                    v-show="checkAction('start_session_live_comments')"
                    variant="outline"
                    color="success"
                    v-c-tooltip="'Start Session'"
                    :disabled="item.status === 1"
                    :key="`{{item._id}}-live-session`"
                    @click="startLiveSession(item._id)"
                  >
                    <CIcon name="cil-media-play"></CIcon>
                  </CButton>
                </td>
              </template>
            </CDataTable>
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
    <CToaster position="bottom-center">
      <CToast :key="'toast'" :show="showToast" color="info">
        There are no more upcoming live sessions
      </CToast>
    </CToaster>
  </div>
</template>
<script>
import * as headers from '@/helpers/headers';
import axios from 'axios';
import { format } from 'date-fns';
import parseISO from 'date-fns/parseISO';
import { mapGetters } from 'vuex';

export default {
  name: 'LiveSession',
  data() {
    return {
      showToast: false,
      loading: false,
      tableHeaders: [
        { key: 'startDate', label: 'Start Date' },
        { key: 'endDate', label: 'End Date' },
        { key: 'contentId', label: 'Feed ID' },
        { key: 'actions', label: 'Actions', sorter: false, filter: false },
      ],
      upcomingLiveSessions: [],
      toastTimer: null,
    };
  },
  computed: {
    ...mapGetters({
      userToken: 'getUserToken',
    }),
  },
  created() {
    if (this.upcomingLiveSessions.length === 0) {
      this.listUpcomingLiveSessions();
    }
  },
  methods: {
    listUpcomingLiveSessions() {
      axios
        .get(
          process.env.VUE_APP_LIVE_COMMENTS_GET_UPCOMING_SESSION,
          headers.createHeaders(this.userToken, process.env.VUE_APP_ADMIN_TOKEN)
        )
        .then((res) => {
          this.upcomingLiveSessions = res.data.data;
          if (this.upcomingLiveSessions.length === 0) {
            this.showToaster();
            return;
          }
        })
        .catch((err) => {
          console.error(err);
        });
    },
    startLiveSession(id) {
      this.$swal
        .fire({
          icon: 'question',
          title: 'Start Live Comment Session?',
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            const url = process.env.VUE_APP_LIVE_COMMENTS_SESSION_START_LIVE;
            axios
              .post(
                url,
                { sessionId: id },
                headers.createHeaders(
                  this.userToken,
                  process.env.VUE_APP_ADMIN_TOKEN
                )
              )
              .then(() => {
                this.listUpcomingLiveSessions();
                this.$swal.fire({
                  icon: 'success',
                  title: 'Success!',
                  text: 'Successfully start live comment session',
                  timer: 1500,
                  showCancelButton: false,
                });
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'error',
                  title: 'Error!',
                  text: 'Failed to start live comment session',
                  timer: 1500,
                  showCancelButton: false,
                });
              });
          }
        });
    },
    formatDt(date) {
      return format(parseISO(date), 'dd/MM/yyyy HH:mm');
    },
    showToaster() {
      if (this.toastTimer) {
        clearInterval(this.toastTimer);
        this.showToast = false;
      }

      this.showToast = true;
      this.toastTimer = setInterval(() => {
        this.showToast = false;
      }, 2000);
    },
  },
};
</script>
