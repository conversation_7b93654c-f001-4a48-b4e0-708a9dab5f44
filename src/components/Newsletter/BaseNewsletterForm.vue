<template>
  <div>
    <OverlayLoader v-if="loading" />
    <CInput
      @change="findArticle"
      v-model="main_article.article_unique_ID"
      label="Article ID to Use"
      invalid-feedback="Article Unique ID is required"
      :is-valid="validator"
    />
    <CSelect
      :value.sync="main_article.sequence_order"
      :options="order"
      label="Article Order"
      description="This will determine the order of articles"
      invalid-feedback="Article Order is required"
      :is-valid="validator"
    />
    <CInput v-model="main_article.heading" label="News Text Heading" />
    <label for="adCoverFile1">Article Image File</label>
    <input
      type="file"
      @change="onFileChange"
      id="adCoverFile1"
      class="form-control-file"
      data-content="cover"
    />
    <br />
    <CInput
      v-model="main_article.title"
      label="Article Title"
      invalid-feedback="Article Title is required"
      :is-valid="validator"
    />
    <CTextarea
      v-model="main_article.description"
      label="Article Description"
      invalid-feedback="Article Description is required"
      :is-valid="validator"
    />
  </div>
</template>

<script>
import slugify from 'slugify';
import axios from 'axios';
import OverlayLoader from '../views/OverlayLoader';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

export default {
  name: 'BaseNewsletterForm',
  components: { OverlayLoader },
  props: {
    main_article: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      order: [
        { value: '', label: 'Please Select a Sequence Order' },
        { value: 1, label: '1' },
        { value: 2, label: '2' },
        { value: 3, label: '3' },
        { value: 4, label: '4' },
        { value: 5, label: '5' },
      ],
    };
  },
  computed: {
    loading() {
      return this.$store.getters.getNewsletterLoading;
    },
  },
  methods: {
    validator(val) {
      return val ? val !== '' : false;
    },
    findArticle() {
      axios
        .get(
          process.env.VUE_APP_GET_ARTICLE_BY_UNIQUE_ID +
            this.main_article.article_unique_ID,
          headers.createHeaders(
            this.$store.getters.getUserToken,
            process.env.VUE_APP_TOKEN_VALUE
          )
        )
        .then((res) => {
          this.main_article.publisher_name = res.data.publisher.publisherName;
          this.main_article.article_unique_ID = res.data.uniqueID;
          this.main_article.article_link = res.data.url;
          this.main_article.title = res.data.title;
          this.main_article.description = res.data.description;
          if (res.data.mediaArray) {
            this.main_article.imageURL = res.data.mediaArray[0].url;
            let send = {
              url: res.data.mediaArray[0].url,
            };
            this.$emit('show-display', send);
          }
        })
        .catch((err) => {
          console.log(err.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          console.log(err);
        });
    },
    onFileChange(event) {
      let input = event.target;
      if (input.files && input.files[0]) {
        this.googleTempUpload(input);
      }
    },
    googleTempUpload(input) {
      let fileName = slugify(input.files[0].name);
      let fileData = new FormData();
      fileData.append('path', 'newsletter/temp');
      fileData.append('file', input.files[0]);
      fileData.append('name', fileName);
      axios
        .post(process.env.VUE_APP_UPLOAD_FILE_TO_GCS, fileData)
        .then((res) => {
          let send = {
            url: res.data.url,
          };
          this.$emit('show-display', send);
        })
        .catch((error) => {
          console.log('----Error----');
          console.log(error);
        });
    },
  },
};
</script>

<style scoped></style>
