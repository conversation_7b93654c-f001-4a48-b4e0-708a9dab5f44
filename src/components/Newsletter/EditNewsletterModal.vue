<template>
  <div>
    <CButton
      v-if="!section"
      color="dark"
      variant="outline"
      shape="pill"
      @click="show = true"
      >Edit Newsletter</CButton
    >
    <CButton
      v-else
      color="warning"
      variant="outline"
      v-c-tooltip="'Edit Newsletter'"
      @click="show = true"
    >
      <CIcon name="cil-pencil"></CIcon>
    </CButton>
    <CModal title="Edit Newsletter" size="lg" :show.sync="show">
      <CInput
        v-model="newsletter.title"
        label="Newsletter Title"
        invalid-feedback="Newsletter Title is required"
        :is-valid="validator"
      />
      <CTextarea
        v-model="newsletter.description"
        label="Newsletter Description"
      />
      <CInput
        type="date"
        v-model="newsletter.date"
        label="Newsletter Date"
        invalid-feedback="Newsletter Date is required"
        :is-valid="true"
      />
      <CSelect
        :value.sync="newsletter.language"
        :options="languages"
        label="Newsletter Language"
        invalid-feedback="Newsletter Language is required"
        :is-valid="validator"
      />
      <template slot="footer">
        <CButton color="success" variant="outline" @click="updateNewsletter"
          >Edit</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
export default {
  name: 'EditNewsletterModal',
  props: {
    newsletter: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    section: {
      type: Boolean,
    },
  },
  data() {
    return {
      show: false,
      languages: [
        { value: '', label: 'Please Select Newsletter Language' },
        { value: 'en', label: 'English' },
        { value: 'ms', label: 'Malay' },
        { value: 'zh', label: 'Chinese' },
      ],
    };
  },
  methods: {
    validator(val) {
      return val ? val !== '' : false;
    },
    dateValidator(val) {
      let today = new Date().setHours(0, 0, 0, 0);
      let compare = new Date(val).setHours(0, 0, 0, 0);
      return val ? compare >= today : false;
    },
    updateNewsletter() {
      let msg = 'This will edit the newsletter. Continue?';
      if (confirm(msg)) {
        this.$store.dispatch('editNewsletter', this.newsletter);
      }
    },
  },
};
</script>

<style scoped></style>
