<template>
  <div>
    <CRow>
      <CCol>
        <CInput v-model="newsletter.title" label="Newsletter Title" readonly />
        <CTextarea
          v-model="newsletter.description"
          label="Newsletter Description"
          rows="5"
          readonly
        />
      </CCol>
      <CCol>
        <CInput
          v-model="newsletter.unique_id"
          label="Newsletter Unique ID"
          readonly
          @click="openPreview"
          v-c-tooltip="'See Preview'"
        />
        <CInput
          v-model="newsletter.campaign_id"
          label="MailChimp Newsletter Campaign ID"
          readonly
        />
        <CInput
          v-model="newsletter.campaign_web_id"
          label="MailChimp Newsletter Web ID"
          v-c-tooltip="'Open in MailChimp'"
          readonly
          @click="redirectToMailChimp"
        />
      </CCol>
    </CRow>
  </div>
</template>

<script>
export default {
  name: 'NewsletterDetailsCollapse',
  props: {
    newsletter: {
      type: Object,
    },
  },
  methods: {
    redirectToMailChimp() {
      if (this.newsletter.campaign_web_id) {
        window.open(
          'https://us10.admin.mailchimp.com/campaigns/edit?id=' +
            this.newsletter.campaign_web_id,
          '_blank'
        );
      } else {
        alert("MailChimp Campaign hasn't been created");
      }
    },
    openPreview() {
      window.open(
        'https://newswav.com/newsletter/' + this.newsletter.unique_id,
        '_blank'
      );
    },
  },
};
</script>

<style scoped></style>
