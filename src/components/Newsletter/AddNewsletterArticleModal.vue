<template>
  <div>
    <CButton
      v-bind:disabled="article_list.length === 5"
      class="float-right"
      @click="show = true"
      color="success"
      variant="outline"
      shape="pill"
      >Add Article</CButton
    >
    <CModal :show.sync="show" title="Add Article" size="xl">
      <CRow>
        <CCol>
          <BaseNewsletterForm
            :main_article="article"
            @show-display="setDisplay"
          />
        </CCol>
        <CCol>
          <PreviewMainArticle
            :main_article="article"
            :created="article_list"
            :display="display"
          />
        </CCol>
      </CRow>
      <template slot="footer">
        <CButton
          color="success"
          variant="outline"
          @click="addNewsletterArticle"
          v-bind:disabled="article_list.length === 5"
          >Add</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
import BaseNewsletterForm from './BaseNewsletterForm';
import PreviewMainArticle from '../FormWizard/Newsletter/PreviewMainArticle';
import axios from 'axios';
export default {
  name: 'AddNewsletterArticleModal',
  components: { PreviewMainArticle, BaseNewsletterForm },
  props: {
    article_list: {
      type: Array,
      required: true,
    },
    newsletter_id: {
      type: String,
    },
  },
  data() {
    return {
      show: false,
      display: null,
      article: {
        heading: 'Breaking News',
        sequence_order: '',
        title: '',
        description: '',
        article_link: '',
        imageURL: '',
        publisher_name: '',
        article_unique_ID: '',
        newsletter_id: '',
      },
    };
  },
  methods: {
    validateInputs(object) {
      let pass = true;
      Object.keys(object).forEach(function (key, val) {
        if (key !== 'description') {
          if (val === '') {
            pass = false;
            return pass;
          }
        }
      });
      return pass;
    },
    async addNewsletterArticle() {
      this.article.newsletter_id = this.newsletter_id;
      let msg = 'Article will be added to newsletter. Continue?';
      if (confirm(msg)) {
        if (this.display) {
          if (this.display.includes('newsletter/temp/')) {
            await this.moveGoogleFile(this.display);
          }
        }
        if (this.validateInputs(this.article)) {
          await this.$store.dispatch('insertNewsletterArticle', this.article);
        } else {
          alert('Please fill in all the details before proceeding');
        }
      }
    },
    setDisplay(item) {
      this.display = item.url;
    },
    async moveGoogleFile(file) {
      let split = file.split('/');
      let fileName = split[split.length - 1];
      let tempPath = 'newsletter/temp/' + split[split.length - 1];
      let path = 'newsletter/use/';
      let fileData = new FormData();
      fileData.append('tempFile', tempPath);
      fileData.append('newPath', path);
      fileData.append('fileName', fileName);
      let res = await axios.post(
        process.env.VUE_APP_MOVE_FILE_TO_FOLDER,
        fileData
      );
      this.article.image = res.data.url;
    },
  },
};
</script>

<style scoped></style>
