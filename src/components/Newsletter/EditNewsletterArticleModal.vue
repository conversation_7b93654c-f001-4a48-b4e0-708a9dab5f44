<template>
  <div>
    <CButton
      color="warning"
      variant="outline"
      @click="show = true"
      v-c-tooltip="'Edit Article'"
    >
      <CIcon name="cil-pencil"></CIcon>
    </CButton>
    <CModal :show.sync="show" title="Edit Newsletter Article" size="xl">
      <CRow>
        <CCol>
          <BaseNewsletterForm
            :main_article="article"
            @show-display="setDisplay"
          />
        </CCol>
        <CCol>
          <PreviewMainArticle
            :main_article="article"
            :created="[]"
            :display="display"
          />
        </CCol>
      </CRow>
      <template slot="footer">
        <CButton
          color="success"
          variant="outline"
          @click="editNewsletterArticle"
          >Edit</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
import BaseNewsletterForm from './BaseNewsletterForm';
import PreviewMainArticle from '../FormWizard/Newsletter/PreviewMainArticle';
import axios from 'axios';

export default {
  name: 'EditNewsletterArticleModal',
  components: { BaseNewsletterForm, PreviewMainArticle },
  props: {
    article: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      display: null,
      show: false,
    };
  },
  created() {
    this.display = this.article.imageURL;
  },
  methods: {
    setDisplay(item) {
      this.display = item.url;
    },
    async editNewsletterArticle() {
      let msg = 'This will edit the article. Continue?';
      if (confirm(msg)) {
        if (this.display) {
          if (this.display.includes('newsletter/temp/')) {
            await this.moveGoogleFile(this.display);
          }
        }
        await this.$store.dispatch('updateNewsletterArticle', this.article);
      }
    },
    async moveGoogleFile(file) {
      let split = file.split('/');
      let fileName = split[split.length - 1];
      let tempPath = 'newsletter/temp/' + split[split.length - 1];
      let path = 'newsletter/use/';
      let fileData = new FormData();
      fileData.append('tempFile', tempPath);
      fileData.append('newPath', path);
      fileData.append('fileName', fileName);
      let res = await axios.post(
        process.env.VUE_APP_MOVE_FILE_TO_FOLDER,
        fileData
      );
      this.article.image = res.data.url;
    },
  },
};
</script>

<style scoped></style>
