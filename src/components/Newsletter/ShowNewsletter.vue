<template>
  <div>
    <CRow>
      <CCol>
        <CButton color="primary" variant="outline" shape="pill" @click="goBack"
          >Back</CButton
        >
      </CCol>
      <CCol>
        <CButtonToolbar class="float-right">
          <CDropdown
            toggler-text="MailChimp Newsletter"
            color="danger"
            variant="outline"
            shape="pill"
          >
            <CDropdownItem
              @click="createMailChimpCampaign"
              v-text="'Create MailChimp Campaign'"
              v-bind:disabled="
                newsletter.campaign_id || newsletter.campaign_web_id
              "
            />
            <CDropdownItem
              @click="sendMailChimpNewsletter"
              v-text="'Send Newsletter'"
              v-bind:aria-disabled="
                (newsletter.campaign_id || newsletter.campaign_web_id) &&
                newsletter.sent === 1
              "
            />
          </CDropdown>
        </CButtonToolbar>
      </CCol>
    </CRow>
    <br />
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader>
            <CRow>
              <CCol> Newsletter Details </CCol>
              <CCol>
                <CButtonToolbar class="float-right">
                  <EditNewsletterModal :newsletter="newsletter" />
                  <CButton
                    class="float-right"
                    color="info"
                    shape="pill"
                    variant="outline"
                    @click="newsletter_collapse = !newsletter_collapse"
                    >Show Details</CButton
                  >
                </CButtonToolbar>
              </CCol>
            </CRow>
          </CCardHeader>
          <CCardBody>
            <CRow>
              <CCol>
                <CWidgetIcon
                  v-bind:header="sent"
                  text="SENT"
                  color="info"
                  :icon-padding="false"
                >
                  <CIcon name="cil-calendar-check" width="24" />
                </CWidgetIcon>
              </CCol>
              <CCol>
                <CWidgetIcon
                  v-bind:header="language"
                  color="dark"
                  text="Language"
                  :icon-padding="false"
                >
                  <CIcon name="cil-chat-bubble" width="24" />
                </CWidgetIcon>
              </CCol>
              <CCol>
                <CWidgetIcon
                  v-bind:header="newsletter.date"
                  color="warning"
                  text="Newsletter Date"
                  :icon-padding="false"
                >
                  <CIcon name="cil-calendar-check" width="24" />
                </CWidgetIcon>
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CCollapse :show="newsletter_collapse">
                  <CCardBody body-wrapper>
                    <NewsletterDetailsCollapse :newsletter="newsletter" />
                  </CCardBody>
                </CCollapse>
              </CCol>
            </CRow>
          </CCardBody>
        </CCard>
        <CCard>
          <CCardHeader> List of Articles in Newsletter </CCardHeader>
          <CCardBody>
            <CRow>
              <CCol>
                <AddNewsletterArticleModal
                  :article_list="articles"
                  :newsletter_id="this.$route.params.newsletter_id"
                />
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CDataTable
                  :loading="loading"
                  :fields="article_fields"
                  :items="articles"
                  small
                  table-filter
                  sorter
                  pagination
                  items-per-page-select
                  :items-per-page="5"
                >
                  <template #action="{ item }">
                    <td>
                      <CButtonToolbar>
                        <EditNewsletterArticleModal :article="item" />
                        <CButton
                          v-c-tooltip="'Remove Article'"
                          @click="deleteArticle(item)"
                          color="danger"
                          variant="outline"
                        >
                          <CIcon name="cil-trash"></CIcon>
                        </CButton>
                      </CButtonToolbar>
                    </td>
                  </template>
                </CDataTable>
              </CCol>
            </CRow>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import EditNewsletterArticleModal from './EditNewsletterArticleModal';
import AddNewsletterArticleModal from './AddNewsletterArticleModal';
import NewsletterDetailsCollapse from './NewsletterDetailsCollapse';
import EditNewsletterModal from './EditNewsletterModal';

export default {
  name: 'ShowNewsletter',
  components: {
    EditNewsletterModal,
    NewsletterDetailsCollapse,
    AddNewsletterArticleModal,
    EditNewsletterArticleModal,
  },
  data() {
    return {
      article_fields: [
        { key: 'id', label: 'Newsletter Article ID', _style: 'width :40px' },
        { key: 'title', label: 'Article Title', _style: 'width :250px' },
        {
          key: 'description',
          label: 'Article Description',
          _style: 'width :200px',
        },
        {
          key: 'sequence_order',
          label: 'Article Order',
          _style: 'width :80px',
        },
        {
          key: 'action',
          sorter: false,
          label: 'Action',
          _style: 'width :80px',
        },
      ],
      newsletter_collapse: false,
    };
  },
  computed: {
    loading() {
      return this.$store.getters.getNewsletterLoading;
    },
    newsletter() {
      return this.$store.getters.getNewsletter;
    },
    articles() {
      return this.$store.getters.getArticleNewsletterList;
    },
    sent() {
      return this.newsletter.sent === 0 ? 'NO' : 'YES';
    },
    language() {
      return this.newsletter.language === 'en'
        ? 'ENGLISH'
        : this.newsletter.language === 'zh'
          ? 'CHINESE'
          : 'MALAY';
    },
  },
  created() {
    this.$store.dispatch('getOneNewsletter', this.$route.params.newsletter_id);
  },
  methods: {
    goBack() {
      this.$router.push('/newsletter');
    },
    deleteArticle(item) {
      let msg = 'This will remove the article from the newsletter. Continue?';
      if (confirm(msg)) {
        this.$store.dispatch('removeArticleFromNewsletter', item);
      }
    },
    createMailChimpCampaign() {
      let msg = 'This will create a MailChimp Campaign. Continue?';
      if (confirm(msg)) {
        this.$store.dispatch('createChimpCampaign', this.newsletter);
      }
    },
    sendMailChimpNewsletter() {
      let msg = 'This will send the newsletter to our users. Continue?';
      if (confirm(msg)) {
        this.$store.dispatch('sendChimpCampaign', this.newsletter);
      }
    },
  },
};
</script>

<style scoped></style>
