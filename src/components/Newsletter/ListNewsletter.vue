<template>
  <div>
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader> List of Created Newsletters </CCardHeader>
          <CCardBody>
            <CRow>
              <CCol>
                <router-link :to="'newsletter/create-newsletter'">
                  <CButton
                    class="float-right"
                    color="success"
                    variant="outline"
                    shape="pill"
                    >Create Newsletter
                  </CButton>
                </router-link>
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CDataTable
                  :loading="loading"
                  table-filter
                  small
                  :fields="fields"
                  :items="newsletters"
                  pagination
                  sorter
                  items-per-page-select
                  :items-per-page="5"
                >
                  <template #sent="{ item }">
                    <td>
                      <CBadge :color="getSentStatus(item.sent)">{{
                        sent(item.sent)
                      }}</CBadge>
                    </td>
                  </template>
                  <template #action="{ item }">
                    <td>
                      <CButtonToolbar>
                        <CButton
                          @click="openPreview(item)"
                          color="success"
                          variant="outline"
                          v-c-tooltip="'Open Preview'"
                        >
                          <CIcon name="cil-paint"></CIcon>
                        </CButton>
                        <EditNewsletterModal
                          :section="true"
                          :newsletter="item"
                        />
                        <CButton
                          @click="redirectTo(item.id)"
                          color="primary"
                          variant="outline"
                          v-c-tooltip="'Show Newsletter'"
                        >
                          <CIcon name="cil-envelope-open"></CIcon>
                        </CButton>
                      </CButtonToolbar>
                    </td>
                  </template>
                </CDataTable>
              </CCol>
            </CRow>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import EditNewsletterModal from './EditNewsletterModal';
export default {
  name: 'ListNewsletter',
  components: { EditNewsletterModal },
  data() {
    return {
      fields: [
        { key: 'id', label: 'Newsletter ID' },
        { key: 'title', label: 'Newsletter Title' },
        { key: 'date', label: 'Newsletter Date' },
        { key: 'sent', label: 'Is Sent' },
        { key: 'action', sorter: false, label: 'Action' },
      ],
    };
  },
  computed: {
    newsletters() {
      return this.$store.getters.getNewsletterList;
    },
    loading() {
      return this.$store.getters.getNewsletterLoading;
    },
  },
  created() {
    this.$store.dispatch('getNewsletters');
  },
  methods: {
    redirectTo(id) {
      this.$router.push('/newsletter/show-newsletter/' + id);
    },
    getSentStatus(status) {
      return status == 1 ? 'success' : 'danger';
    },
    sent(status) {
      return status == 1 ? 'Yes' : 'No';
    },
    openPreview(item) {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      window.open(
        'https://newswav.com/newsletter/' + item.unique_id + '?r=' + r,
        '_blank'
      );
    },
  },
};
</script>

<style scoped></style>
