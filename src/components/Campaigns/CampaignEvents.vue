<template>
  <div>
    <div v-show="checkAction('campaign_log')">
      <CButton @click="show = true" shape="pill" color="secondary"
        >Campaign Events</CButton
      >
    </div>
    <CModal size="lg" title="Campaign Events" :show.sync="show" centered>
      <CDataTable
        small
        pagination
        :fields="fields"
        :items-per-page="5"
        :items="events"
      >
        <template #status="{ item }">
          <td>
            <CBadge :color="getBadge(item.status)">{{ item.status }}</CBadge>
          </td>
        </template>
      </CDataTable>
      <template slot="footer">
        <CButton variant="outline" color="success" @click="show = !show"
          >Close</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
export default {
  name: 'CampaignEvents',
  props: {
    events: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  data() {
    return {
      show: false,
      fields: [
        { key: 'reason', label: 'Message' },
        { key: 'created_at', label: 'Created At' },
        { key: 'status', label: 'Status' },
      ],
    };
  },
  methods: {
    getBadge(status) {
      return status === 'completed'
        ? 'success'
        : status === 'snoozed'
          ? 'dark'
          : status === 'active'
            ? 'info'
            : 'warning';
    },
  },
};
</script>

<style scoped></style>
