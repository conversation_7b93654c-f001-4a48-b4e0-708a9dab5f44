<template>
  <div>
    <CRow>
      <CCol sm="12" md="6" lg="6">
        <CInput
          v-model="campaign.name"
          label="Name"
          placeholder="Enter campaign name"
          :invalid-feedback="'Campaign name is required'"
          :is-valid="validator"
          :disabled="disableForm"
        />
        <CInput
          v-model="campaign.total_budget"
          label="Budget"
          prepend="RM"
          placeholder="Enter campaign budget"
          invalid-feedback="Budget is required"
          :is-valid="validator"
          :disabled="disableForm"
        />
        <CInput
          v-model="campaign.cpm"
          label="CPM"
          placeholder="Enter campaign CPM"
          prepend="RM"
          invalid-feedback="CPM is required"
          :is-valid="validator"
          :disabled="disableForm"
        />
        <CInput
          v-model="campaign.start_date"
          type="date"
          id="start_date"
          label="Start Date"
          :min="minStartDate"
          :invalid-feedback="startDateErrorMsg"
          :is-valid="startDateValid"
          :disabled="disableForm"
        />
        <CSelect
          v-model="campaign.start_hour"
          :value.sync="campaign.start_hour"
          label="Start Hour"
          :options="time"
          :disabled="disableForm"
        />
        <CInput
          v-model="campaign.end_date"
          type="date"
          id="end_date"
          label="End Date"
          :min="minEndDate"
          :invalid-feedback="endDateErrorMsg"
          :is-valid="endDateValid"
          :disabled="disableForm"
        />
        <CSelect
          :value.sync="campaign.type"
          label="Campaign Type"
          placeholder="Select Campaign Type"
          :options="type"
          invalid-feedback="Type is required"
          :is-valid="validator"
          :disabled="disableForm"
        />
        <CSelect
          v-if="type_edit"
          :value.sync="campaign.ads_type"
          label="Ad Type"
          :options="ads_type"
          invalid-feedback="Ad type is required"
          :is-valid="validator"
          :disabled="disableForm"
        />
        <CSelect
          v-else
          :value.sync="campaign.ads_type"
          label="Ad Type"
          :options="ads_type"
          invalid-feedback="Ad type is required"
          :is-valid="validator"
          readonly
          disabled
        />

        <label for="cmpLoc">Location</label>
        <multiselect
          id="cmpLoc"
          v-model="campaign.targeted_states"
          :options="locations"
          :multiple="true"
          :custom-label="customLabel"
          track-by="id"
          :close-on-select="false"
          @input="onChange"
          :disabled="disableForm"
        >
          <span slot="noResult"
            >Oops! No locations with the name. Consider changing the search
            query.</span
          >
        </multiselect>
        <CRow>
          <CCol>
            Estimated Reach :
            {{ estimate_count_location }}
            Users</CCol
          >
        </CRow>
        <br />
        <label for="cmpTel">Telco Provider</label>
        <multiselect
          id="cmpTel"
          v-model="campaign.targeted_telcos"
          :options="telcos"
          :multiple="true"
          :custom-label="customLabelTelco"
          track-by="id"
          :close-on-select="false"
          @input="onChangeTelco"
          :disabled="disableForm"
        >
          <span slot="noResult">
            Oops! No telco with the name. Consider changing the searchquery.
          </span>
        </multiselect>
        <CRow>
          <CCol> Estimated Reach : {{ estimate_count_telco }} Users </CCol>
        </CRow>
        <div v-if="campaign.ads_type !== 'splash'">
          <br />
          <label for="cmpInt">Feed</label>
          <multiselect
            id="cmpFeed"
            v-model="campaign.targeted_feeds"
            :options="feeds"
            :multiple="true"
            :custom-label="customLabelFeed"
            track-by="id"
            :close-on-select="false"
            @input="onChangeFeed"
            :disabled="disableForm"
          >
            <span slot="noResult">Oops! No feeds with the name.</span>
          </multiselect>
          <span class="invalid-error">{{ feed_error }}</span>
        </div>
      </CCol>

      <CCol sm="12" md="6" lg="6">
        <OverlayLoader v-if="loading || campaignLoading" />
        <CInput
          v-model="campaign.impressions_per_day"
          label="Max Impression / Day"
          placeholder="Enter Max Impression / Day"
          invalid-feedback="Max Impression / Day is required"
          :is-valid="validator"
          :disabled="disableForm"
        />
        <CInput
          v-model="campaign.impressions_per_user"
          label="Impression / User / Campaign"
          placeholder="Enter Impression / User / Campaign"
          invalid-feedback="Impression / User / Campaign is required"
          :is-valid="validator"
          :disabled="disableForm"
        />
        <CInput
          v-model="campaign.clicks_per_user"
          label="Clicks / User / Campaign"
          placeholder="Enter Clicks / User / Campaign"
          invalid-feedback="Clicks / User / Campaign is required"
          :is-valid="validator"
          :disabled="disableForm"
        />
        <CInput
          v-model="campaign.impressions_per_session"
          label="Impression / User / Session"
          placeholder="Enter Impression / User / Session"
          invalid-feedback="Impression / User / Session is required"
          :is-valid="validator"
          :disabled="disableForm"
        />
        <CInput
          v-model="campaign.clicks_per_sessions"
          label="Clicks / User / Session"
          placeholder="Enter Clicks / User / Session"
          invalid-feedback="Clicks / User / Session is required"
          :is-valid="validator"
          :disabled="disableForm"
        />
        <CInput
          v-model="campaign.impressions_per_time_block"
          label="Impression / User / Day"
          placeholder="Enter Impression / User / Day"
          invalid-feedback="Impression / User / Day is required"
          :is-valid="validator"
          :disabled="disableForm"
        />
        <CInput
          v-model="campaign.clicks_per_time_block"
          label="Clicks / User / Day"
          placeholder="Enter Clicks / User / Day"
          invalid-feedback="Clicks / User / Day is required"
          :is-valid="validator"
          :disabled="disableForm"
        />
        <CSelect
          :value.sync="campaign.status"
          label="Status"
          :options="from_edit ? for_edit_status : status"
          :disabled="disableForm"
        />
        <CCol style="padding-left: 0 !important; padding-right: 0 !important">
          <label for="cmpInt">Interest</label>
          <multiselect
            id="cmpInt"
            v-model="campaign.targeted_interests"
            :options="interests"
            :multiple="true"
            :custom-label="customLabelInterest"
            track-by="id"
            :close-on-select="false"
            @input="onChangeInterest"
            :disabled="disableForm"
          >
            <span slot="noResult"
              >Oops! No topics with the name. Consider changing the search
              query.</span
            >
          </multiselect>
          Estimated Reach : {{ estimate_count }} Users <br /><br />
          <label for="cmpInt">Device</label>
          <multiselect
            id="cmpInt"
            v-model="campaign.targeted_devices"
            :options="devices"
            :multiple="true"
            :custom-label="customLabelDevice"
            track-by="id"
            :close-on-select="false"
            @input="onChangeDevice"
            :disabled="disableForm"
          >
            <span slot="noResult"
              >Oops! No device with the name. Consider changing the search
              query.</span
            >
          </multiselect>
          Estimated Reach : {{ estimate_count_device }} Users
        </CCol>
      </CCol>
    </CRow>
  </div>
</template>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>

<script>
import { mapGetters, mapActions } from 'vuex';
import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import Multiselect from 'vue-multiselect';
import OverlayLoader from '../views/OverlayLoader';
import * as headers from '@/helpers/headers';
import { isBefore, parseISO, format } from 'date-fns';
import { prettifyNumbers } from '@/helpers/numbers';
export default {
  name: 'BaseCampaignForm',
  components: { OverlayLoader, Multiselect },
  props: {
    campaign: {
      type: Object,
    },
    from_edit: {
      type: Boolean,
      default: false,
    },
    type_edit: {
      type: Boolean,
      default: true,
    },
    disableForm: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      now: format(new Date(), 'yyyy-MM-dd'),
      for_edit_status: [
        { value: 'active', label: 'Active' },
        { value: 'scheduled', label: 'Scheduled' },
        { value: 'paused', label: 'Paused' },
        { value: 'snoozed', label: 'Snoozed' },
        { value: 'new', label: 'New' },
        { value: 'stopped', label: 'Stopped' },
      ],
      status: [
        { value: 'paused', label: 'Paused' },
        { value: 'active', label: 'Active' },
      ],
      type: [
        { value: '', label: 'Please Select Campaign Type' },
        { value: 'premium', label: 'Premium' },
        { value: 'cheap', label: 'Cheap' },
      ],
      ads_type: [
        { value: '', label: 'Please Select Campaign Ads Type' },
        { value: 'bigcard', label: 'Feed Ad' },
        { value: 'splash', label: 'Splash Ad' },
        { value: 'sponsored', label: 'Sponsored Tab' },
        { value: 'spotlight', label: 'Spotlight Ad' },
      ],
      time: [
        { value: 0, label: '12:00 AM' },
        { value: 1, label: '1:00 AM' },
        { value: 2, label: '2:00 AM' },
        { value: 3, label: '3:00 AM' },
        { value: 4, label: '4:00 AM' },
        { value: 5, label: '5:00 AM' },
        { value: 6, label: '6:00 AM' },
        { value: 7, label: '7:00 AM' },
        { value: 8, label: '8:00 AM' },
        { value: 9, label: '9:00 AM' },
        { value: 10, label: '10:00 AM' },
        { value: 11, label: '11:00 AM' },
        { value: 12, label: '12:00 PM' },
        { value: 13, label: '1:00 PM' },
        { value: 14, label: '2:00 PM' },
        { value: 15, label: '3:00 PM' },
        { value: 16, label: '4:00 PM' },
        { value: 17, label: '5:00 PM' },
        { value: 18, label: '6:00 PM' },
        { value: 19, label: '7:00 PM' },
        { value: 20, label: '8:00 PM' },
        { value: 21, label: '9:00 PM' },
        { value: 22, label: '10:00 PM' },
        { value: 23, label: '11:00 PM' },
      ],
    };
  },
  computed: {
    ...mapGetters({
      loading: 'getLoading',
      campaignLoading: 'getCampaignLoading',
      locations: 'getLocations',
      feeds: 'getFeeds',
      interests: 'getInterests',
      devices: 'getDevices',
      telcos: 'getTelcos',
    }),
    minStartDate() {
      return this.now;
    },
    startDateErrorMsg() {
      if (isBefore(parseISO(this.campaign.start_date), parseISO(this.now))) {
        return "Start Date must be today or AFTER today's date";
      } else {
        return 'Start Date is required';
      }
    },
    startDateValid() {
      if (isBefore(parseISO(this.campaign.start_date), parseISO(this.now))) {
        return !this.from_edit ? false : true;
      } else if (this.campaign.start_date === '') {
        return false;
      } else {
        return true;
      }
    },
    minEndDate() {
      return this.campaign.start_date !== ''
        ? this.campaign.start_date
        : this.now;
    },
    endDateErrorMsg() {
      if (
        isBefore(
          parseISO(this.campaign.end_date),
          parseISO(this.campaign.start_date)
        )
      ) {
        return 'End date must be the same or later than start date';
      } else if (this.campaign.end_date === '') {
        return 'End Date is required';
      } else {
        return '';
      }
    },
    endDateValid() {
      if (
        isBefore(
          parseISO(this.campaign.end_date),
          parseISO(this.campaign.start_date)
        )
      ) {
        return false;
      } else if (this.campaign.end_date === '') {
        return false;
      } else {
        return true;
      }
    },
    ti() {
      return this.campaign.targeted_interests;
    },
    feed_error() {
      return this.campaign.targeted_feeds.length == 0
        ? 'Targeted feed is required'
        : '';
    },
    estimate_count() {
      return this.campaign.estimate_count !== null
        ? prettifyNumbers(this.campaign.estimate_count)
        : '';
    },
    estimate_count_telco() {
      return this.campaign.estimate_count_telco !== null
        ? prettifyNumbers(this.campaign.estimate_count_telco)
        : '';
    },
    estimate_count_location() {
      return this.campaign.estimate_count_location !== null
        ? prettifyNumbers(this.campaign.estimate_count_location)
        : '';
    },
    estimate_count_device() {
      return this.campaign.estimate_count_device !== null
        ? prettifyNumbers(this.campaign.estimate_count_device)
        : '';
    },
    estimateReach() {
      if (this.campaign.targeted_devices != null) {
        if (Object.keys(this.campaign.targeted_devices).length > 0) {
          let loc = this.campaign.targeted_devices;
          let t = 0;
          for (let l in loc) {
            t = t + loc[l].users;
          }
          return t;
        } else {
          return this.totalReach();
        }
      } else {
        return this.totalReach();
      }
    },
    estimateReachInterests() {
      if (this.campaign.targeted_interests != null) {
        if (Object.keys(this.campaign.targeted_interests).length > 0) {
          let int = this.campaign.targeted_interests;
          let t = 0;
          for (let i in int) {
            t = t + int[i].users;
          }
          return t;
        } else {
          return this.totalReachInterests();
        }
      } else {
        return this.totalReachInterests();
      }
    },
    timeValue() {
      return { H: String(this.campaign.start_hour) };
    },
  },
  methods: {
    ...mapActions({
      listLocation: 'listLocations',
      listInterests: 'listInterests',
      listTelcos: 'listTelcos',
      listDevices: 'listDevices',
    }),
    impValidate(val) {
      return val > 3 ? val : false;
    },
    validator(val) {
      return val ? val !== '' : false;
    },
    dateValidator(val) {
      let today = new Date().setHours(0, 0, 0, 0);
      let compare = new Date(val).setHours(0, 0, 0, 0);
      return val ? compare >= today : false;
    },
    customLabel({ location, users }) {
      return `${location} — [${prettifyNumbers(users)}]`;
    },
    customLabelInterest({ name, users }) {
      return `${name} — [${prettifyNumbers(users)}]`;
    },
    customLabelFeed({ name }) {
      return `${name}`;
    },
    customLabelTelco({ name, users }) {
      return `${name} — [${prettifyNumbers(users)}]`;
    },
    customLabelDevice({ name, users }) {
      return `${name} — [${prettifyNumbers(users)}]`;
    },
    totalReach() {
      return this.locations.length > 0 ? this.locations[0].users : 0;
    },
    totalReachInterests() {
      return this.interests.length > 0 ? this.interests[0].users : 0;
    },
    getPeriod() {
      return this.campaign.start_hour < 12 ? 'AM' : 'PM';
    },
    convertHour() {
      return this.campaign.start_hour === 12 || this.campaign.start_hour === 0
        ? 12
        : this.campaign.start_hour % 12;
    },
    changeHandler(eventData) {
      this.campaign.start_hour = eventData.data.H;
    },
    onChange(value) {
      if (value.length > 0) {
        if (value[value.length - 1].id === 0) {
          let data = value[value.length - 1];
          this.campaign.targeted_states = [];
          this.campaign.targeted_states.push(data);
        } else {
          if (value.find((element) => element.id === 0) != null) {
            value = value.filter(function (item) {
              return item.id !== 0;
            });
            this.campaign.targeted_states = [];
            this.campaign.targeted_states = value;
          }
        }
        this.calculateLocations(this.campaign.targeted_states);
      } else {
        this.campaign.estimate_count_location = 0;
      }
    },
    onChangeInterest(value) {
      if (value.length > 0) {
        if (value[value.length - 1].id === 0) {
          let data = value[value.length - 1];
          this.campaign.targeted_interests = [];
          this.campaign.targeted_interests.push(data);
        } else {
          if (value.find((element) => element.id === 0) != null) {
            value = value.filter(function (item) {
              return item.id !== 0;
            });
            this.campaign.targeted_interests = [];
            this.campaign.targeted_interests = value;
          }
        }
        this.calculateInterests(this.campaign.targeted_interests);
      } else {
        this.campaign.estimate_count = 0;
      }
    },
    onChangeFeed(value) {
      if (value.length > 0) {
        if (
          value[value.length - 1].id === '0' ||
          value[value.length - 1].id === 'F_F_ALL'
        ) {
          let data = value[value.length - 1];
          this.campaign.targeted_feeds = [];
          this.campaign.targeted_feeds.push(data);
        } else {
          if (value.find((element) => element.id === '0') != null) {
            value = value.filter(function (item) {
              return item.id !== '0';
            });
            this.campaign.targeted_feeds = [];
            this.campaign.targeted_feeds = value;
          }
          if (value.find((element) => element.id === 'F_F_ALL') != null) {
            value = value.filter(function (item) {
              return item.id !== 'F_F_ALL';
            });
            this.campaign.targeted_feeds = [];
            this.campaign.targeted_feeds = value;
          }
        }
      }
    },
    onChangeTelco(value) {
      if (value.length > 0) {
        if (value[value.length - 1].id === 0) {
          let data = value[value.length - 1];
          this.campaign.targeted_telcos = [];
          this.campaign.targeted_telcos.push(data);
        } else {
          if (value.find((element) => element.id === 0) != null) {
            value = value.filter(function (item) {
              return item.id !== 0;
            });
            this.campaign.targeted_telcos = [];
            this.campaign.targeted_telcos = value;
          }
        }
        this.calculateTelcos(this.campaign.targeted_telcos);
      } else {
        this.campaign.estimate_count_telco = 0;
      }
    },
    onChangeDevice(value) {
      if (value.length > 0) {
        if (value[value.length - 1].id === 0) {
          let data = value[value.length - 1];
          this.campaign.targeted_devices = [];
          this.campaign.targeted_devices.push(data);
        } else {
          if (value.find((element) => element.id === 0) != null) {
            value = value.filter(function (item) {
              return item.id !== 0;
            });
            this.campaign.targeted_devices = [];
            this.campaign.targeted_devices = value;
          }
        }
        this.calculateDevices(this.campaign.targeted_devices);
      } else {
        this.campaign.estimate_count_device = 0;
      }
    },
    calculateInterests(interests = []) {
      this.campaign.estimate_count = 'Calculating';
      if (interests.length > 1) {
        let text = '';
        for (let i = 0, len = interests.length; i < len; i++) {
          text += interests[i].id + ',';
        }
        let send = text.slice(0, -1);
        console.log('send calculate api for topic ' + send);

        axios
          .get(
            process.env.VUE_APP_GET_INTEREST_ESTIMATES_CALCULATE + send,
            headers.createHeaders(this.$store.getters.getUserToken)
          )
          .then((res) => {
            if (res.data == -1) {
              console.log(
                "it's not supposed to even request, check targeted_interests array."
              );
              this.interest_estimate = 0;
            } else {
              this.campaign.estimate_count = res.data[0].users;
            }
          })
          .catch((err) => {
            alert('Something went wrong, check logs');
            console.log(err);
            Bugsnag.notify(err);
          });
      } else {
        if (interests.length == 0) {
          let data = this.interests;

          if (data.length > 0) {
            let value = data[0].users;
            this.campaign.estimate_count = value;
          } else {
            this.campaign.estimate_count = 0;
          }
        } else {
          let index = interests[0].id;
          let data = this.interests;

          if (data.length > 0) {
            let value = data.find((element) => element.id === index);
            this.campaign.estimate_count = value.users;
          } else {
            this.campaign.estimate_count = 0;
          }
        }
      }
    },
    calculateTelcos(telcos = []) {
      this.campaign.estimate_count_telco = 'Calculating';
      if (telcos.length > 1) {
        let text = '';
        for (let i = 0, len = telcos.length; i < len; i++) {
          text += telcos[i].id + ',';
        }
        let send = text.slice(0, -1);
        console.log('send calculate api for telco ' + send);

        axios
          .get(
            process.env.VUE_APP_GET_TELCO_ESTIMATES_CALCULATE + send,
            headers.createHeaders(this.$store.getters.getUserToken)
          )
          .then((res) => {
            if (res.data == -1) {
              console.log(
                "it's not supposed to request, check targeted_telcos array."
              );
              this.telco_estimate = 0;
            } else {
              this.campaign.estimate_count_telco = res.data[0].users;
            }
          })
          .catch((err) => {
            alert('Something went wrong, check logs');
            console.log(err);
            Bugsnag.notify(err);
          });
      } else {
        if (telcos.length == 0) {
          let data = this.telcos;

          if (data.length > 0) {
            let value = data[0].users;
            this.campaign.estimate_count_telco = value;
          } else {
            this.campaign.estimate_count_telco = 0;
          }
        } else {
          let index = telcos[0].id;
          let data = this.telcos;

          if (data.length > 0) {
            let value = data.find((element) => element.id === index);
            this.campaign.estimate_count_telco = value.users;
          } else {
            this.campaign.estimate_count_telco = 0;
          }
        }
      }
    },
    calculateLocations(locations = []) {
      this.campaign.estimate_count_location = 'Calculating';
      if (locations.length > 1) {
        let text = '';
        for (let i = 0, len = locations.length; i < len; i++) {
          text += locations[i].id + ',';
        }
        let send = text.slice(0, -1);
        console.log('send calculate api for telco ' + send);

        axios
          .get(
            process.env.VUE_APP_GET_LOCATION_ESTIMATES_CALCULATE + send,
            headers.createHeaders(this.$store.getters.getUserToken)
          )
          .then((res) => {
            if (res.data == -1) {
              console.log(
                "it's not supposed to request, check targeted_states array."
              );
              this.location_estimate = 0;
            } else {
              this.campaign.estimate_count_location = res.data[0].users;
            }
          })
          .catch((err) => {
            alert('Something went wrong, check logs');
            console.log(err);
            Bugsnag.notify(err);
          });
      } else {
        if (locations.length == 0) {
          let data = this.locations;

          if (data.length > 0) {
            let value = data[0].users;
            this.campaign.estimate_count_location = value;
          } else {
            this.campaign.estimate_count_location = 0;
          }
        } else {
          let index = locations[0].id;
          let data = this.locations;

          if (data.length > 0) {
            let value = data.find((element) => element.id === index);
            this.campaign.estimate_count_location = value.users;
          } else {
            this.campaign.estimate_count_location = 0;
          }
        }
      }
    },
    calculateDevices(devices = []) {
      this.campaign.estimate_count_device = 'Calculating';
      if (devices.length > 1) {
        let text = '';
        for (let i = 0, len = devices.length; i < len; i++) {
          text += devices[i].id + ',';
        }
        let send = text.slice(0, -1);
        console.log('send calculate api for device ' + send);

        axios
          .get(
            process.env.VUE_APP_GET_DEVICE_ESTIMATES_CALCULATE + send,
            headers.createHeaders(this.$store.getters.getUserToken)
          )
          .then((res) => {
            if (res.data == -1) {
              console.log(
                "it's not supposed to request, check targeted_devices array."
              );
              this.device_estimate = 0;
            } else {
              this.campaign.estimate_count_device = res.data[0].users;
            }
          })
          .catch((err) => {
            alert('Something went wrong, check logs');
            console.log(err);
            Bugsnag.notify(err);
          });
      } else {
        if (devices.length == 0) {
          let data = this.devices;

          if (data.length > 0) {
            let value = data[0].users;
            this.campaign.estimate_count_device = value;
          } else {
            this.campaign.estimate_count_device = 0;
          }
        } else {
          let index = devices[0].id;
          let data = this.devices;

          if (data.length > 0) {
            let value = data.find((element) => element.id === index);
            this.campaign.estimate_count_device = value.users;
          } else {
            this.campaign.estimate_count_device = 0;
          }
        }
      }
    },
  },
  async mounted() {
    if (this.locations.length == 0) {
      await this.listLocation();
      if (this.campaign.targeted_states.length == 0) {
        this.campaign.targeted_states.push(this.locations[0]);
        this.campaign.estimate_count_location = this.locations[0].users;
      }
    } else {
      if (this.campaign.targeted_states.length == 0) {
        this.campaign.targeted_states.push(this.locations[0]);
        this.campaign.estimate_count_location = this.locations[0].users;
      }
    }
    if (this.interests.length == 0) {
      await this.listInterests();
      if (this.campaign.targeted_interests.length == 0) {
        this.campaign.targeted_interests.push(this.interests[0]);
        this.campaign.estimate_count = this.interests[0].users;
      }
    } else {
      if (this.campaign.targeted_interests.length == 0) {
        this.campaign.targeted_interests.push(this.interests[0]);
        this.campaign.estimate_count = this.interests[0].users;
      }
    }
    if (this.telcos.length == 0) {
      await this.listTelcos();
      if (this.campaign.targeted_telcos.length == 0) {
        this.campaign.targeted_telcos.push(this.telcos[0]);
        this.campaign.estimate_count_telco = this.telcos[0].users;
      }
    } else {
      if (this.campaign.targeted_telcos.length == 0) {
        this.campaign.targeted_telcos.push(this.telcos[0]);
        this.campaign.estimate_count_telco = this.telcos[0].users;
      }
    }
    if (this.devices.length == 0) {
      await this.listDevices();
      if (this.campaign.targeted_devices.length == 0) {
        this.campaign.targeted_devices.push(this.devices[0]);
        this.campaign.estimate_count_device = this.devices[0].users;
      }
    } else {
      if (this.campaign.targeted_devices.length == 0) {
        this.campaign.targeted_devices.push(this.devices[0]);
        this.campaign.estimate_count_device = this.devices[0].users;
      }
    }
  },
};
</script>

<style scoped>
#startTime {
  margin-bottom: 0.5rem;
}

.invalid-error {
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #e55353;
}
</style>
