<template>
  <div>
    <CButton
      shape="pill"
      variant="outline"
      color="success"
      @click="addModal = true"
      class="float-right"
    >
      Add Campaign
    </CButton>
    <CForm @submit="createCampaign">
      <CModal
        title="Add Campaign"
        color="default"
        size="lg"
        :show.sync="addModal"
        centered
      >
        <CRow>
          <CCol>
            <CRow>
              <CCol>
                <CInput
                  v-model="campaign.name"
                  label="Name"
                  placeholder="Enter campaign name"
                />
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CInput
                  v-model="campaign.total_budget"
                  label="Budget"
                  prepend="RM"
                  placeholder="Enter campaign budget"
                />
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CInput
                  v-model="campaign.cpm"
                  label="CPM"
                  placeholder="Enter campaign CPM"
                  prepend="RM"
                />
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CInput
                  type="date"
                  v-model="campaign.start_date"
                  label="Start Date"
                />
              </CCol>
              <CCol>
                <CInput
                  type="date"
                  v-model="campaign.end_date"
                  label="End Date"
                />
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CSelect
                  :value.sync="campaign.type"
                  label="Campaign Type"
                  placeholder="Select Campaign Type"
                  :options="type"
                />
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CSelect
                  :value.sync="campaign.status"
                  label="Campaign Status"
                  :options="status"
                  readonly
                />
              </CCol>
            </CRow>
          </CCol>
          <CCol>
            <CRow>
              <CCol>
                <CInput
                  v-model="campaign.impressions_per_day"
                  label="Impression / Day"
                  placeholder="Enter Impression / Day"
                />
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CInput
                  v-model="campaign.impressions_per_user"
                  label="Impression / User"
                  placeholder="Enter Impression / User"
                />
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CInput
                  v-model="campaign.clicks_per_user"
                  label="Clicks / User"
                  placeholder="Enter Clicks / User"
                />
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CInput
                  v-model="campaign.impressions_per_session"
                  label="Impression / User / Session"
                  placeholder="Enter Impression / User / Session"
                />
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CInput
                  v-model="campaign.clicks_per_sessions"
                  label="Clicks / User / Session"
                  placeholder="Enter Clicks / User / Session"
                />
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CInput
                  v-model="campaign.impressions_per_time_block"
                  label="Impression / User / Day"
                  placeholder="Enter Impression / User / Day"
                />
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CInput
                  v-model="campaign.clicks_per_time_block"
                  label="Clicks / User / Day"
                  placeholder="Enter Clicks / User / Day"
                />
              </CCol>
            </CRow>
          </CCol>
        </CRow>
        <template slot="footer">
          <CButton color="success" variant="outline" type="submit">
            Add
          </CButton>
        </template>
      </CModal>
    </CForm>
  </div>
</template>

<script>
export default {
  name: 'AddCampaign',
  data() {
    return {
      addModal: false,
      status: [{ value: 'pause', label: 'Paused' }],
      type: [
        { value: 'premium', label: 'Premium' },
        { value: 'cheap', label: 'Cheap' },
      ],
      campaign: {
        name: '',
        description: '',
        advertiser_id: this.$route.params.adv_id,
        start_date: '',
        end_date: '',
        total_budget: '',
        impressions_per_day: '',
        impressions_per_user: '',
        clicks_per_user: '',
        impressions_per_session: '',
        clicks_per_sessions: '',
        type: '',
        status: '',
        impressions_per_time_block: '',
        clicks_per_time_block: '',
        cpm: '',
      },
      minDate: new Date(),
    };
  },
  methods: {
    createCampaign: function (e) {
      if (this.validateValues(this.campaign)) {
        e.preventDefault();
        let msg = 'Campaign will be added. Continue?';
        if (confirm(msg)) {
          let a = JSON.parse(JSON.stringify(this.campaign));
          if (
            this.campaign.targeted_states.find((element) => element.id === 0) !=
            null
          ) {
            a.targeted_states = [];
          }
          if (
            this.campaign.targeted_devices.find(
              (element) => element.id === 0
            ) != null
          ) {
            a.targeted_devices = [];
          }
          if (
            this.campaign.targeted_interests.find(
              (element) => element.id === 0
            ) != null
          ) {
            a.targeted_interests = [];
          }
          if (
            this.campaign.targeted_feeds.find(
              (element) => element.id === '0'
            ) != null
          ) {
            a.targeted_feeds = [];
          }
          if (
            this.campaign.targeted_telcos.find((element) => element.id === 0) !=
            null
          ) {
            a.targeted_telcos = [];
          }
          if (
            this.campaign.targeted_feeds.find(
              (element) => element.name === 'Videos-Videos'
            ) != null
          ) {
            let f = {
              id: 'F_F_-7',
              name: 'Videos-Videos',
            };
            a.targeted_feeds.push(f);
          }
          this.$store.dispatch('addCampaign', a);
          this.$forceUpdate();
        }
      }
    },
    validateValues(object) {
      let pass = true;
      console.log(object);
      let exclude = [
        'subtitle',
        'title',
        'impressions_per_day',
        'targeted_states',
        'targeted_interests',
      ];

      if (object.ads_type != 'spotlight') {
        console.log("it's not spotlight");
        exclude.push('targeted_feeds');
      }

      if (object.type) {
        if (object.type == 'spotlight' || object.type == 'splash') {
          exclude.push('title');
          exclude.push('body');
          exclude.push('icon_url');
          object.body = object.type;
        }
        if (object.type == 'spotlight' || object.type == 'bigcard') {
          exclude.push('splash_media_duration');
          exclude.push('splash_media_no_skip_duration');
          exclude.push('cap_enabled');
        }
      }
      for (const [key, value] of Object.entries(object)) {
        if (exclude.find((element) => element == key) == null) {
          if (key == 'targeted_feeds') {
            if (value.length == 0) {
              console.log(`${key} : ${value}`);
              alert('Check all values or fill in the fields before proceeding');
              pass = false;
              return pass;
            }
          }
          if (value === '') {
            console.log(`${key} : ${value}`);
            alert('Check all values or fill in the fields before proceeding');
            pass = false;
            return pass;
          }
        }
      }
      return pass;
    },
  },
};
</script>

<style scoped></style>
