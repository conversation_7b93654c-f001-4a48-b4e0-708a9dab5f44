<template>
  <div>
    <CRow>
      <CCol>
        <BackButton />
      </CCol>
      <CCol>
        <CButtonToolbar class="float-right">
          <EditCampaignModal
            v-if="!campaignLoading"
            :campaign="campaign"
            :section="false"
          />
          <CampaignEvents :events="events" />
          <CampaignHistory :histories="histories" />
        </CButtonToolbar>
      </CCol>
    </CRow>
    <br />
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader>
            {{ campaign.name }} Details [
            <strong>Campaign ID : {{ campaign.id }}</strong> ]
          </CCardHeader>
          <CCardBody>
            <CRow>
              <CCol>
                <CWidgetIcon
                  :header="progressLoading ? 'Loading...' : campaign.status"
                  text="Status"
                  :color="statusColor(campaign.status)"
                  :icon-padding="false"
                >
                  <CIcon :name="statusIcon(campaign.status)" width="24" />
                </CWidgetIcon>
              </CCol>
              <CCol>
                <CButtonToolbar class="float-right">
                  <CButton
                    @click="campaign_collapse = !campaign_collapse"
                    color="info"
                    variant="outline"
                    shape="pill"
                  >
                    Campaign Details
                  </CButton>
                </CButtonToolbar>
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CCollapse :show="campaign_collapse">
                  <CCardBody body-wrapper>
                    <CampaignDetailsCollapse :campaign="campaign" />
                  </CCardBody>
                </CCollapse>
              </CCol>
            </CRow>
            <CRow>
              <CCol>
                <CRow>
                  <CCol>
                    <CCallout color="warning">
                      <small class="text-muted">Start Date</small><br />
                      <strong class="h4">{{
                        progressLoading ? 'Loading...' : campaign.start_date
                      }}</strong>
                    </CCallout>
                  </CCol>
                  <CCol>
                    <CCallout color="warning">
                      <small class="text-muted">End Date</small><br />
                      <strong class="h4">{{
                        progressLoading ? 'Loading...' : campaign.end_date
                      }}</strong>
                    </CCallout>
                  </CCol>
                </CRow>
                <CRow v-if="campaignProgress">
                  <CCol>
                    <div class="progress-group">
                      <div class="progress-group-header">
                        <CIcon
                          name="cil-calendar-check"
                          class="progress-group-icon"
                        />
                        <span class="title">Campaign Progress</span>
                        <span class="ml-auto font-weight-bold">
                          {{
                            progressLoading ? 'Loading...' : campaignProgressBar
                          }}
                          %</span
                        >
                      </div>
                      <div class="progress-group-bars">
                        <CProgress
                          class="progress-xs"
                          :value="progressLoading ? 0 : campaignProgressBar"
                          color="warning"
                        />
                      </div>
                    </div>
                  </CCol>
                </CRow>
              </CCol>
              <CCol>
                <CRow>
                  <CCol>
                    <CCallout color="success">
                      <small class="text-muted">Budget</small><br />
                      <strong class="h4">{{
                        progressLoading
                          ? 'Loading...'
                          : 'RM ' + campaign.total_budget
                      }}</strong>
                    </CCallout>
                  </CCol>
                  <CCol>
                    <CCallout color="success">
                      <small class="text-muted">CPM</small><br />
                      <strong class="h4">{{
                        progressLoading ? 'Loading...' : 'RM ' + campaign.cpm
                      }}</strong>
                    </CCallout>
                  </CCol>
                </CRow>
                <CRow v-if="campaignProgress">
                  <CCol>
                    <div class="progress-group">
                      <div class="progress-group-header">
                        <CIcon name="cil-money" class="progress-group-icon" />
                        <span class="title">Budget Used</span>
                        <span class="ml-auto font-weight-bold">
                          {{
                            progressLoading
                              ? 'Loading...'
                              : campaignProgress.spent_budget
                          }}
                          <span class="text-muted small"
                            >(
                            {{ progressLoading ? '0' : budgetProgressBar }}
                            %)</span
                          >
                        </span>
                      </div>
                      <div class="progress-group-bars">
                        <CProgress
                          class="progress-xs"
                          :value="progressLoading ? 0 : budgetProgressBar"
                          color="success"
                        />
                      </div>
                    </div>
                  </CCol>
                </CRow>
              </CCol>
            </CRow>
            <CRow v-if="campaignProgress">
              <CCol>
                <CWidgetIcon
                  :header="
                    progressLoading
                      ? 'Loading...'
                      : String(campaignProgress.clicks)
                  "
                  text="Clicks"
                  color="warning"
                  :icon-padding="false"
                >
                  <CIcon
                    name="cil-mouse"
                    class="mx-5"
                    width="24"
                  /> </CWidgetIcon
              ></CCol>
              <CCol>
                <CWidgetIcon
                  :header="
                    progressLoading
                      ? 'Loading...'
                      : String(campaignProgress.views)
                  "
                  text="Impressions"
                  color="success"
                  :icon-padding="false"
                >
                  <CIcon name="cil-people" class="mx-5" width="24" />
                </CWidgetIcon>
              </CCol>
              <CCol>
                <CWidgetIcon
                  v-bind:header="
                    progressLoading
                      ? 'Loading...'
                      : String(
                          (
                            (campaignProgress.clicks / campaignProgress.views) *
                            100
                          ).toFixed(2)
                        ) + `%`
                  "
                  text="CTR"
                  color="success"
                  :icon-padding="false"
                >
                  <CIcon name="cil-people" class="mx-5" width="24" />
                </CWidgetIcon>
              </CCol>
            </CRow>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
    <CRow>
      <CCol>
        <AdvertisementsTable
          :loading="campaignLoading"
          :table_data="ads"
          :table_fields="ads_fields"
          :page="'campaign'"
          :advertiser="{ name: campaign.adv_name }"
        >
          <template #card-title> Ads in this Campaign</template>
          <template #card-add-button>
            <AddAdvertisement
              :adv_name="campaign.adv_name"
              :adv_id="$route.params.adv_id"
              :ads_type="campaign.ads_type"
              :campaign="{ id: campaign.id, name: campaign.name }"
            />
          </template>
        </AdvertisementsTable>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import EditCampaignModal from '@/components/Campaigns/EditCampaignModal';
import AddAdvertisement from '@/components/Advertisements/AddAdvertisement';
import CampaignDetailsCollapse from '@/components/Campaigns/CampaignDetailsCollapse';
import CampaignEvents from '@/components/Campaigns/CampaignEvents';
import CampaignHistory from '@/components/Campaigns/CampaignHistory';
import AdvertisementsTable from '@/components/Advertisements/AdvertisementsTable';
import BackButton from '@/components/common/BackButton.vue';
import {
  CAMPAIGN_STATUS_ICONS,
  CAMPAIGN_STATUS_COLORS,
  CAMPAIGN_ADS_TABLE_FIELDS,
} from '@/constant/constants';
export default {
  name: 'ShowCampaign',
  components: {
    CampaignHistory,
    CampaignEvents,
    CampaignDetailsCollapse,
    AddAdvertisement,
    EditCampaignModal,
    AdvertisementsTable,
    BackButton,
  },
  data() {
    return {
      campaign_collapse: false,
      ads_fields: CAMPAIGN_ADS_TABLE_FIELDS,
    };
  },
  computed: {
    ...mapGetters({
      histories: 'getCampaignHistory',
      events: 'getCampaignEvents',
      campaign: 'getCurrentCampaign',
      ads: 'getAllAdsInCampaign',
      campaignLoading: 'getCampaignLoading',
      campaignProgress: 'getCampaignProgress',
      progressLoading: 'getProgressLoading',
    }),
    campaignProgressBar() {
      let today = new Date();
      let start = new Date(this.campaign.start_date);
      let end = new Date(this.campaign.end_date);
      let result = Math.round(((today - start) / (end - start)) * 100);
      return result > 0 ? (result > 100 ? 100 : result) : 0;
    },
    budgetProgressBar() {
      const spent = this.campaignProgress.spent_budget;
      if (spent > 0) {
        let total = this.campaign.total_budget;
        let current = this.campaignProgress.spent_budget;
        return Math.round((current / total) * 100);
      } else {
        return 0;
      }
    },
  },
  created() {
    this.retrieveCampaign(this.$route.params.c_id);
    this.listLocations();
    this.listInterests();
    this.listFeeds();
  },
  methods: {
    ...mapActions({
      listLocations: 'listLocations',
      listInterests: 'listInterests',
      listFeeds: 'listFeeds',
      retrieveCampaign: 'getAdsProgressCampaign',
    }),
    statusColor(status) {
      return status ? CAMPAIGN_STATUS_COLORS[status] : '';
    },
    statusIcon(status) {
      return status ? CAMPAIGN_STATUS_ICONS[status] : '';
    },
  },
};
</script>
