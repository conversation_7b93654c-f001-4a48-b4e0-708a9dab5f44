<template>
  <div>
    <CRow>
      <CCol>
        <CInput v-model="campaign.type" label="Campaign Type" readonly />
      </CCol>
      <CCol>
        <CInput
          v-model="campaign.impressions_per_day"
          label="Max Impressions / Day"
          readonly
        />
      </CCol>
    </CRow>
    <CRow>
      <CCol>
        <CInput
          v-model="campaign.impressions_per_user"
          label="Impression / User / Campaign"
          readonly
        />
        <CInput
          v-model="campaign.clicks_per_user"
          label="Clicks / User / Campaign"
          readonly
        />
      </CCol>
      <CCol>
        <CInput
          v-model="campaign.impressions_per_session"
          label="Impression / User / Session"
          readonly
        />
        <CInput
          v-model="campaign.clicks_per_sessions"
          label="Clicks / User / Session"
          readonly
        />
      </CCol>
      <CCol>
        <CInput
          v-model="campaign.impressions_per_time_block"
          label="Impression / User / Day"
          readonly
        />
        <CInput
          v-model="campaign.clicks_per_time_block"
          label="Clicks / User / Day"
          readonly
        />
      </CCol>
    </CRow>
    <CRow>
      <CCol>
        <CInput v-model="timeValue" label="Start Time" readonly />
      </CCol>
      <CCol>
        <CInput
          :value.sync="adsType[campaign.ads_type]"
          label="Campaign Ad Type"
          readonly
        />
      </CCol>
    </CRow>
    <CRow>
      <CCol>
        <label for="cmpLoc">Location</label>
        <multiselect
          id="cmpLoc"
          v-model="campaign.targeted_states"
          :options="locations"
          :multiple="true"
          :custom-label="customLabel"
          track-by="id"
          :placeholder="locationPlaceholder()"
          disabled
        >
        </multiselect>
        Estimate Reach :
        {{ estimateReach.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}
        Users
      </CCol>
    </CRow>
    <br />
    <CRow>
      <CCol>
        <label for="cmpInt">Interest</label>
        <multiselect
          id="cmpInt"
          v-model="campaign.targeted_interests"
          :options="interests"
          :multiple="true"
          :custom-label="customLabelInterest"
          track-by="id"
          :placeholder="interestPlaceholder()"
          disabled
        >
        </multiselect>
        Estimate Reach : {{ estimate_count }} Users
      </CCol>
    </CRow>
    <br />
    <CRow v-if="campaign.ads_type !== 'splash'">
      <CCol>
        <label for="cmpFeed">Targeted Feed</label>
        <multiselect
          id="cmpInt"
          v-model="campaign.targeted_feeds"
          :options="feeds"
          :multiple="true"
          :custom-label="customLabelFeed"
          track-by="id"
          :placeholder="locationPlaceholder()"
          disabled
        >
        </multiselect>
      </CCol>
    </CRow>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import Multiselect from 'vue-multiselect';
import { ADS_TYPE } from '@/constant/constants';
export default {
  name: 'CampaignDetailsCollapse',
  props: {
    campaign: {
      type: Object,
      required: true,
    },
  },
  components: { Multiselect },
  data() {
    return {
      adsType: ADS_TYPE,
    };
  },
  computed: {
    ...mapGetters({
      locations: 'getLocations',
      interests: 'getInterests',
      feeds: 'getFeeds',
    }),
    estimate_count: {
      get() {
        if (this.campaign.estimate_count != null) {
          return this.campaign.estimate_count
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }
        return '';
      },
      set() {
        if (this.campaign.estimate_count != null) {
          return this.campaign.estimate_count
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }
        return '';
      },
    },
    estimateReach() {
      if (this.campaign.targeted_states != null) {
        if (Object.keys(this.campaign.targeted_states).length > 0) {
          let loc = this.campaign.targeted_states;
          let t = 0;
          for (let l in loc) {
            t = t + loc[l].users;
          }
          return t;
        } else {
          return this.totalReach();
        }
      } else {
        return this.totalReach();
      }
    },
    estimateReachInterests() {
      if (this.campaign.targeted_interests != null) {
        if (Object.keys(this.campaign.targeted_interests).length > 0) {
          let int = this.campaign.targeted_interests;
          let t = 0;
          for (let i in int) {
            t = t + int[i].users;
          }
          return t;
        } else {
          return this.totalReachInterests();
        }
      } else {
        return this.totalReachInterests();
      }
    },
    timeValue() {
      let period = 'PM';
      if (this.campaign.start_hour < 12) {
        period = 'AM';
      }
      let hour = this.campaign.start_hour % 12;
      if (this.campaign.start_hour === 12 || this.campaign.start_hour === 0) {
        hour = 12;
      }
      return `${hour}:00 ${period}`;
    },
  },
  methods: {
    customLabel({ location, users }) {
      const count = users.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      return `${location} — [${count}]`;
    },
    customLabelInterest({ name, users }) {
      const count = users.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      return `${name} — [${count}]`;
    },
    customLabelFeed({ name }) {
      return `${name}`;
    },
    totalReach() {
      return this.locations.length > 0 ? this.locations[0].users : 0;
    },
    totalReachInterests() {
      return this.interests.length > 0 ? this.interests[0].users : 0;
    },
    locationPlaceholder() {
      if (
        this.campaign.targeted_states != null &&
        Object.keys(this.campaign.targeted_states).length === 0
      ) {
        return 'All Location';
      }
      return '';
    },
    interestPlaceholder() {
      if (
        this.campaign.targeted_interests != null &&
        Object.keys(this.campaign.targeted_interests).length === 0
      ) {
        return 'All Interests';
      }
      return '';
    },
  },
};
</script>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
