<template>
  <div>
    <div v-show="checkAction('campaign_edit')">
      <CButton
        v-if="!section"
        color="warning"
        @click="showModel"
        class="float-right"
        shape="pill"
        >Edit Campaign
      </CButton>
      <CButton
        v-else
        color="warning"
        variant="outline"
        @click="showModel"
        v-c-tooltip="'Edit'"
      >
        <CIcon name="cil-pencil"></CIcon>
      </CButton>
    </div>
    <CModal
      v-if="campaign_data"
      title="Edit Campaign"
      color="default"
      size="lg"
      :show.sync="show"
      @update:show="onClose"
      centered
    >
      <template>
        <BaseCampaignForm
          :campaign="campaign_data"
          :from_edit="true"
          :type_edit="edit_type == null || edit_type == '' ? true : false"
        />
      </template>

      <template slot="footer">
        <CButton
          color="success"
          variant="outline"
          type="button"
          @click="alterCampaign"
          :disabled="checkInvestor"
          >Edit</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import BaseCampaignForm from './BaseCampaignForm';
import { adwavValidateValues } from '@/helpers/field-validate';
export default {
  name: 'EditCampaignModal',
  components: { BaseCampaignForm },
  props: {
    id: {
      type: [String, Number],
      required: false,
    },
    campaign: {
      type: Object,
      required: false,
    },
    section: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      type: [
        { value: 'premium', label: 'Premium' },
        { value: 'cheap', label: 'Cheap' },
      ],
      status: [
        { value: 'active', label: 'Active' },
        { value: 'scheduled', label: 'Scheduled' },
        { value: 'paused', label: 'Paused' },
        { value: 'new', label: 'New' },
        { value: 'stopped', label: 'Stopped' },
      ],
      show: false,
      min: new Date(),
      edit_type: false,
      campaign_data: this.campaign
        ? JSON.parse(JSON.stringify(this.campaign))
        : null,
    };
  },
  computed: {
    ...mapGetters({
      campaignLoading: 'getCampaignLoading',
      locations: 'getLocations',
      interests: 'getInterests',
      devices: 'getDevices',
      telcos: 'getTelcos',
    }),
  },
  methods: {
    ...mapActions({
      getOneCampaign: 'getOneCampaignDetail',
      listLocations: 'listLocations',
      listInterests: 'listInterests',
      listDevices: 'listDevices',
    }),
    showModel() {
      if (!this.campaign && this.id) {
        this.$store
          .dispatch('getOneCampaignDetail', this.id)
          .then((campaign) => {
            this.campaign_data = JSON.parse(JSON.stringify(campaign));
          })
          .catch((error) => {
            console.error('Error fetching campaign details:', error);
          });
      }

      this.show = true;
    },
    alterCampaign() {
      this.$swal
        .fire({
          icon: 'question',
          text: 'Campaign will be updated. Continue?',
        })
        .then((res) => {
          if (res.isConfirmed) {
            if (adwavValidateValues('campaign', this.campaign_data, true)) {
              let updatedCampaign = JSON.parse(
                JSON.stringify(this.campaign_data)
              );
              let a = JSON.parse(JSON.stringify(updatedCampaign));
              if (
                updatedCampaign.targeted_states.find(
                  (element) => element.id === 0
                ) != null
              ) {
                a.targeted_states = [];
              }
              if (
                updatedCampaign.targeted_devices.find(
                  (element) => element.id === 0
                ) != null
              ) {
                a.targeted_devices = [];
              }
              if (
                updatedCampaign.targeted_interests.find(
                  (element) => element.id === 0
                ) != null
              ) {
                a.targeted_interests = [];
              }
              if (
                updatedCampaign.targeted_telcos.find(
                  (element) => element.id === 0
                ) != null
              ) {
                a.targeted_telcos = [];
              }
              if (
                updatedCampaign.targeted_feeds.find(
                  (element) => element.id === '0'
                ) != null
              ) {
                a.targeted_feeds = [];
              }
              if (
                updatedCampaign.targeted_feeds.find(
                  (element) => element.name == 'Videos-Videos'
                ) != null
              ) {
                let f = {
                  id: 'F_F_-7',
                  name: 'Videos1',
                };
                a.targeted_feeds.push(f);
              }
              this.$store
                .dispatch('editCampaign', a)
                .then(() => (this.show = !this.show));
              this.edit_type = JSON.parse(JSON.stringify(a.ads_type ?? ''));
            }
          }
        });
    },
    onClose() {
      if (this.campaign) {
        this.$store
          .dispatch('getOneCampaignDetail', this.campaign.id)
          .then((newCampaign) => {
            this.campaign_data = JSON.parse(JSON.stringify(newCampaign));
          })
          .catch((error) => {
            console.error('Error fetching campaign details:', error);
          });
      }
    },
  },
};
</script>

<style scoped></style>
