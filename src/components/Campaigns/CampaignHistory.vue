<template>
  <div>
    <CButton @click="show = true" shape="pill" color="secondary"
      >Edit History</CButton
    >
    <CModal size="lg" title="Edit History" :show.sync="show" centered>
      <CDataTable
        small
        pagination
        :fields="fields"
        :items-per-page="10"
        :items="transformedHistories"
        table-class="table-bordered"
      >
      </CDataTable>
      <template slot="footer">
        <CButton variant="outline" color="success" @click="show = !show"
          >Close</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
import { CAMPAIGN_FIELD_NAMES } from '@/constant/constants';
export default {
  name: 'CampaignHistories',
  props: {
    histories: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      show: false,
      fields: [
        { key: 'action', label: 'Action' },
        { key: 'created_at', label: 'Date' },
        { key: 'field_name', label: 'Field Name' },
        { key: 'new_value', label: 'Value' },
        { key: 'user_email', label: 'By User' },
      ],
    };
  },
  computed: {
    //addhere
    transformedHistories() {
      return this.histories.map((history) => {
        return {
          ...history,
          field_name: this.transformFieldName(history.field_name),
          new_value: this.transformValue(history.field_name, history.new_value),
        };
      });
    },
  },
  methods: {
    transformFieldName(fieldName) {
      return CAMPAIGN_FIELD_NAMES[fieldName] || fieldName;
    },
    transformValue(field_name, value) {
      switch (field_name) {
        case 'targeted_interests':
          value = value === null || value === '' ? 'All Interests' : value;
          break;
        case 'targeted_states':
          value = value === null || value === '' ? 'All States' : value;
          break;
        case 'targeted_telcos':
          value = value === null || value === '' ? 'All Telcos' : value;
          break;
        case 'targeted_feeds':
          value = value === null || value === '' ? 'All Feeds' : value;
          break;
        case 'targeted_devices':
          value = value === null || value === '' ? 'All Devices' : value;
          break;
        case 'status':
        case 'type':
        case 'ads_type':
          value = value.charAt(0).toUpperCase() + value.slice(1);
          break;
      }
      value = value === null || value === '' ? '[EMPTY]' : value;
      return value;
    },
  },
};
</script>
