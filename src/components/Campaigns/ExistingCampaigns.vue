<template>
  <div>
    <CButton
      class="float-right"
      @click="show = true"
      variant="outline"
      shape="pill"
      color="primary"
      :disabled="toDisable"
      >Choose from Existing Campaign</CButton
    >
    <CModal
      :show.sync="show"
      title="Choose Existing Campaign"
      size="lg"
      centered
    >
      <CDataTable
        :loading="campaignLoading"
        sorter
        table-filter
        pagination
        small
        :fields="fields"
        :items-per-page="5"
        :items="campaigns"
      >
        <template #action="{ item }">
          <td>
            <CButton
              variant="outline"
              shape="pill"
              color="info"
              @click="chooseCampaign(item.id)"
              >Choose</CButton
            >
          </td>
        </template>
      </CDataTable>
      <template slot="footer">
        <span></span>
      </template>
    </CModal>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
export default {
  name: 'ExistingCampaigns',
  props: {
    toDisable: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      show: false,
      fields: [
        { key: 'name', label: 'Campaign Name' },
        { key: 'start_date', label: 'Start Date' },
        { key: 'start_hour', label: 'Start Hour' },
        { key: 'end_date', label: 'End Date' },
        { key: 'total_budget', label: 'Budget (RM)' },
        { key: 'action', label: 'Action' },
      ],
    };
  },
  created() {
    this.listCampaigns(this.$route.params.adv_id);
  },
  computed: {
    ...mapGetters({
      campaigns: 'getAllCampaigns',
      campaign: 'getCampaignDetail',
      campaignLoading: 'getCampaignLoading',
    }),
  },
  watch: {
    campaign(newVal) {
      if (newVal && Object.keys(newVal).length > 0) {
        const send = {
          name: newVal.name + ' COPY',
          advertiser_id: this.$route.params.adv_id,
          start_date: newVal.start_date,
          end_date: newVal.end_date,
          total_budget: newVal.total_budget,
          impressions_per_day: newVal.impressions_per_day,
          impressions_per_user: newVal.impressions_per_user,
          clicks_per_user: newVal.clicks_per_user,
          impressions_per_session: newVal.impressions_per_session,
          clicks_per_sessions: newVal.clicks_per_sessions,
          type: newVal.type,
          status: 'paused',
          locations: newVal.targeted_states,
          interests: newVal.targeted_interests,
          impressions_per_time_block: newVal.impressions_per_time_block,
          clicks_per_time_block: newVal.clicks_per_time_block,
          cpm: newVal.cpm,
          start_hour: newVal.start_hour,
          targeted_interests: newVal.targeted_interests,
          targeted_states: newVal.targeted_states,
          targeted_feeds: newVal.targeted_feeds,
          targeted_telcos: newVal.targeted_telcos,
          targeted_devices: newVal.targeted_devices,
          ads_type: newVal.ads_type,
        };

        this.selected = true;
        this.$emit('selected-campaign', send);
        this.show = false;
      }
    },
  },
  methods: {
    ...mapActions({
      listCampaigns: 'getAllCampaign',
      getOneCampaign: 'getCampaignDetail',
    }),
    chooseCampaign(campaign_id) {
      this.getOneCampaign(campaign_id).catch((error) => {
        console.error('Error fetching campaign details:', error);
      });
    },
  },
};
</script>
