<template>
  <div>
    <div v-show="checkAction('ad_add')">
      <CButton
        @click="show = true"
        color="success"
        shape="pill"
        variant="outline"
        class="mb-3 float-right"
      >
        Add Advertisement
      </CButton>
    </div>
    <CModal
      :show.sync="show"
      size="xl"
      v-bind:title="'Add Advertisement'"
      centered
    >
      <CRow>
        <CCol>
          <ChooseExistingAd :ad_list="ad_list" @choose-existing="chooseAd" />
        </CCol>
      </CRow>
      <br />
      <CRow>
        <CCol>
          <BaseAdvertisementForm
            :selected="selected"
            :adv="adv"
            :advertisement="advert"
            @clear-display="clearDisplays"
            @show-display="showDisplays"
            @prefill-data="setPlaceholder"
            @remove-thumbnail="removeThumbnail"
          />
        </CCol>
        <CCol>
          <AdPreview
            :adv="{ name: adv_name }"
            :advertisement="advert"
            :cover="display_cover"
            :icon="display_icon"
            :thumbnail="display_thumbnail"
          />
        </CCol>
      </CRow>
      <template slot="footer">
        <CButton
          type="button"
          color="success"
          variant="outline"
          @click="createAd"
          :disabled="checkInvestor"
          >Submit</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import Bugsnag from '@bugsnag/js';
import slugify from 'slugify';
import axios from 'axios';
import AdPreview from './AdPreview';
import ChooseExistingAd from './ChooseExistingAd';
import BaseAdvertisementForm from './BaseAdvertisementForm';
import { adwavValidateValues } from '@/helpers/field-validate';

export default {
  name: 'AddAdvertisement',
  components: { BaseAdvertisementForm, ChooseExistingAd, AdPreview },
  props: {
    campaign: {
      type: Object,
      default: () => {
        return {
          id: null,
          name: '',
        };
      },
      required: true,
    },
    adv_name: {
      type: String,
      default: '',
      required: true,
    },
    adv_id: {
      type: String,
      default: '',
      required: true,
    },
    ads_type: {
      type: String,
      default: '',
      required: true,
    },
  },
  data() {
    return {
      selected: false,
      show: false,
      display_icon: null,
      display_cover: null,
      display_thumbnail: null,
      advert: {
        name: '',
        campaign_id: '',
        body: '',
        media_type: '',
        language: [],
        title: '',
        subtitle: '',
        cover_url: '',
        thumbnail_url: '',
        icon_url: '',
        cta: '',
        landing_url: '',
        landing_page_type: 'url',
        lead_form: {},
        type: this.ads_type ?? '',
        advertiser_id: this.adv_id,
        cap_enabled: '',
        splash_media_duration: '',
        splash_media_no_skip_duration: '',
        metadata: {},
        html_ad_type: null,
        ad_media: [],
      },
    };
  },
  computed: {
    ...mapGetters({
      ad_list: 'allAds',
    }),
    adv() {
      return {
        name: this.adv_name,
      };
    },
  },
  watch: {
    ads_type: function (newVal) {
      this.advert.type = newVal;
    },
    campaign: function (newVal) {
      if (newVal) {
        this.advert.name = newVal.name;
        this.advert.campaign_id = newVal.id;
      }
    },
  },
  created() {
    if (this.advert.type === 'sponsored') {
      this.$set(this.advert, 'metadata', {
        banner_background_color: null,
        banner_text_color: null,
        banner_description: null,
      });
    }
    this.listAds(this.$route.params.adv_id);
  },
  methods: {
    ...mapActions({
      listAds: 'getAllAds',
      createAdInCampaign: 'addAdvertInCampaign',
    }),
    resetAdvert() {
      this.advert = {
        name: '',
        campaign_id: '',
        body: '',
        media_type: '',
        language: [],
        title: '',
        subtitle: '',
        cover_url: '',
        thumbnail_url: '',
        icon_url: '',
        cta: '',
        landing_url: '',
        landing_page_type: 'url',
        lead_form: {},
        type: this.ads_type ?? '',
        advertiser_id: this.adv_id,
        cap_enabled: '',
        splash_media_duration: '',
        splash_media_no_skip_duration: '',
        metadata: {},
        html_ad_type: null,
        ad_media: [],
      };

      this.display_cover = null;
      this.display_icon = null;
      this.display_thumbnail = null;
      this.selected = false;
    },
    async createAd() {
      this.$swal
        .fire({
          icon: 'question',
          text: 'Advertisement(s) will be added. Continue?',
        })
        .then(async (res) => {
          if (res.isConfirmed) {
            await this.handleAdvertFileUploads();

            if (adwavValidateValues('ads', this.advert)) {
              await this.createAdInCampaign(this.advert).then(() => {
                this.resetAdvert();
                this.show = !this.show;
              });
            }
          }
        });
    },
    async handleAdvertFileUploads() {
      if (
        this.display_cover &&
        this.display_cover.includes('/temp/') &&
        this.advert.cover_url === ''
      ) {
        this.advert.cover_url = await this.moveGoogleFile(this.display_cover);
      }

      if (
        this.display_thumbnail &&
        this.display_thumbnail.includes('/temp/') &&
        this.advert.thumbnail_url === ''
      ) {
        this.advert.thumbnail_url = await this.moveGoogleFile(
          this.display_thumbnail
        );
      }

      if (this.advert.type !== 'spotlight' && this.advert.type !== 'splash') {
        if (
          this.display_icon &&
          this.display_icon.includes('/temp/') &&
          this.advert.icon_url === ''
        ) {
          this.advert.icon_url = await this.moveGoogleFile(this.display_icon);
        }
      } else {
        this.advert.icon_url = '';
      }

      if (this.advert.type === 'sponsored') {
        this.advert.cover_url = this.advert.icon_url;
        this.advert.cta = this.advert.type;
      }

      // handle html splash ad
      if (this.advert.type === 'splash' && this.advert.media_type === 'web') {
        this.advert.cover_url = '';
        this.advert.landing_url = '';
        this.advert.cta = this.advert.type;

        await Promise.all(
          this.advert.ad_media.map(async (urlData, index) => {
            if (urlData.media_url.includes('/temp/')) {
              let url = await this.moveGoogleFile(urlData.media_url);
              this.advert.cover_url = url;
              this.advert.landing_url = this.advert.ad_media[index].landing_url;
              this.advert.ad_media[index].media_url = url;
            }
          })
        );
      }
    },
    chooseAd(ad) {
      Object.entries(this.advert).forEach(([key]) => {
        if (key === 'language') {
          this.advert[key] = [ad[key]];
        } else {
          this.advert[key] = ad[key];
        }
      });
      // this.advert = Object.assign({}, this.advert, ad);
      this.$set(this.advert, 'campaign_id', this.campaign.id);

      this.display_cover = ad.cover_url;
      this.display_icon = ad.icon_url;
      this.display_thumbnail = ad.thumbnail_url;
      this.selected = true;
    },
    showDisplays(item) {
      if (item.space === 'cover') {
        this.display_cover = item.data;
      } else if (item.space === 'thumbnail') {
        this.display_thumbnail = item.data;
      } else {
        this.display_icon = item.data;
      }
    },
    clearDisplays() {
      this.display_cover = null;
      this.display_icon = null;
      this.display_thumbnail = null;
    },
    // todo refactor to move it to service class
    async moveGoogleFile(file) {
      let split = file.split('/');
      let fileName = split[split.length - 1];
      let tempPath = 'adwav/temp/' + split[split.length - 1];
      let path = 'adwav/' + slugify(this.adv_name.toLowerCase()) + '/';
      let fileData = new FormData();
      fileData.append('tempFile', tempPath);
      fileData.append('newPath', path);
      fileData.append('fileName', fileName);
      try {
        let res = await axios.post(
          process.env.VUE_APP_MOVE_FILE_TO_FOLDER,
          fileData
        );
        return res.data.url;
      } catch (err) {
        Bugsnag.notify(err);
      }
    },
    setPlaceholder(val) {
      this.advert.title = val;
      this.advert.body = val;
      this.advert.subtitle = val;
      this.advert.cta = val;
    },
    removeThumbnail() {
      this.display_thumbnail = null;
      this.advert.thumbnail_url = '';
    },
  },
};
</script>

<style scoped></style>
