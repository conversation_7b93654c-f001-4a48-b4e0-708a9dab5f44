<template>
  <div>
    <CButton
      variant="outline"
      @click="show = true"
      color="primary"
      v-c-tooltip="'Re-Use'"
    >
      <CIcon name="cil-plus"></CIcon>
    </CButton>
    <CModal title="Re-Use Ads" :show.sync="show" size="xl" centered>
      <OverlayLoader v-if="loading" />
      <CDataTable :items="campaigns" :fields="fields">
        <template #action="{ item }">
          <td>
            <input type="checkbox" :value="item.id" v-model="reCampaign" />
          </td>
        </template>
      </CDataTable>
      <template slot="footer">
        <CButton
          type="submit"
          class="float-right"
          color="success"
          variant="outline"
          @click="reUseAds"
        >
          Link</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
import OverlayLoader from '../views/OverlayLoader';
export default {
  name: 'ReUseAdsModal',
  components: { OverlayLoader },
  props: ['campaigns', 'advert_id'],
  data() {
    return {
      show: false,
      reCampaign: [],
      fields: [
        { key: 'action', sorter: false, label: '' },
        { key: 'name', sorter: true, label: 'Name' },
        { key: 'total_budget', sorter: true, label: 'Budget (RM)' },
        { key: 'start_date', sorter: true, label: 'Start Date' },
        { key: 'end_date', sorter: false, label: 'End Date' },
      ],
    };
  },
  computed: {
    loading() {
      return this.$store.getters.getLoading;
    },
  },
  methods: {
    reUseAds(e) {
      e.preventDefault();
      this.$swal
        .fire({
          icon: 'question',
          text: 'Ads will be linked to selected campaign(s). Continue?',
        })
        .then((res) => {
          if (res.isConfirmed) {
            const post = {
              advert_id: this.advert_id,
              campaign_ids: this.reCampaign,
            };
            this.$store.dispatch('reuseAdvertisement', post);
          }
        });
    },
  },
};
</script>

<style scoped></style>
