<template>
  <div>
    <div>
      <OverlayLoader v-if="isCreate === true && loading" />
      <CRow>
        <CCol>
          <CSelect
            @change="clearDisplay"
            :value.sync="advert.type"
            :options="ad_type"
            :disabled="true"
            @update:value="prefillData"
            label="Advertisement Type"
          />
          <CInput
            v-model="advert.name"
            label="Advertisement Name"
            invalid-feedback="Advertisement Name is required"
            :is-valid="validator"
          />
          <CSelect
            v-if="advert.type !== 'sponsored'"
            :value.sync="advert.media_type"
            :options="media_type"
            label="Media Type"
            invalid-feedback="Media Type is required"
            :is-valid="validator"
          />

          <splash-ad-form
            v-if="isAdHtmlSplash === true"
            :selected="selected"
            :advertisement="advert"
            :is-create="isCreate"
            @prefill-data="prefillData"
            @clear-display="clearDisplay"
            @show-display="showDisplay"
          />

          <sponsored-ad-form
            v-if="isAdSponsored === true"
            :selected="selected"
            :advertisement="advert"
            :is-create="isCreate"
            @prefill-data="prefillData"
            @clear-display="clearDisplay"
            @show-display="showDisplay"
          />

          <bigcard-ad-form
            v-if="isAdBigcard === true"
            :advertisement="advert"
            :selected="selected"
            :is-create="isCreate"
            @prefill-data="prefillData"
            @clear-display="clearDisplay"
            @show-display="showDisplay"
          />

          <spotlight-ad-form
            v-if="isAdSpotlight === true"
            :advertisement="advert"
            :selected="selected"
            :is-create="isCreate"
            @prefill-data="prefillData"
            @clear-display="clearDisplay"
            @show-display="showDisplay"
          />
        </CCol>
      </CRow>
    </div>
    <div id="vidPlaceholder" style="display: none"></div>
  </div>
</template>

<script>
import OverlayLoader from '../views/OverlayLoader';
import SplashAdForm from './Forms/SplashAdForm.vue';
import SponsoredAdForm from './Forms/SponsoredAdForm.vue';
import BigcardAdForm from './Forms/BigcardAdForm.vue';
import SpotlightAdForm from './Forms/SpotlightAdForm.vue';

export default {
  name: 'BaseAdvertisementForm',
  components: {
    SpotlightAdForm,
    BigcardAdForm,
    SponsoredAdForm,
    SplashAdForm,
    OverlayLoader,
  },
  props: {
    advertisement: {
      type: Object,
      required: true,
    },
    isCreate: {
      type: Boolean,
      default: true,
    },
    adv: {
      type: Object,
    },
    selected: {
      type: Boolean,
    },
  },
  data() {
    return {
      landing_type: [
        { value: 'url', label: 'Landing URL' },
        { value: 'lead', label: 'Lead Form' },
        { value: 'nw_post', label: 'NW Post' },
      ],
      media_type: [
        { value: '', label: 'Select Media Type' },
        { value: 'image', label: 'Image' },
        { value: 'video', label: 'Video' },
        { value: 'web', label: 'HTML5' },
      ],
      language: [
        { value: 'en', label: 'English' },
        { value: 'ms', label: 'Malay' },
        { value: 'zh', label: 'Chinese' },
      ],
      ad_type: [
        { value: '', label: 'Select Ad Type' },
        { value: 'bigcard', label: 'Feed Ad' },
        { value: 'splash', label: 'Splash Ad' },
        { value: 'sponsored', label: 'Sponsored Tab' },
        { value: 'spotlight', label: 'Spotlight Ad' },
      ],
      cap: [
        { value: '', label: 'Select Ad Cap' },
        { value: 1, label: 'Enabled' },
        { value: 0, label: 'Disabled' },
      ],
    };
  },
  computed: {
    loading() {
      return this.$store.getters.getLoading;
    },
    advert() {
      return this.advertisement;
    },
    isAdSpotlight() {
      return this.advert.type === 'spotlight';
    },
    isAdBigcard() {
      return this.advert.type === 'bigcard';
    },
    isAdSponsored() {
      return this.advert.type === 'sponsored';
    },
    isAdHtmlSplash() {
      return this.advert.type === 'splash';
    },
    forms() {
      let forms = [];
      this.$store.getters.getLeadForms.forEach((item) => {
        let dd = { id: item.id, form_name: item.form_name };
        forms.push(dd);
      });
      forms.sort((a, b) => a.form_name.localeCompare(b.form_name));
      return forms;
    },
  },
  created() {
    if (this.$store.getters.getLeadForms.length === 0) {
      this.$store.dispatch('listLeadForm');
    }
  },
  watch: {
    'advert.type': function (newVal) {
      this.prefillData(newVal);
    },
  },
  methods: {
    validator(val) {
      return val ? val !== '' : false;
    },
    clearDisplay() {
      this.$emit('clear-display');
    },
    prefillData(val) {
      if (val === 'splash') {
        this.$emit('prefill-data', val);
      }
    },
    showDisplay(value) {
      this.$emit('show-display', value);
    },
  },
};
</script>

<style>
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  margin: 0 !important;
}

input[type='number'] {
  -moz-appearance: textfield !important; /* Firefox */
}
</style>
