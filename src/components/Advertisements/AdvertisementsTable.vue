<template>
  <CCard>
    <CCardHeader>
      <slot name="card-title"></slot>
    </CCardHeader>
    <CCardBody>
      <slot name="card-add-button"></slot>
      <CDataTable
        :loading="loading"
        sorter
        table-filter
        small
        :items-per-page="5"
        :items="table_data"
        :fields="table_fields"
        pagination
      >
        <template v-if="loading" #no-items-view>
          <div style="height: 300px"></div>
        </template>
        <template #type="{ item }">
          <td>{{ item.type | ads_type }}</td>
        </template>
        <template #show_details="{ item }">
          <td>
            <CButtonToolbar>
              <EditAdvertisementModal
                v-if="checkAction('ad_edit')"
                :advertisement="item"
                :adv_name="advertiser.name"
              />
              <CButton
                v-if="checkAction('ad_unlink') && page === 'campaign'"
                @click="disconnectAd(item.link_id)"
                variant="outline"
                color="primary"
                v-c-tooltip="'Unlink Ad'"
              >
                <CIcon name="cil-link-broken"></CIcon>
              </CButton>
              <ReUseAdsModal
                v-if="checkAction('ad_reuse') && page === 'advertiser'"
                :campaigns="campaigns"
                :advert_id="item.id"
              />
              <CButton
                v-if="checkAction('ad_export') && item.html_ad_type !== null"
                variant="outline"
                color="success"
                v-c-tooltip="'Export HTML Ad Stat'"
                @click="exportAd(item.id)"
              >
                <CIcon name="cilCloudDownload"></CIcon>
              </CButton>
              <CButton
                v-if="checkAction('ad_delete')"
                variant="outline"
                color="danger"
                v-c-tooltip="'Delete'"
                @click="removeAd(item.id)"
              >
                <CIcon name="cil-trash"></CIcon>
              </CButton>
            </CButtonToolbar>
          </td>
        </template>
      </CDataTable>
    </CCardBody>
  </CCard>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import ReUseAdsModal from '@/components/Advertisements/ReUseAdsModal';
import EditAdvertisementModal from '@/components/Advertisements/EditAdvertisementModal';
import { ADS_TYPE } from '@/constant/constants';
export default {
  name: 'AdvertisementsTable',
  components: { ReUseAdsModal, EditAdvertisementModal },
  props: {
    table_fields: {
      type: Array,
      required: true,
    },
    table_data: {
      type: Array,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
      required: true,
    },
    page: {
      type: String,
      default: '',
      required: true,
    },
    advertiser: {
      type: Object,
      required: true,
    },
  },
  filters: {
    ads_type(val) {
      return ADS_TYPE[val];
    },
  },
  computed: {
    ...mapGetters({
      campaigns: 'getAllCampaigns',
    }),
  },
  methods: {
    ...mapActions({
      detachAd: 'unLinkAd',
      deleteAd: 'removeAdvertisement',
      exportAd: 'exportAd',
    }),
    disconnectAd(id) {
      this.$swal
        .fire({
          icon: 'question',
          text: 'This will unlink selected advertisement to this campaign. Continue?',
        })
        .then((res) => {
          if (res.isConfirmed) {
            this.detachAd(id);
          }
        });
    },
    removeAd(id) {
      this.$swal
        .fire({
          icon: 'question',
          text: 'This will delete the advertisement. Are you sure?',
        })
        .then((res) => {
          if (res.isConfirmed) {
            this.deleteAd(id);
          }
        });
    },
  },
};
</script>
