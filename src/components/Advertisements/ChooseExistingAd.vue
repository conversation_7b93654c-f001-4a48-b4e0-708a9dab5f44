<template>
  <div>
    <CButton
      class="float-right"
      @click="show = true"
      variant="outline"
      shape="pill"
      color="primary"
      >Choose Existing Ad</CButton
    >
    <CModal
      :show.sync="show"
      title="Choose from Existing"
      color="default"
      size="lg"
      centered
    >
      <CDataTable
        sorter
        column-filter
        pagination
        small
        :fields="fields"
        :items-per-page="5"
        :items="ad_list"
      >
        <template #type="{ item }">
          <td>{{ item.type | ads_type }}</td>
        </template>
        <template #action="{ item }">
          <td>
            <CButton
              variant="outline"
              shape="pill"
              color="info"
              @click="chooseAd(item)"
              >Choose</CButton
            >
          </td>
        </template>
      </CDataTable>
      <template slot="footer">
        <span></span>
      </template>
    </CModal>
  </div>
</template>

<script>
import { ADS_TYPE } from '@/constant/constants';
export default {
  name: 'ChooseExistingAd',
  props: ['ad_list'],
  data() {
    return {
      show: false,
      fields: [
        { key: 'type', label: 'Ad Type' },
        { key: 'name', label: 'Ad Name' },
        { key: 'body', label: 'Ad Body' },
        { key: 'title', label: 'Ad Title' },
        { key: 'language', label: 'Ad Language' },
        { key: 'action', sorter: false, label: 'Action' },
      ],
    };
  },
  filters: {
    ads_type(val) {
      return ADS_TYPE[val];
    },
  },
  methods: {
    chooseAd(ad) {
      this.$emit('choose-existing', ad);
      this.show = false;
    },
  },
};
</script>

<style scoped></style>
