<template>
  <div>
    <div class="mb-2">Advertisement Preview</div>
    <div v-if="advertisement.type == 'bigcard'">
      <CCard>
        <OverlayLoader v-if="loading" />
        <CCardHeader>
          <div v-if="display_icon">
            <img id="iconSpot" :src="display_icon" class="rounded-circle" />
            <span style="padding-left: 10px">Advertiser Icon</span>
          </div>
          <div v-else>
            <div class="circle"></div>
          </div>
        </CCardHeader>
        <CCardBody style="padding-top: 0">
          <CRow>
            <CCol>
              <CRow>
                <div id="coverPlace">
                  <div v-if="display_cover">
                    <div class="feedSpot" v-if="video">
                      <video
                        class="feedSpotSize"
                        controls
                        :src="display_cover"
                        ref="videoPlayer"
                        v-show="!showThumbnailFlag"
                        @play="showThumbnail()"
                      ></video>
                      <img
                        class="feedSpotSize"
                        :src="display_thumbnail"
                        v-if="showThumbnailFlag"
                      />
                    </div>

                    <div v-else class="feedSpot">
                      <img class="feedSpotSize" :src="display_cover" />
                    </div>
                  </div>
                  <div v-else>
                    <div id="square"></div>
                  </div>
                </div>
              </CRow>
            </CCol>
          </CRow>
          <CRow>
            <CCol>
              <CRow>
                <CCol>
                  <h3
                    class="mt-2 mb-3"
                    v-text="
                      advertisement.body
                        ? advertisement.body
                        : 'This is where Advertisement Title go'
                    "
                  ></h3>
                </CCol>
              </CRow>
              <CRow>
                <CCol class="col-sm-8">
                  <div
                    class="mb-2 pb-3"
                    v-text="
                      advertisement.title
                        ? advertisement.title
                        : 'This is where the Advertisement Description go'
                    "
                  ></div>
                  <!-- <div class="pt-1 pb-3"
                       v-text="advertisement.subtitle ? advertisement.subtitle : 'This is where the Advertisement Sub-description go'">
                    this is the ad subtitle
                  </div> -->
                </CCol>
                <CCol class="col-sm-4">
                  <div class="mt-0">
                    <CButton
                      color="danger"
                      class="float-right"
                      v-text="
                        advertisement.cta ? advertisement.cta : 'Button Text'
                      "
                    ></CButton>
                  </div>
                </CCol>
              </CRow>
            </CCol>
          </CRow>
        </CCardBody>
      </CCard>
    </div>
    <div v-if="advertisement.type == 'splash'">
      <CCard id="splash">
        <OverlayLoader v-if="loading" />
        <CCardBody>
          <div v-if="display_cover" class="text-center">
            <video
              v-if="video"
              class="splashSpot"
              controls
              :src="display_cover"
              ref="videoPlayer"
              v-show="!showThumbnailFlag"
              @play="showThumbnail()"
            ></video>
            <img
              class="splashSpot"
              :src="display_thumbnail"
              v-if="showThumbnailFlag"
            />
            <!-- Might be working if cors is disabled or all the link is from newswav // alot of sync issue -->
            <!-- <div v-else-if="web && display_cover !== null" class="splashSpot">
              <iframe :src="display_cover" title="Ad Preview" frameborder="0" marginwidth="0" marginheight="0" scrolling="auto" allowtransparency="false">
              </iframe> 
            </div> -->
            <img v-else class="splashSpot" :src="display_cover" />
          </div>
          <div v-else-if="web" class="splashSpot">
            <p>Sorry, There is no preview for HTML Splash Ad currently.</p>
          </div>
          <div v-else>
            <div id="splashBox"></div>
          </div>
        </CCardBody>
      </CCard>
    </div>
    <div v-if="advertisement.type == 'spotlight'">
      <CCard id="spotlight">
        <OverlayLoader v-if="loading" />
        <CCardBody>
          <div v-if="display_cover">
            <div v-if="video" class="spotlightSpot">
              <video
                class="spotlightVideo"
                controls
                :src="display_cover"
                ref="videoPlayer"
                v-show="!showThumbnailFlag"
                @play="loopThumbnail()"
              ></video>
              <img
                class="spotlightVideo"
                :src="display_thumbnail"
                v-if="showThumbnailFlag"
              />
              <div class="spotlightButton float-right">
                <CButton
                  color="light"
                  class="spotlightButtonVideoStyle float-right"
                  v-text="advertisement.cta ? advertisement.cta : 'Button Text'"
                ></CButton>
              </div>
            </div>
            <div v-else class="spotlightSpot">
              <img class="spotlightSpotSize" :src="display_cover" />
              <div class="spotlightButton">
                <CButton
                  color="light"
                  class="spotlightButtonStyle float-right"
                  v-text="advertisement.cta ? advertisement.cta : 'Button Text'"
                ></CButton>
              </div>
            </div>
          </div>
          <div v-else>
            <div id="spotlightSpot" class="spotlightSpot">
              <div></div>
            </div>
          </div>
        </CCardBody>
      </CCard>
    </div>
    <div v-if="advertisement.type == 'sponsored'">
      <div
        class="d-flex flex-row flex-wrap align-items-center sponsor-banner"
        :style="{ backgroundColor: `#${banner_background_color}` }"
      >
        <div v-if="display_icon" style="margin-right: 10px; margin-left: 16px">
          <img
            :src="display_icon"
            class="rounded-circle"
            height="30"
            width="30"
          />
        </div>
        <div v-else>
          <div class="sponsor-suedo-logo"></div>
        </div>
        <div
          :style="{ color: `#${banner_text_color}`, fontFamily: 'SF UI Text' }"
          v-text="banner_description"
        >
          <!-- Brought to you by <span style="font-weight: bold"> {{adv.name}}  -->
          <!-- </span> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import OverlayLoader from '../views/OverlayLoader';

export default {
  name: 'AdPreview',
  components: { OverlayLoader },
  props: ['advertisement', 'cover', 'thumbnail', 'icon', 'adv'],
  data() {
    return {
      showThumbnailFlag: false, // initialize as false
    };
  },
  computed: {
    loading() {
      return this.$store.getters.getLoading;
    },
    video() {
      return this.display_cover.toLowerCase().includes('.mp4');
    },
    web() {
      return this.advertisement.media_type == 'web';
    },
    display_cover() {
      return this.cover;
    },
    display_thumbnail() {
      return this.thumbnail;
    },
    display_icon() {
      return this.icon;
    },
    banner_background_color() {
      return this.advertisement.metadata.banner_background_color || 'F9DC5C';
    },
    banner_text_color() {
      return this.advertisement.metadata.banner_text_color || '000000';
    },
    banner_description() {
      return (
        this.advertisement.metadata.banner_description ||
        'Brought to you by ' + this.adv.name
      );
    },
  },
  methods: {
    // Method to show thumbnail
    showThumbnail() {
      if (this.thumbnail) {
        this.showThumbnailFlag = true; // Sets showThumbnailFlag to be true, shows thumbnail instead of video
        setTimeout(() => {
          this.showThumbnailFlag = false;
          this.$refs.videoPlayer.currentTime = 0;
          this.$refs.videoPlayer.play();
        }, 2000); // Simulated loading time of 2 seconds
      } else {
        return;
      }
    },
    // Method for loops
    loopThumbnail() {
      if (this.thumbnail) {
        this.showThumbnail();
        this.$refs.videoPlayer.onended = () => {
          this.showThumbnail();
        };
      } else return;
    },
  },
};
</script>

<style scoped>
/* @import url('//fonts.cdnfonts.com/css/sf-ui-text-2'); */

.card-header {
  border-bottom: none;
}
.sponsor-banner {
  max-width: 360px;
  height: 47px;
}
.sponsor-suedo-logo {
  background: lightblue;
  height: 30px;
  width: 30px;
  border-radius: 50%;
  margin-right: 10px;
  margin-left: 16px;
}
#coverPlace {
  padding: 0 10px 0 10px;
  width: 100%;
}

#iconSpot {
  width: 40px;
  height: 40px;
}

.coverSpot {
  height: 310px;
  width: 100%;
}

.circle {
  background: lightblue;
  border-radius: 50%;
  width: 40px;
  height: 40px;
}

#square {
  padding: 10px;
  background: lightblue;
  width: 100%;
  height: 310px;
}

#spotlightBox {
  height: 400px;
  width: 100%;
}

.splashSpot {
  height: 700px;
  width: 400px;
  margin-left: 50px;
  margin-right: 50px;
  object-fit: cover;
}

.spotlightSpot {
  max-height: 100%;
  max-width: 100%;
  background-color: white;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
  position: relative;
}

.feedSpot {
  max-height: 100%;
  max-width: 100%;
  background-color: red;
  width: 100%;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
  position: relative;
  margin-top: 10px;
}

.feedSpotSize {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  object-fit: cover; /* Ensure thumbnail image covers the video area */
}

.spotlightVideo {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  object-fit: cover; /* Ensure thumbnail image covers the video area */
}

.spotlightButton {
  position: absolute;
  width: 100%;
  height: 10%;
  z-index: 2;
  top: 2;
}

.spotlightButtonStyle {
  position: relative;
  right: 15px;
  top: -48px;
  color: #ec3535;
  background-color: #ffffff;
}

.spotlightSpot:hover .spotlightButtonVideoStyle {
  display: none;
}

.spotlightButtonVideoStyle {
  position: relative;
  right: 15px;
  top: -48px;
  color: #ec3535;
  background-color: #ffffff;
}

#splash {
  height: 750px;
  width: 100%;
}

#splashBox {
  height: 750px;
  width: 100%;
}

.spotlightSpotSize {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

div iframe {
  width: 100%;
  height: 100%;
  position: absolute;
  display: block;
  top: 0;
  left: 0;
}
</style>
