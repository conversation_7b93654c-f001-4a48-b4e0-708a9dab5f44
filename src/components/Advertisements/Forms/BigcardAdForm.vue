<template>
  <div class="bigcard-ad-form">
    <CInput
      v-model="advert.body"
      label="Advertisement Title"
      invalid-feedback="Advertisement Body is required"
      :description="titleLength"
      :is-valid="validator"
    />
    <CInput
      v-model="advert.title"
      label="Advertisement Description"
      :description="descriptionLength"
      invalid-feedback="Advertisement Description is required"
      :is-valid="validator"
    />
    <CInput
      v-model="advert.cta"
      label="Advertisement CTA"
      invalid-feedback="Advertisement CTA is required"
      :description="ctaLength"
      :is-valid="validator"
    />

    <landing-page-form :advertisement="advert" />

    <advertisement-media-form
      :advertisement="advert"
      :selected="selected"
      :show-logo-input="true"
      :show-existing-web-link="true"
      @show-display="showDisplay"
      @remove-thumbnail="removeThumbnail"
    />

    <language-form :advertisement="advert" :is-create="isCreate" />
  </div>
</template>
<script>
import LandingPageForm from './LandingPageForm.vue';
import LanguageForm from './LanguageForm.vue';
import AdvertisementMediaForm from './AdvertisementMediaForm.vue';

export default {
  name: 'BigcardAdForm',
  components: {
    AdvertisementMediaForm,
    LanguageForm,
    LandingPageForm,
  },
  props: {
    advertisement: {
      type: Object,
      required: true,
    },
    selected: {
      type: Boolean,
      required: true,
    },
    isCreate: {
      type: Boolean,
      required: false,
      default: true,
    },
  },
  data() {
    return {};
  },
  computed: {
    advert() {
      return this.advertisement;
    },
    titleLength() {
      return `${this.advert.body.length} characters`;
    },
    descriptionLength() {
      return `${this.advert.title.length} characters`;
    },
    ctaLength() {
      return `${this.advert.cta.length} characters`;
    },
  },
  methods: {
    validator(val) {
      return val ? val !== '' : false;
    },
    showDisplay(value) {
      this.$emit('show-display', value);
    },
    removeThumbnail() {
      this.$emit('remove-thumbnail');
    },
  },
};
</script>
