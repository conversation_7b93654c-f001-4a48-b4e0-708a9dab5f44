<template>
  <div class="sponsored-ad-form">
    <CInput
      v-model="advert.metadata.banner_background_color"
      label="Background Color"
      invalid-feedback="Invalid hex color"
      :is-valid="hexValidator"
      placeholder="F9DC5C (Default)"
    />
    <CInput
      v-model="advert.metadata.banner_text_color"
      label="Text Color"
      invalid-feedback="Invalid hex color"
      :is-valid="hexValidator"
      placeholder="FFFFFF (Default)"
    />
    <CInput
      v-model="advert.metadata.banner_description"
      label="Advertisement Description"
      :description="bannerDescLength"
      :is-valid="bannerDescValidator"
      invalid-feedback="Current Limit is 160 characters"
      :placeholder="'Brought to you by ' + [[advert.name]] + '(Default)'"
    />

    <landing-page-form :advertisement="advert" :show-logo-input="true" />

    <advertisement-media-form
      :advertisement="advert"
      :selected="selected"
      :show-logo-input="true"
      :show-existing-web-link="true"
      @show-display="showDisplay"
      @remove-thumbnail="removeThumbnail"
    />

    <language-form :advertisement="advert" :is-create="isCreate" />
  </div>
</template>
<script>
import LandingPageForm from './LandingPageForm.vue';
import LanguageForm from './LanguageForm.vue';
import AdvertisementMediaForm from './AdvertisementMediaForm.vue';

export default {
  name: 'SponsoredAdForm',
  components: { AdvertisementMediaForm, LanguageForm, LandingPageForm },
  props: {
    advertisement: {
      type: Object,
      required: true,
    },
    selected: {
      type: Boolean,
      required: true,
    },
    isCreate: {
      type: Boolean,
      required: false,
      default: true,
    },
  },
  data() {
    return {};
  },
  computed: {
    advert() {
      return this.advertisement;
    },
    bannerDescLength() {
      let Desclength = this.advert.metadata.banner_description?.length || '0';
      return `${Desclength} characters`;
    },
    bannerDescValidator(val) {
      return !val || val?.length < 161;
    },
  },
  methods: {
    hexValidator(val) {
      if (!val || val?.length === 0) return true;
      return (
        typeof val === 'string' &&
        val.length === 6 &&
        !isNaN(Number('0x' + val))
      );
    },
    showDisplay(value) {
      this.$emit('show-display', value);
    },
    removeThumbnail() {
      this.$emit('remove-thumbnail');
    },
  },
};
</script>
