<template>
  <div class="advertisement-thumbnail-form">
    <label for="adCoverFile1">Advertisement Media File</label>
    <div>
      <label class="custom-file-upload">
        <input
          type="file"
          @change="onFileChange($event, 'cover')"
          id="adCoverFile1"
          ref="inputCover"
          class="form-control-file"
          data-content="cover"
          accept="video/mp4,image/*"
        />
        <i class="fa fa-cloud-upload"></i> Upload
      </label>
    </div>
    <br />

    <div
      style="margin-top: 10px"
      v-if="isCreate === false && showExistingWebLink === true"
    >
      <CInput
        v-if="advert.media_type === 'web'"
        label="Existing Web Link"
        v-model="advert.cover_url"
        invalid-feedback="Web URL must start with http:// or https://"
        :is-valid="urlValidator"
      />
      <CInput
        v-else
        disabled
        label="Existing Media File/URL"
        v-model="advert.cover_url"
      />
    </div>

    <template v-if="advert.media_type === 'video'">
      <label for="adThumbnailFile1">Advertisement Thumbnail File</label>
      <div>
        <label class="custom-file-upload">
          <input
            type="file"
            @change="onFileChange($event, 'thumbnail')"
            id="adThumbnailFile1"
            ref="inputThumbnail"
            class="form-control-file"
            data-content="thumbnail"
            accept=".png, .jpg, .jpeg"
          />
          <i class="fa fa-cloud-upload"></i> Upload
        </label>
        <i
          class="fa fa-times"
          style="margin-left: 10px; cursor: pointer"
          @click="removeThumbnail()"
        ></i>
        <span style="color: red; font-size: 12px; margin-left: 10px"
          >*optional</span
        >
      </div>
      <br />
      <CInput
        v-if="selected"
        v-model="advert.thumbnail_url"
        label="Thumbnail URL"
        readonly
      />
    </template>

    <div v-show="showLogoInput === true">
      <label for="adIconFile1">Advertisement Logo File</label>
      <div>
        <label class="custom-file-upload">
          <input
            type="file"
            id="adIconFile1"
            @change="onFileChange($event, 'icon')"
            ref="inputIcon"
            class="form-control-file"
            data-content="icon"
            accept=".png, .jpg, .jpeg"
          />
          <i class="fa fa-cloud-upload"></i> Upload
        </label>
      </div>
      <br />
      <CInput
        v-if="selected"
        v-model="advert.icon_url"
        label="Logo URL"
        readonly
      />
    </div>
  </div>
</template>
<script>
import Bugsnag from '@bugsnag/js';
import slugify from 'slugify';
import $ from 'jquery';
import axios from 'axios';
import { IMAGE_TYPE } from '@/constant/constants';
import { validateMediaTypeWithFileType } from '@/helpers/field-validate';

export default {
  name: 'AdvertisementMediaForm',
  props: {
    advertisement: {
      type: Object,
      required: true,
    },
    selected: {
      type: Boolean,
      required: true,
    },
    showLogoInput: {
      type: Boolean,
      required: false,
      default: false,
    },
    showExistingWebLink: {
      type: Boolean,
      required: false,
      default: true,
    },
    isCreate: {
      type: Boolean,
      required: false,
      default: true,
    },
  },
  data() {
    return {};
  },
  computed: {
    advert() {
      return this.advertisement;
    },
  },
  methods: {
    // todo refactor to service class
    // compress video files depends test first
    onFileChange(event, type) {
      let mediaType =
        type === 'icon' || type === 'thumbnail'
          ? IMAGE_TYPE
          : this.advertisement.media_type;
      let input = event.target;
      if (input.files && input.files[0]) {
        if (validateMediaTypeWithFileType(mediaType, input.files[0])) {
          let iSize = input.files[0].size / 1000 / 1000;
          console.log(`${iSize} - file size`);
          if (type === 'cover') {
            if (input.files[0].name.split('.').pop().toLowerCase() === 'mp4') {
              if (iSize <= 10) {
                this.getVideoDimension(input);
              } else {
                //display warning
                alert('File size exceeded. Max 10mb for Video');
                console.log('file too big');
                this.$refs.inputCover.value = null;
              }
            } else {
              if (iSize <= 1) {
                this.getCoverDimension(input);
                //this.googleUpload(input);
              } else {
                //display warning
                alert('File size exceeded. Max 1mb for Image');
                console.log('file too big');
                this.$refs.inputCover.value = null;
              }
            }
          } else if (type === 'thumbnail') {
            // If onFileChange($event, "thumbnail") is called,
            if (iSize <= 5) {
              // If file size does not exceed 5 mb
              this.getThumbnailDimension(input);
            } else {
              // Display warning and reset thumbnail value
              alert('File size exceeded. Max 5Mb');
              console.log('file too big');
              this.$refs.inputThumbnail.value = null;
            }
          } else {
            if (iSize <= 0.5) {
              this.getIconDimension(input);
              //this.googleUpload(input);
            } else {
              //display warning
              alert('File size exceeded. Max 500kb');
              console.log('file too big');
              this.$refs.inputIcon.value = null;
            }
          }
        } else {
          alert('Please select correct media type');
        }
      }
    },
    getVideoDimension(input) {
      var file = input.files[0];
      var reader = new FileReader();
      var self = this;
      reader.addEventListener(
        'load',
        function () {
          var self2 = self;
          var dataUrl = reader.result;
          var videoId = 'videoMain';
          var $videoEl = $('<video id="' + videoId + '"></video>');
          $('#vidPlaceholder').append($videoEl);
          $videoEl.attr('src', dataUrl);
          var videoTagRef = $videoEl[0];
          videoTagRef.addEventListener(
            'loadedmetadata',
            function () {
              let w = videoTagRef.videoWidth;
              let h = videoTagRef.videoHeight;
              let r = self2.gcd(w, h);
              let error, a1, a2;
              if (self2.advert.type === 'splash') {
                error = 'Media must be in 9:16 ratio';
                a2 = w / r;
                a1 = h / r;
              } else {
                error = 'Media must be in 16:9 ratio';
                a1 = w / r;
                a2 = h / r;
              }
              console.log(`${a1} : ${a2}`);
              if (self2.advert.type === 'bigcard' && w === 1200 && h === 620) {
                console.log('SPECIAL REQUIREMENT!');
                self2.googleUpload(input);
              } else {
                if (a1 === 16 && a2 === 9) {
                  self2.googleUpload(input);
                } else {
                  alert(error);

                  self2.$refs.inputCover.value = null;
                }
              }
            }.bind(input)
          );
        }.bind(input),
        false
      );

      if (file) {
        reader.readAsDataURL(file);
      }
    },
    getCoverDimension(input) {
      let file = input.files[0];
      if (!file || file.type.indexOf('image/') !== 0) {
        alert('image not found');
      } else {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (evt) => {
          let img = new Image();
          img.onload = () => {
            let w = img.width;
            let h = img.height;
            let r = this.gcd(w, h);
            let error, a1, a2;
            if (this.advert.type === 'splash') {
              error = 'Media must be in 9:16 ratio';
              a2 = w / r;
              a1 = h / r;
            } else {
              error = 'Media must be in 16:9 ratio';
              a1 = w / r;
              a2 = h / r;
            }
            console.log(`${a1} : ${a2}`);
            if (this.advert.type === 'bigcard' && w === 1200 && h === 620) {
              console.log('SPECIAL REQUIREMENT!');
              this.googleUpload(input);
            } else {
              if (a1 === 16 && a2 === 9) {
                this.googleUpload(input);
              } else {
                alert(error);

                this.$refs.inputCover.value = null;
              }
            }
          };
          img.src = evt.target.result;
        };
        reader.onerror = (evt) => {
          console.error(evt);
        };
      }
    },
    getThumbnailDimension(input) {
      let file = input.files[0];
      if (!file || file.type.indexOf('image/') !== 0) {
        // If not an image filetype
        alert('image not found');
      } else {
        // Check thumbnail dimension to ensure its correct (16:9 or 9:16), if true call googleUpload
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (evt) => {
          let img = new Image();
          img.onload = () => {
            let w = img.width;
            let h = img.height;
            let r = this.gcd(w, h);
            let error;
            let a1, a2;
            if (this.advert.type === 'splash') {
              error = 'Thumbnail must be in 9:16 ratio';
              a2 = w / r;
              a1 = h / r;
            } else {
              error = 'Thumbnail must be in 16:9 ratio';
              a1 = w / r;
              a2 = h / r;
            }
            console.log(`${a1} : ${a2}`);
            if (this.advert.type === 'bigcard' && w === 1200 && h === 620) {
              console.log('SPECIAL REQUIREMENT!');
              this.googleUpload(input);
            } else {
              if (a1 === 16 && a2 === 9) {
                this.googleUpload(input);
              } else {
                alert(error);
                this.$refs.inputThumbnail.value = null;
              }
            }
          };
          img.src = evt.target.result;
        };
        reader.onerror = (evt) => {
          console.error(evt);
        };
      }
    },
    getIconDimension(input) {
      let file = input.files[0];
      if (!file || file.type.indexOf('image/') !== 0) {
        alert('image not found');
      } else {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (evt) => {
          let img = new Image();
          img.onload = () => {
            let w = img.width;
            let h = img.height;
            let r = this.gcd(w, h);

            var error = 'Logo must be in 1:1 ratio';
            var a1 = w / r;
            var a2 = h / r;
            console.log(`${a1} : ${a2}`);
            if (a1 === 1 && a2 === 1) {
              this.googleUpload(input);
            } else {
              alert(error);
              this.$refs.inputIcon.value = null;
            }
          };
          img.src = evt.target.result;
        };
        reader.onerror = (evt) => {
          console.error(evt);
        };
      }
    },
    googleUpload: function (input) {
      const name = input.files[0].name;
      const lastDot = name.lastIndexOf('.');
      const nameOnly = name.substring(0, lastDot);
      const ext = name.substring(lastDot + 1);
      let fileName =
        slugify(`${nameOnly} ${this.generatePostfix()}`) + `.${ext}`;
      let fileData = new FormData();
      fileData.append('path', 'adwav/temp');
      fileData.append('file', input.files[0]);
      fileData.append('name', fileName);
      this.$store.commit('SET_GENERAL_LOADING', true);
      axios
        .post(process.env.VUE_APP_UPLOAD_FILE_TO_GCS, fileData)
        .then((res) => {
          if (input.getAttribute('data-content') === 'cover') {
            let send = {
              data: res.data.url,
              space: 'cover',
            };
            this.$emit('show-display', send);
          } else if (input.getAttribute('data-content') === 'thumbnail') {
            let send = {
              data: res.data.url,
              space: 'thumbnail',
            };
            this.$emit('show-display', send);
          } else {
            let send = {
              data: res.data.url,
              space: 'icon',
            };
            this.$emit('show-display', send);
          }
          this.$refs.inputIcon.value = null;
          if (this.advert.type !== 'sponsored') {
            this.$refs.inputCover.value = null;
          }
          this.$store.commit('SET_GENERAL_LOADING', false);
        })
        .catch((error) => {
          this.$store.commit('SET_GENERAL_LOADING', false);
          alert('Something went wrong with uploading media');
          console.log('----Error----');
          console.log(error);
          Bugsnag.notify(error);
        });
    },
    gcd(a, b) {
      return b == 0 ? a : this.gcd(b, a % b);
    },
    generatePostfix() {
      // todo code refactor
      let randomChars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      while (!/^(?=.*[A-Z])(?=.*\d).{6,}$/.test(result)) {
        result = '';
        for (let i = 0; i < 6; i++) {
          result += randomChars.charAt(
            Math.floor(Math.random() * randomChars.length)
          );
        }
      }
      return `${result} ${Date.now()}`;
    },
    urlValidator(val) {
      if (val !== null) {
        return val.includes('http://') || val.includes('https://');
      }
      return false;
    },
    removeThumbnail() {
      this.$refs.inputThumbnail.value = null;
      this.$emit('remove-thumbnail');
    },
  },
};
</script>

<style scoped>
input[type='file'] {
  display: none;
}

.custom-file-upload {
  border: 1px solid #ccc;
  display: inline-block;
  padding: 6px 12px;
  cursor: pointer;
}
</style>
