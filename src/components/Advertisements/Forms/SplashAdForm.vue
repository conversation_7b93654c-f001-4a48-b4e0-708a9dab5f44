<template>
  <div class="splash-ad-form">
    <CSelect
      v-if="isAdHtmlSplash === true"
      label="HTML5 Ad Type"
      :value.sync="advert.html_ad_type"
      :options="html_ad_types"
      @update:value="prefillData"
      @change="clearDisplay"
    />

    <template v-if="isAdHtmlSplash === true">
      <html-splash-ad-form :advertisement="advert" />
    </template>
    <template v-else>
      <landing-page-form
        v-if="isAdHtmlSplash === false"
        :advertisement="advert"
      />
      <advertisement-media-form
        v-if="isAdHtmlSplash === false"
        :advertisement="advert"
        :selected="selected"
        :is-create="isCreate"
        @show-display="showDisplay"
        @remove-thumbnail="removeThumbnail"
      />
    </template>

    <CInput
      v-model="advert.splash_media_duration"
      invalid-feedback="Media Duration is required"
      :is-valid="validator"
      label="Media Duration (sec)"
      type="number"
    />
    <CInput
      v-model="advert.splash_media_no_skip_duration"
      invalid-feedback="No Skip Duration is required"
      :is-valid="validator"
      type="number"
      label="No Skip Duration (sec)"
    />

    <language-form :advertisement="advert" :is-create="isCreate" />

    <CSelect
      :value.sync="advert.cap_enabled"
      :options="cap"
      label="Advertisement Cap"
    />
  </div>
</template>
<script>
import { HTML_AD_TYPES } from '../../../constant/constants';
import LandingPageForm from './LandingPageForm.vue';
import LanguageForm from './LanguageForm.vue';
import AdvertisementMediaForm from './AdvertisementMediaForm.vue';
import HtmlSplashAdForm from './Html/HtmlSplashAdForm.vue';

export default {
  name: 'SplashAdForm',
  components: {
    HtmlSplashAdForm,
    AdvertisementMediaForm,
    LanguageForm,
    LandingPageForm,
  },
  props: {
    advertisement: {
      type: Object,
      required: true,
    },
    selected: {
      type: Boolean,
      required: true,
    },
    isCreate: {
      type: Boolean,
      required: false,
      default: true,
    },
  },
  data() {
    return {
      html_ad_types: [
        { value: '', label: 'Select Ad Type' },
        ...Object.values(HTML_AD_TYPES),
      ],
      cap: [
        { value: '', label: 'Select Ad Cap' },
        { value: 1, label: 'Enabled' },
        { value: 0, label: 'Disabled' },
      ],
    };
  },
  computed: {
    advert() {
      return this.advertisement;
    },
    isAdHtmlSplash() {
      return this.advert.media_type === 'web';
    },
  },
  methods: {
    prefillData(val) {
      if (val === 'splash') {
        this.$emit('prefill-data', val);
      }
    },
    clearDisplay() {
      this.$emit('clear-display');
    },
    validator(val) {
      return val ? val !== '' : false;
    },
    showDisplay(value) {
      this.$emit('show-display', value);
    },
    removeThumbnail() {
      this.$emit('remove-thumbnail');
    },
  },
};
</script>
