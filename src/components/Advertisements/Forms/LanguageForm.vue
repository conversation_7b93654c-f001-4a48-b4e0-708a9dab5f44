<template>
  <div class="language-form">
    <template v-if="isCreate === true">
      <label for="lang_C">Language</label>
      <div id="lang_C">
        <input
          type="checkbox"
          value="en"
          id="en_C"
          v-model="advert.language"
          class="mr-1"
        />
        <label for="en_C" class="mr-2">English</label>
        <input
          type="checkbox"
          value="ms"
          id="ms_C"
          v-model="advert.language"
          class="mr-1"
        />
        <label for="ms_C" class="mr-2">Malay</label>
        <input
          type="checkbox"
          value="zh"
          id="zh_C"
          v-model="advert.language"
          class="mr-1"
        />
        <label for="zh_C" class="mr-2">Chinese</label>
      </div>
    </template>
    <template v-else>
      <CSelect
        :value.sync="advert.language"
        :options="language"
        label="Language"
        invalid-feedback="Advertisement language is required"
        :is-valid="validator"
      ></CSelect>
    </template>
  </div>
</template>
<script>
export default {
  name: 'LanguageForm',
  props: {
    advertisement: {
      type: Object,
      required: true,
    },
    isCreate: {
      type: Boolean,
      required: false,
      default: true,
    },
  },
  data() {
    return {
      language: [
        { value: 'en', label: 'English' },
        { value: 'ms', label: 'Malay' },
        { value: 'zh', label: 'Chinese' },
      ],
    };
  },
  computed: {
    advert() {
      return this.advertisement;
    },
  },
  methods: {
    validator(val) {
      return val ? val !== '' : false;
    },
  },
};
</script>

<style lang="scss" scoped>
.language-form {
  margin-top: 15px;
  margin-bottom: 15px;
}
</style>
