<template>
  <div class="carousel-media-form">
    <label>Carousel Images File</label>

    <CButton
      class="float-right mb-2"
      color="primary"
      size="sm"
      shape="pill"
      @click="addInputFields"
    >
      Add Media
    </CButton>

    <draggable
      v-model="localValue"
      @start="dragging = true"
      @end="onDragEnd"
      handle=".drag-handle"
      item-key="index"
      animation="200"
    >
      <div
        v-for="(image, index) in localValue"
        :key="index"
        class="carousel-media-form__images my-3 d-flex align-items-center"
        :class="{ 'dragging-active': draggingItem === index }"
        @dragstart="draggingItem = index"
        @dragend="draggingItem = null"
      >
        <div class="col-auto d-flex align-items-center">
          <i class="fa fa-bars drag-handle"></i>
        </div>
        <div class="col">
          <div class="row">
            <div class="col">
              <CInput
                v-model="image.landing_url"
                label="Landing URL"
                invalid-feedback="Landing URL must start with http:// or https://"
                :is-valid="urlValidator"
              />
            </div>
          </div>
          <div class="row">
            <div class="col">
              <label for="adCoverFile1">Image Media File</label>
              <div>
                <label v-if="!image.id" class="custom-file-upload">
                  <input
                    type="file"
                    @change="uploadMedia($event, 'cover', index)"
                    class="form-control-file"
                    data-content="cover"
                    accept="image/*"
                  />
                  <i class="fa fa-cloud-upload"></i> Upload
                </label>
                <CInput
                  v-if="image.media_url !== ''"
                  :value="image.media_url"
                  :is-valid="urlValidator"
                  disabled
                />
              </div>
            </div>
            <div class="col-auto my-auto">
              <button
                class="btn btn-danger"
                @click.stop="removeInputFields(index)"
              >
                Remove
              </button>
            </div>
          </div>
        </div>
      </div>
    </draggable>
  </div>
</template>

<script>
import draggable from 'vuedraggable';
import Bugsnag from '@bugsnag/js';
import slugify from 'slugify';
import axios from 'axios';
import { HTML_AD_PLACEMENTS, IMAGE_TYPE } from '../../../../constant/constants';
import { validateMediaTypeWithFileType } from '../../../../helpers/field-validate';

export default {
  name: 'CarouselMediaForm',
  components: {
    draggable,
  },
  props: {
    value: {
      type: Array,
      required: true,
      default: () => [],
    },
    advertisementType: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      dragging: false,
      draggingItem: null,
    };
  },

  computed: {
    localValue: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit('input', newValue);
      },
    },
  },
  methods: {
    addInputFields() {
      this.localValue.push({
        placement: HTML_AD_PLACEMENTS.SLIDER,
        order: this.localValue.length,
        landing_url: '',
        media_url: '',
      });
    },
    removeInputFields(index) {
      this.localValue.splice(index, 1);
      this.updateOrder();

      if (this.localValue.length === 0) {
        this.addInputFields();
      }
    },
    updateOrder() {
      this.localValue = [
        ...this.localValue.map((item, index) => ({ ...item, order: index })),
      ];
    },
    onDragEnd() {
      this.dragging = false;
      this.updateOrder();
    },
    urlValidator(val) {
      if (val !== null) {
        if (val.includes('http://') || val.includes('https://')) {
          return true;
        } else {
          return false;
        }
      }
      return false;
    },
    async uploadMedia(event, type, index) {
      let url = await this.onFileChange(event, type);
      this.localValue[index].media_url = url;
    },
    async onFileChange(event) {
      let url = '';
      let input = event.target;
      if (input.files && input.files[0]) {
        if (validateMediaTypeWithFileType(IMAGE_TYPE, input.files[0])) {
          let iSize = input.files[0].size / 1000 / 1000;
          console.log(`${iSize} - file size`);
          if (iSize <= 1) {
            url = await this.getCoverDimension(input);
          } else {
            //display warning
            alert('File size exceeded. Max 1mb for Image');
            console.log('file too big');
          }
        } else {
          alert('Please select correct media type');
        }
      }
      return url;
    },
    gcd(a, b) {
      return b == 0 ? a : this.gcd(b, a % b);
    },
    async getCoverDimension(input) {
      return new Promise((resolve, reject) => {
        let file = input.files[0];
        if (!file || file.type.indexOf('image/') !== 0) {
          reject(new Error('Image not found'));
          return;
        }

        let reader = new FileReader();
        reader.onload = (evt) => {
          let img = new Image();
          img.onload = async () => {
            try {
              let url = await this.googleUpload(input);
              //   let w = img.width;
              //   let h = img.height;
              //   let r = this.gcd(w, h);
              //   let error, a1, a2;
              //
              //   error = 'Media must be in 9:16 ratio';
              //   a2 = w / r;
              //   a1 = h / r;
              //
              //   console.log(`${a1} : ${a2}`);
              //
              //   let url = '';
              //   if (a1 === 16 && a2 === 9) {
              //     url = await this.googleUpload(input);
              //   } else {
              //     alert(error);
              //   }
              //
              resolve(url);
            } catch (error) {
              reject(error);
            }
          };

          img.onerror = () => {
            reject(new Error('Failed to load image'));
          };

          img.src = evt.target.result;
        };

        reader.onerror = () => {
          reject(new Error('Failed to read file'));
        };

        reader.readAsDataURL(file);
      });
    },
    generatePostfix() {
      let randomChars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      while (!/^(?=.*[A-Z])(?=.*\d).{6,}$/.test(result)) {
        result = '';
        for (let i = 0; i < 6; i++) {
          result += randomChars.charAt(
            Math.floor(Math.random() * randomChars.length)
          );
        }
      }
      return `${result} ${Date.now()}`;
    },
    async googleUpload(input) {
      const name = input.files[0].name;
      const lastDot = name.lastIndexOf('.');
      const nameOnly = name.substring(0, lastDot);
      const ext = name.substring(lastDot + 1);
      let fileName =
        slugify(`${nameOnly} ${this.generatePostfix()}`) + `.${ext}`;
      let fileData = new FormData();
      fileData.append('path', 'adwav/temp');
      fileData.append('file', input.files[0]);
      fileData.append('name', fileName);
      this.$store.commit('SET_GENERAL_LOADING', true);
      return axios
        .post(process.env.VUE_APP_UPLOAD_FILE_TO_GCS, fileData)
        .then((res) => {
          this.$store.commit('SET_GENERAL_LOADING', false);
          return res.data.url;
        })
        .catch((error) => {
          this.$store.commit('SET_GENERAL_LOADING', false);
          alert('Something went wrong with uploading media');
          console.log('----Error----');
          console.log(error);
          Bugsnag.notify(error);
          return '';
        });
    },
  },
  mounted() {
    if (this.localValue.length === 0) {
      this.addInputFields();
    }
  },
};
</script>

<style scoped>
.drag-handle {
  cursor: grab;
  font-size: 18px;
  margin-right: 8px;
  color: #888;
  transition: color 0.2s;
}

.drag-handle:hover {
  color: #555;
}

input[type='file'] {
  display: none;
}

.custom-file-upload {
  border: 1px solid #ccc;
  display: inline-block;
  padding: 6px 12px;
  cursor: pointer;
}

.carousel-media-form__images {
  border: 1px solid #ccc;
  padding: 8px;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease-in-out;
}

.carousel-media-form__images.dragging-active {
  background-color: #f0f0f0;
  transition: background-color 0.1s ease-in-out;
}
</style>
