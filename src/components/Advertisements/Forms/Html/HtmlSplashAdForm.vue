<template>
  <div class="html-splash-ad-form">
    <carousel-media-form
      v-if="isCarousel === true"
      v-model="advert.ad_media"
      :advertisement-type="advertisement.type"
    />
    <image-slider-media-form
      v-if="isImageSlider === true"
      v-model="advert.ad_media"
      :advertisement-type="advertisement.type"
    />
    <video-slider-media-form
      v-if="isVideoSlider === true"
      v-model="advert.ad_media"
      :advertisement-type="advertisement.type"
    />
  </div>
</template>
<script>
import VideoSliderMediaForm from './VideoSliderMediaForm.vue';
import CarouselMediaForm from './CarouselMediaForm.vue';
import ImageSliderMediaForm from './ImageSliderMediaForm.vue';
import { HTML_AD_TYPES } from '../../../../constant/constants';

export default {
  name: 'HtmlSplashAdForm',
  components: { ImageSliderMediaForm, CarouselMediaForm, VideoSliderMediaForm },
  props: {
    advertisement: {
      type: Object,
      required: true,
    },
  },
  computed: {
    advert() {
      return this.advertisement;
    },
    isCarousel() {
      return this.advert.html_ad_type === HTML_AD_TYPES.CAROUSEL.value;
    },
    isImageSlider() {
      return this.advert.html_ad_type === HTML_AD_TYPES.IMAGE_SLIDER.value;
    },
    isVideoSlider() {
      return this.advert.html_ad_type === HTML_AD_TYPES.VIDEO_SLIDER.value;
    },
  },
};
</script>

<style scoped></style>
