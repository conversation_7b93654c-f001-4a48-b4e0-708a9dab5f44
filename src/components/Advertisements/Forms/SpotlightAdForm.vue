<template>
  <div class="spolight-ad-form">
    <CInput
      v-model="advert.cta"
      label="Advertisement CTA"
      invalid-feedback="Advertisement CTA is required"
      :description="ctaLength"
      :is-valid="validator"
    />

    <landing-page-form :advertisement="advert" />

    <advertisement-media-form
      :advertisement="advert"
      :selected="selected"
      @show-display="showDisplay"
      @remove-thumbnail="removeThumbnail"
    />

    <language-form :advertisement="advert" :is-create="isCreate" />
  </div>
</template>
<script>
import LandingPageForm from './LandingPageForm.vue';
import LanguageForm from './LanguageForm.vue';
import AdvertisementMediaForm from './AdvertisementMediaForm.vue';

export default {
  name: 'SpotlightAdForm',
  components: {
    AdvertisementMediaForm,
    LanguageForm,
    LandingPageForm,
  },
  props: {
    advertisement: {
      type: Object,
      required: true,
    },
    selected: {
      type: Boolean,
      required: true,
    },
    isCreate: {
      type: Boolean,
      required: false,
      default: true,
    },
  },
  data() {
    return {};
  },
  computed: {
    advert() {
      return this.advertisement;
    },
    ctaLength() {
      return `${this.advert.cta.length} characters`;
    },
  },
  methods: {
    validator(val) {
      return val ? val !== '' : false;
    },
    showDisplay(value) {
      this.$emit('show-display', value);
    },
    removeThumbnail() {
      this.$emit('remove-thumbnail');
    },
  },
};
</script>
