<template>
  <div>
    <CButton v-if="isItemTranslated" @click="getTranslation()" color="success">
      Translated
    </CButton>
    <CButton v-else @click="translate()" color="primary">Translate</CButton>

    <CModal
      title="Translation"
      :show.sync="show"
      :close-on-backdrop="false"
      centered
      size="xl"
    >
      <OverlayLoader v-if="isLoading" />
      <div>
        <div
          v-for="(translation, lang) in translations"
          :key="lang"
          class="mb-3"
        >
          <CCard>
            <CCardHeader>
              <div class="d-flex justify-content-between w-100">
                <CButton
                  color="primary"
                  variant="outline"
                  @click="toggleCollapse(lang)"
                >
                  {{ lang.toUpperCase() }}
                </CButton>
                <CButton
                  color="success"
                  variant="outline"
                  @click="translate(lang)"
                >
                  Translate Again
                </CButton>
              </div>
            </CCardHeader>
            <CCollapse :show="collapsed[lang]">
              <CCardBody>
                <div v-if="translation !== 'No translation for this language'">
                  <p><strong>Title:</strong> {{ translation.title }}</p>
                  <p><strong>Content:</strong></p>
                  <div v-html="resizeImages(translation.content)"></div>
                </div>
                <p v-else>
                  {{ translation }}
                </p>
              </CCardBody>
            </CCollapse>
          </CCard>
        </div>
      </div>
    </CModal>
  </div>
</template>

<script>
import axios from 'axios';
import * as headers from '@/helpers/headers';
import OverlayLoader from '../views/OverlayLoader';
import Vue from 'vue';
import { ALL_LANGUAGES } from '../../constant/constants';

export default {
  name: 'TranslationPreview',
  components: { OverlayLoader },
  props: {
    item: {
      type: Object,
      required: true,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      show: false,
      isLoading: false,
      translations: [],
      collapsed: {},
    };
  },
  watch: {
    show(newVal) {
      if (!newVal) {
        // Reset the data when the modal is closed
        this.resetData();
      }
    },
  },
  computed: {
    isItemTranslated() {
      return this.item.is_translated || this.item.isTranslated;
    },
  },
  methods: {
    getUniqueId() {
      return this.item.unique_id || this.item.uniqueID;
    },
    translate(lang) {
      this.$swal
        .fire({
          icon: 'question',
          text: 'Translation may take some time. Continue?',
          showCancelButton: true,
          confirmButtonText: 'Yes',
          cancelButtonText: 'Cancel',
        })
        .then((result) => {
          if (result.isConfirmed) {
            this.show = false;
            this.$store
              .dispatch('translate', {
                unique_id: this.getUniqueId(),
                languages: lang
                  ? [lang]
                  : ALL_LANGUAGES.filter(
                      (language) => language !== this.item.language
                    ),
              })
              .then(() => {
                if ('is_translated' in this.item) {
                  this.item.is_translated = true;
                } else {
                  this.item.isTranslated = true;
                }
                this.getTranslation();
              });
          }
        });
    },
    getTranslation() {
      this.show = true;
      this.isLoading = true;
      let url = process.env.VUE_APP_GET_ARTICLE_TRANSLATIONS.replace(
        '{uniqueId}',
        this.getUniqueId()
      );
      axios
        .get(url, headers.createHeaders(this.$store.getters.getUserToken))
        .then((res) => {
          this.translations = res.data;
          Object.keys(this.translations).forEach((lang) => {
            if (!(lang in this.collapsed)) {
              Vue.set(this.collapsed, lang, true); // Ensure reactivity
            }
          });
          this.isLoading = false;
        })
        .catch((error) => {
          this.show = false;
          this.isLoading = false;
          let message = 'Check console for more info';
          let title = 'Error';
          if (error.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          this.$notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${error.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          console.log('---Error---');
          console.log(error);
        });
    },
    getLanguageName(langCode) {
      const langMap = {
        ms: 'Malay',
        en: 'English',
        zh: 'Chinese',
      };
      return langMap[langCode];
    },
    toggleCollapse(lang) {
      Vue.set(this.collapsed, lang, !this.collapsed[lang]);
    },
    resizeImages(content) {
      const wrapper = document.createElement('div');
      wrapper.innerHTML = content;

      const images = wrapper.querySelectorAll('img');
      images.forEach((img) => {
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
      });

      const videos = wrapper.querySelectorAll('video');
      videos.forEach((video) => {
        video.style.maxWidth = '100%';
      });

      const iframes = wrapper.querySelectorAll('iframe');
      iframes.forEach((iframe) => {
        iframe.style.maxWidth = '100%';
      });

      return wrapper.innerHTML;
    },
    resetData() {
      this.isLoading = false;
      this.translations = [];
      this.collapsed = {};
    },
  },
};
</script>
