<template>
  <div>
    <div>
      <CButton @click="collapse = !collapse" color="primary" class="mb-2">
        Filters
      </CButton>
      <CButton @click="reset()" color="primary" class="mb-2 ml-2">
        Reset
      </CButton>
      <CButton @click="refresh()" color="primary" class="mb-2 ml-2">
        Refresh
      </CButton>
      <CCollapse :show="collapse">
        <CCard body-wrapper>
          <CRow>
            <!--Publisher column in filter collapse-->
            <CCol sm="4">
              Publisher
              <!--selection tiles to loop through publisher array and show publisher label from publishers list-->
              <multiselect
                id="publishers"
                v-model="publishers_array"
                :options="filteredPublishers"
                :multiple="true"
                :custom-label="publisherLabel"
                :close-on-select="false"
                :clear-on-select="false"
                track-by="id"
                :searchable="true"
                :internal-search="false"
                @search-change="handleSearchChange"
                @input="onChangePublisher"
              >
                <template
                  slot="selection"
                  slot-scope="{ values, search, isOpen }"
                >
                  <span
                    class="multiselect__single"
                    v-if="values.length"
                    v-show="!isOpen"
                  >
                    {{
                      allPublishersSelected
                        ? 'All Publishers'
                        : values.length + ' publisher(s) selected'
                    }}
                  </span>
                </template>

                <!-- Loop through the publishers options -->
                <template
                  slot="option"
                  slot-scope="{ option }"
                  @click.self="select(option)"
                >
                  <div>
                    <!-- Checkbox input for each publisher -->
                    <input
                      type="checkbox"
                      :value="option.id"
                      v-model="option.checked"
                      @focus.prevent
                    />
                    {{ option.publisherName }}
                  </div>
                </template>

                <!-- Default message if no publishers found -->
                <span slot="noResult">Oops! No publishers with that name</span>
              </multiselect>
            </CCol>
            <CCol sm="4">
              Topic
              <multiselect
                id="topics"
                v-model="topics_array"
                :options="topics"
                :multiple="true"
                :custom-label="topicLabel"
                track-by="id"
                @input="onChangeTopic"
              >
                <span slot="noResult">Oops! No topics with that name</span>
              </multiselect>
            </CCol>
            <CCol sm="4">
              Content Type
              <multiselect
                id="type"
                v-model="type_array"
                :options="type_options"
                :multiple="true"
                :searchable="false"
                @input="onChangeType"
              >
              </multiselect>
            </CCol>
            <CCol sm="4">
              Language
              <multiselect
                id="language"
                v-model="lang_array"
                :options="lang_options"
                :multiple="true"
                :searchable="false"
                @input="onChange"
              >
              </multiselect>
            </CCol>
            <CCol sm="4">
              Published Time (Hours)
              <multiselect
                id="language"
                v-model="hours"
                :options="hours_options"
                :searchable="false"
                @input="onChangeHours"
              >
              </multiselect>
            </CCol>
            <CCol sm="4">
              Native
              <multiselect
                id="native"
                v-model="native"
                :options="native_options"
                :searchable="false"
                :multiple="true"
                track-by="value"
                label="label"
                @input="onChangeNative"
              >
              </multiselect>
            </CCol>
          </CRow>
        </CCard>
      </CCollapse>
    </div>
    <div>
      <CInput
        v-model="searchKeyword"
        class="col-12 p-0 mt-2"
        placeholder="Search by Title or Description"
      ></CInput>
      <CDataTable
        :items="contents"
        :fields="fields"
        items-per-page-select
        :items-per-page="10"
        hover
        sorter
        pagination
        :loading="loading"
      >
        <template #description="{ item }">
          <td>
            {{
              item.description
                | truncate(item.language == 'zh' ? 80 : 150, '...')
            }}
          </td>
        </template>
        <template #language="{ item }">
          <td>
            {{ item.language.toUpperCase() }}
          </td>
        </template>
        <template #stats="{ item }">
          <td>
            {{
              `${item.reaction_count} / ${item.comment_count} / ${item.share_count}`
            }}
          </td>
        </template>
        <template #topic="{ item }">
          <td>
            {{ item.topic == '' ? '-' : item.topic }}
          </td>
        </template>
        <template #action="{ item }">
          <td>
            <a :href="getLink(item)" target="_blank">
              <CButton color="primary" class="mb-2"> Link </CButton>
            </a>
            <CButton
              @click="go(item.content_type, item.unique_id)"
              color="primary"
              class="mb-2"
            >
              App
            </CButton>

            <TranslationPreview v-if="item.content_type === 'a'" :item="item" />
          </td>
        </template>
      </CDataTable>
    </div>
  </div>
</template>

<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>

<script>
import Multiselect from 'vue-multiselect';
import TranslationPreview from '@/components/AllContent/TranslationPreview.vue';

export default {
  name: 'AllContent',
  components: { Multiselect, TranslationPreview },
  data() {
    return {
      collapse: false,
      innerCollapse: false,
      searchKeyword: '',
      publishers_array: [],
      allPublishersSelected: true,
      searchQuery: '',
      topics_array: [
        {
          id: -999,
          nameEN: 'All Topics',
        },
      ],
      native_options: [
        { value: 1, label: 'Native' },
        { value: 0, label: 'Non-native' },
      ],
      native: [{ value: 1, label: 'Native' }],
      hours: 2,
      hours_select: [],
      lang_array: ['EN'],
      type_array: ['Article'],
      type_options: ['Article', 'Podcast', 'Video'],
      lang_options: ['EN', 'MS', 'ZH'],
      hours_options: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      fields: [
        {
          key: 'published_date',
          sorter: true,
          filter: false,
          label: 'Published Date',
        },
        { key: 'view_count', sorter: true, filter: false, label: 'Views' },
        { key: 'unique_id', sorter: true, filter: false, label: 'Unique ID' },
        { key: 'title', sorter: false, filter: false, label: 'Title' },
        {
          key: 'description',
          sorter: true,
          filter: false,
          label: 'Description',
        },
        { key: 'language', sorter: true, filter: false, label: 'Language' },
        { key: 'publisher', sorter: true, filter: false, label: 'Publisher' },
        { key: 'topic', sorter: true, filter: false, label: 'Topic' },
        {
          key: 'stats',
          sorter: false,
          filter: true,
          label: 'Reactions/Comments/Shares',
        },
        {
          key: 'action',
          sorter: false,
          label: 'Action',
          filter: true,
          _style: { width: '10px' },
        },
      ],
    };
  },
  filters: {
    date(val) {
      return val ? val.toLocaleString() : '';
    },
    truncate: function (text, length, suffix) {
      if (text.length > length) {
        return text.substring(0, length) + suffix;
      } else {
        return text;
      }
    },
  },
  created() {
    // Watch for changes in getContentPublishers and initialize publishers_array when it changes
    this.$watch(
      () => this.$store.getters.getContentPublishers,
      (newPublishers, oldPublishers) => {
        if (newPublishers !== oldPublishers) {
          this.publishers_array = [
            {
              id: -999,
              publisherName: 'All Publisher',
              checked: true,
            },
            ...newPublishers.map((publisher) => ({
              ...publisher,
              checked: true,
            })),
          ];
        }
      }
    );

    let hours = this.hours;
    let type_array = this.type_array;
    let native = this.native[0].value;
    this.$store
      .dispatch('getContentFilters', { hours, type_array, native })
      .then(() => {
        this.getContent();
      });
  },
  computed: {
    publishers() {
      const publishers = this.$store.getters.getContentPublishers;
      return publishers.map((publisher) => ({
        ...publisher,
        checked: this.publishers_array.some((p) => p.id === publisher.id),
      }));
    },

    // dropdown options filter
    filteredPublishers() {
      // if no query, display all
      if (!this.searchQuery) {
        return this.publishers;
      }
      // if got query, display related
      return this.publishers.filter((publisher) =>
        publisher.publisherName
          .toLowerCase()
          .includes(this.searchQuery.toLowerCase())
      );
    },

    topics() {
      return this.$store.getters.getContentTopics;
    },
    contents() {
      if (this.searchKeyword) {
        let keyword = this.searchKeyword.toLowerCase();
        return this.$store.getters.getContents.filter((item) => {
          return (
            item.title.toLowerCase().includes(keyword) ||
            item.description.toLowerCase().includes(keyword)
          );
        });
      }
      return this.$store.getters.getContents;
    },
    loading() {
      return this.$store.getters.getContentLoading;
    },
    translation() {
      return this.$store.getters.getTranslations;
    },
  },
  methods: {
    onChange() {
      this.getContent();
    },
    onChangePublisher(value) {
      // Check if "All Publishers" was in the previous selection
      let wasAllPublishersSelected = this.allPublishersSelected;

      if (value.length > 0) {
        // if selected value is all publishers
        if (value[value.length - 1].id === -999) {
          // get all publishers and check their checkbox
          let allPublishers = this.$store.getters.getContentPublishers.map(
            (publisher) => ({
              ...publisher,
              checked: true,
            })
          );
          // add to publishers array
          this.publishers_array = allPublishers;
          // mark boolean as true
          this.allPublishersSelected = true;
        } else {
          // check if all publishers is selected previously
          if (wasAllPublishersSelected) {
            // check if the all publishers option is deselected
            if (!value.some((publisher) => publisher.id === -999)) {
              // deselect everything
              this.publishers_array = [];
              this.allPublishersSelected = false;
            } else if (value.length === this.publishers.length) {
              // check for currently deselected number of publishers
              const deselectedCount = this.publishers.filter(
                (publisher) => !publisher.checked
              ).length;

              // if there is publisher that is not selected
              if (deselectedCount > 0) {
                // uncheck all publishers option
                value = value.filter((publisher) => publisher.id !== -999);
                this.publishers_array = value;
                this.allPublishersSelected = false;
              } else {
                // deselect everything
                this.publishers_array = [];
                this.allPublishersSelected = false;
              }
            } else {
              // filter out the all publishers option
              value = value.filter((publisher) => publisher.id !== -999);
              this.publishers_array = value;
              this.allPublishersSelected = false;
            }
          } else {
            // if previous round not all publisher is selected
            // check if after selecting or deselecting for this round, is it all publishers selected
            const publishersNumber = this.publishers.length;
            // check for currently selected number of publishers
            const selectedCount = this.publishers.filter(
              (publisher) => publisher.checked && publisher.id !== -999
            ).length;
            // if all publishers are selected
            if (publishersNumber - 1 === selectedCount) {
              // get all publishers and check their checkbox
              let allPublishers = this.$store.getters.getContentPublishers.map(
                (publisher) => ({
                  ...publisher,
                  checked: true,
                })
              );
              // add to publishers array
              this.publishers_array = allPublishers;
              // reset value array
              value = allPublishers;
              this.allPublishersSelected = true;
            } else {
              // update selected publishers
              this.publishers_array = value;
              this.allPublishersSelected = false;
            }
          }
        }
        this.onChange();
      } else {
        // If no publishers are selected, deselect all
        this.publishers_array = [];
        value = [];
        this.allPublishersSelected = false;
        this.onChange();
      }
    },
    handleSearchChange(query) {
      this.searchQuery = query;
    },
    onChangeTopic(value) {
      if (value.length > 0) {
        if (value[value.length - 1].id === -999) {
          let data = value[value.length - 1];
          this.topics_array = [];
          this.topics_array.push(data);
        } else {
          if (value.find((element) => element.id === -999) != null) {
            value = value.filter(function (item) {
              return item.id !== -999;
            });
            this.topics_array = [];
            this.topics_array = value;
          }
        }
        this.onChange();
      } else {
        this.topics_array = [
          {
            id: -999,
            nameEN: 'All Topics',
          },
        ];
        this.onChange();
      }
    },
    onChangeHours(val) {
      this.hours = val;
      this.topics_array = [
        {
          id: -999,
          nameEN: 'All Topics',
        },
      ];
      let hours = this.hours;
      let type_array = this.type_array;
      let native = 'all';
      if (this.native.length === 1) {
        native = this.native[0].value;
      }
      this.$store
        .dispatch('getContentFilters', { hours, type_array, native })
        .then(() => {
          this.onChange();
        });
    },
    onChangeType(val) {
      this.type = val;
      this.topics_array = [
        {
          id: -999,
          nameEN: 'All Topics',
        },
      ];
      let hours = this.hours;
      let type_array = this.type_array;
      let native = 'all';
      if (this.native.length === 1) {
        native = this.native[0].value;
      }
      this.$store
        .dispatch('getContentFilters', { hours, type_array, native })
        .then(() => {
          this.onChange();
        });
    },
    onChangeNative(val) {
      this.topics_array = [
        {
          id: -999,
          nameEN: 'All Topics',
        },
      ];
      let hours = this.hours;
      let type_array = this.type_array;
      let native = 'all';
      if (val.length === 1) {
        native = val[0].value;
      } else if (val.length === 0) {
        this.native = [{ value: 1, label: 'Native' }];
        native = this.native[0].value;
      }
      this.$store
        .dispatch('getContentFilters', { hours, type_array, native })
        .then(() => {
          this.onChange();
        });
    },
    getContent() {
      let hours = this.hours;
      let p = this.publishers_array;
      let t = this.topics_array;
      let type_array = this.type_array;
      let lang_array = this.lang_array;
      let publishers_array = [];
      let topics_array = [];
      let native = 'all';
      if (this.native.length === 1) {
        native = this.native[0].value;
      }
      p.forEach((publisher) => publishers_array.push(publisher.id));
      t.forEach((topic) => topics_array.push(topic.id));
      this.$store.dispatch('getContent', {
        hours,
        publishers_array,
        topics_array,
        type_array,
        lang_array,
        native,
      });
    },
    publisherLabel({ id, publisherName }) {
      if (id === -999) {
        // Return "All Publishers" if selected
        return publisherName;
      } else {
        // Return the count of selected publishers
        const selectedCount = this.publishers_array.filter(
          (publisher) => publisher.checked && publisher.id !== -999
        ).length;
        return `${selectedCount} Publisher${selectedCount !== 1 ? 's' : ''} Selected`;
      }
    },
    topicLabel({ nameEN }) {
      return nameEN;
    },
    go(type, id) {
      setTimeout(() => {
        //window.open(`https://newswav.com/${id}`, '_blank');
      }, 900);
      if (type == 'a') {
        window.location = `newswav://applinks/${id}`;
      }
      if (type == 'v') {
        window.location = `newswav://applinks/video/${id}`;
      }
      if (type == 'p') {
        window.location = `newswav://applinks/podcast/${id}`;
      }
    },
    getLink(item) {
      return `https://newswav.com/${item.link}`;
    },
    refresh() {
      this.getContent();
    },
    reset() {
      this.searchKeyword = '';
      this.hours = 2;
      this.type_array = ['Article'];
      this.lang_array = ['EN'];
      this.publishers_array = [];
      this.topics_array = [
        {
          id: -999,
          nameEN: 'All Topics',
        },
      ];
      let hours = this.hours;
      let type_array = this.type_array;
      this.native = [{ value: 1, label: 'Native' }];
      let native = 'all';
      if (this.native.length === 1) {
        native = this.native[0].value;
      }
      this.$store
        .dispatch('getContentFilters', { hours, type_array, native })
        .then(() => {
          this.getContent();
        });
    },
  },
};
</script>
