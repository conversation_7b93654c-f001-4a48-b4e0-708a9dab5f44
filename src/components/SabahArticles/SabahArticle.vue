<template>
  <div>
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader>
            <strong>Curate Sabah Articles</strong>
          </CCardHeader>
          <CCardBody>
            <CDataTable
              v-bind:loading="sabahLoading"
              :items="sabahListArticles"
              sorter
              table-filter
              small
              pagination
              :items-per-page="10"
              items-per-page-select
              :fields="fields"
            >
              <template v-if="sabahLoading" #no-items-view>
                <div style="height: 300px"></div>
              </template>
              <template #enabled="{ item }">
                <td>
                  <CBadge
                    :color="enabledBadge(item.enabled)"
                    v-text="item.enabled ? 'Yes' : 'No'"
                  ></CBadge>
                </td>
              </template>
              <template #action="{ item }">
                <td>
                  <CButtonToolbar>
                    <a :href="item.url" target="_blank">
                      <CButton
                        variant="outline"
                        color="success"
                        v-c-tooltip="'Open Source'"
                      >
                        <CIcon name="cil-envelope-open"></CIcon>
                      </CButton>
                    </a>
                    <EditFeedArticleModal :article="item" />
                  </CButtonToolbar>
                </td>
              </template>
            </CDataTable>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import EditFeedArticleModal from './EditFeedArticleModal';
export default {
  name: 'SabahArticle',
  components: { EditFeedArticleModal },
  data() {
    return {
      fields: [
        { key: 'article_unique_id', label: 'Article Unique ID' },
        { key: 'title', label: 'Title' },
        { key: 'enabled', label: 'Enabled' },
        { key: 'published_date', label: 'Published Date' },
        { key: 'action', sortable: false, label: 'Action' },
      ],
    };
  },
  computed: {
    sabahLoading() {
      return this.$store.getters.getSabahLoading;
    },
    sabahListArticles() {
      return this.$store.getters.getSabahArticles;
    },
  },
  created() {
    this.$store.dispatch('listArticles');
  },
  methods: {
    enabledBadge(status) {
      return status === 1 ? 'success' : 'danger';
    },
  },
};
</script>

<style scoped></style>
