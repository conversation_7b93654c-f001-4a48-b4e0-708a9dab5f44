<template>
  <div>
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader>
            <strong>Sabah Twitter News Feed</strong>
          </CCardHeader>
          <CCardBody>
            <CDataTable
              :loading="sabahLoading"
              :items="tweets"
              :fields="tweets_fields"
              sorter
              table-filter
              small
              pagination
              :items-per-page="10"
              items-per-page-select
            >
              <template v-if="sabahLoading" #no-items-view>
                <div style="height: 300px"></div>
              </template>
              <template #action="{ item }">
                <td>
                  <CButtonToolbar>
                    <a v-bind:href="item.url" target="_blank">
                      <CButton
                        color="info"
                        v-c-tooltip="'Open Tweet'"
                        variant="outline"
                      >
                        <CIcon name="cib-twitter"></CIcon>
                      </CButton>
                    </a>
                  </CButtonToolbar>
                </td>
              </template>
            </CDataTable>
          </CCardBody>
        </CCard>
      </CCol>
      <CCol>
        <CCard>
          <CCardHeader>
            <strong>Sabah Facebook News Feed</strong>
          </CCardHeader>
          <CCardBody>
            <CDataTable
              :loading="sabahLoading"
              :items="facebook"
              :fields="facebook_fields"
              sorter
              table-filter
              small
              pagination
              :items-per-page="10"
              items-per-page-select
            >
              <template v-if="sabahLoading" #no-items-view>
                <div style="height: 300px"></div>
              </template>
              <template #action="{ item }">
                <td>
                  <CButtonToolbar>
                    <a v-bind:href="item.url" target="_blank">
                      <CButton
                        color="primary"
                        v-c-tooltip="'Open Facebook Post'"
                        variant="outline"
                      >
                        <CIcon name="cib-facebook-f"></CIcon>
                      </CButton>
                    </a>
                  </CButtonToolbar>
                </td>
              </template>
            </CDataTable>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  </div>
</template>

<script>
export default {
  name: 'SabahNewsFeed',
  data() {
    return {
      tweets_fields: [
        { key: 'author', label: 'Author' },
        { key: 'title', label: 'Title' },
        { key: 'action', sortable: false, label: 'Action' },
      ],
      facebook_fields: [
        { key: 'author', label: 'Author' },
        { key: 'title', label: 'Title' },
        { key: 'action', sortable: false, label: 'Action' },
      ],
    };
  },
  computed: {
    tweets() {
      let feed = this.$store.getters.getNewsFeed;
      let twitter = feed.filter((news) => {
        return news.url.includes('twitter.com');
      });
      return twitter;
    },
    facebook() {
      let feed = this.$store.getters.getNewsFeed;
      let fb = feed.filter((news) => {
        return news.url.includes('facebook.com');
      });
      return fb;
    },
    sabahLoading() {
      return this.$store.getters.getSabahLoading;
    },
  },
  created() {
    this.$store.dispatch('listNewsFeed');
  },
};
</script>

<style scoped></style>
