<template>
  <div>
    <CButton
      @click="show = true"
      variant="outline"
      v-bind:color="twitter ? 'info' : 'primary'"
    >
      <CIcon v-bind:name="twitter ? 'cib-twitter' : 'cib-facebook-f'"></CIcon>
    </CButton>
    <CModal size="lg" :show.sync="show" centered>
      <div v-if="twitter">
        <Tweet
          v-bind:id="tweet_id"
          :options="{ align: 'center', conversation: 'none' }"
        ></Tweet>
      </div>
      <div v-if="facebook">
        <div id="fb-root"></div>
        <div
          class="fb-post"
          :data-href="article.url"
          data-show-text="true"
          data-width=""
        ></div>
      </div>
      <template>
        <template slot="footer">
          <CButton color="success" variant="outline" @click="show = !show"
            >Close</CButton
          >
        </template>
      </template>
    </CModal>
  </div>
</template>
<script>
import { Tweet } from 'vue-tweet-embed';

export default {
  name: 'ViewSocial',
  components: { Tweet },
  data() {
    return {
      show: false,
    };
  },
  created() {
    window.FB.XFBML.parse();
  },
  props: {
    article: {
      type: Object,
      required: true,
    },
    twitter: {
      type: Boolean,
      default: false,
    },
    facebook: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    tweet_id() {
      let base = this.article.url.split('/');
      return base[base.length - 1];
    },
  },
};
</script>

<style scoped></style>
