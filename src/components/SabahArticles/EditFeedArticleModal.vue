<template>
  <div>
    <CButton
      color="warning"
      variant="outline"
      @click="show = true"
      v-c-tooltip="'Edit'"
    >
      <CIcon name="cil-pencil"></CIcon>
    </CButton>
    <CModal
      title="Update Article"
      color="default"
      size="lg"
      :show.sync="show"
      centered
    >
      <CInput v-model="article.article_unique_id" label="Article ID" readonly />
      <CInput v-model="article.title" label="Title" readonly />
      <CInput v-model="article.language" label="Language" readonly="" />
      <CInput v-model="article.feed_id" label="Feed ID" readonly />
      <input
        type="checkbox"
        id="checkboxVue"
        v-model="article.enabled"
        :value="article.enabled"
        style="margin-right: 10px"
      />
      <label for="checkboxVue">Enable Article</label>
      <template slot="footer">
        <CButton
          color="success"
          variant="outline"
          type="submit"
          @click="updateArticle"
          >Update</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
export default {
  name: 'EditFeedArticleModal',
  props: {
    article: {
      type: Object,
    },
  },
  data() {
    return {
      show: false,
    };
  },
  methods: {
    updateArticle() {
      let msg = 'This will update the article';
      let send = {
        articleIds: this.article.article_unique_id,
        feedId: this.article.feed_id,
        enabled: this.getValue(this.article.enabled),
      };
      if (confirm(msg)) {
        this.$store.dispatch('hideArticle', send);
      }
    },
    getValue(val) {
      if (val) {
        return 1;
      } else {
        return 0;
      }
    },
  },
};
</script>

<style scoped></style>
