<template>
  <CCard>
    <CCardHeader>List of Highlights</CCardHeader>
    <CCardBody>
      <CRow>
        <CCol>
          <router-link :to="'highlights/upsert'">
            <CButton
              v-show="checkAction('create_highlight')"
              class="float-right mb-2"
              variant="outline"
              color="success"
              shape="pill"
              >Add Highlight</CButton
            >
          </router-link>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CDataTable
            :loading="loading"
            :key="highlights.id"
            small
            :sorter-value="{ column: 'endDate', asc: false }"
            sorter
            :fields="list_fields"
            :items="highlights"
          >
            <template #show_details="{ item }">
              <td>
                <CButtonToolbar>
                  <router-link :to="'highlights/contents/' + item.id">
                    <CButton
                      v-show="checkAction('get_highlight_contents')"
                      color="primary"
                      v-c-tooltip="'View Highlight'"
                      variant="outline"
                      ><CIcon name="cil-share"></CIcon
                    ></CButton>
                  </router-link>
                  <router-link :to="'highlights/upsert/' + item.id">
                    <CButton
                      v-show="checkAction('edit_highlight')"
                      color="warning"
                      v-c-tooltip="'Edit Highlight'"
                      variant="outline"
                      ><CIcon name="cil-pencil"></CIcon
                    ></CButton>
                  </router-link>
                  <CButton
                    v-show="!editable(item) && checkAction('delete_highlight')"
                    @click="deleteHighlight(item)"
                    variant="outline"
                    color="danger"
                    v-c-tooltip="'Remove Highlight'"
                    ><CIcon name="cil-trash"></CIcon
                  ></CButton>
                </CButtonToolbar>
              </td>
            </template>
          </CDataTable>
          <CPagination
            @update:activePage="goToPage"
            :pages="pagination.last_page"
            :activePage.sync="pagination.current_page"
            align="center"
          />
        </CCol>
      </CRow>
    </CCardBody>
  </CCard>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
import { parseISO, format, isBefore } from 'date-fns';
export default {
  data() {
    return {
      list_fields: [
        { key: 'statusShow', sorter: true, label: 'Status' },
        { key: 'startDate', label: 'Start Time' },
        { key: 'endDate', label: 'End Time' },
        { key: 'isPinnedShow', label: 'Pin' },
        { key: 'titleShow', sorter: true, label: 'Title' },
        { key: 'descShow', sorter: true, label: 'Desc' },
        { key: 'typeShow', sorter: true, label: 'Type' },
        { key: 'feedShow', sorter: true, label: 'Feed' },
        { key: 'show_details', sorter: false, filter: false, label: 'Actions' },
      ],
      current_date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
    };
  },
  async created() {
    if (this.feeds.length === 0) {
      await this.listFeeds();
    }
    this.listHighlights();
  },
  computed: {
    ...mapGetters({
      loading: 'getHighlightsLoading',
      feeds: 'getFeeds',
      highlights: 'getHighlights',
      pagination: 'getPagination',
    }),
  },
  methods: {
    ...mapActions({
      removeHighlight: 'removeHighlight',
      listHighlights: 'listHighlights',
      listFeeds: 'listFeeds',
    }),
    editable(item) {
      return !isBefore(parseISO(this.current_date), parseISO(item.endDate));
    },
    deleteHighlight(item) {
      const firstLang = item.languages[0];
      this.$swal
        .fire({
          icon: 'question',
          html: `<p>Remove this highlight?</p><p>
            ${item.translations[firstLang].title}</p>`,
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            this.removeHighlight(item);
          }
        });
    },
    goToPage(currentPage) {
      this.listHighlights(currentPage);
    },
  },
};
</script>
