<template>
  <div>
    <CCard>
      <CCardHeader> Highlight Contents </CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <div class="d-flex flex-row align-items-center mb-4">
              <input
                type="text"
                v-model="query"
                placeholder="Search via Unique ID OR Title"
                class="form-control"
              />
              <CButton
                type="submit"
                variant="outline"
                color="success"
                @click="search"
              >
                Search
              </CButton>
              <CButton
                type="button"
                variant="outline"
                color="warning"
                @click="resetQuery"
              >
                Reset
              </CButton>
            </div>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CButtonToolbar class="float-right mb-4">
              <CButton
                variant="outline"
                shape="pill"
                color="info"
                @click="show_details = !show_details"
                >Highlight Summary</CButton
              >
            </CButtonToolbar>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CCollapse :show.sync="show_details" class="mb-4">
              <div v-if="highlight.type === 'keywords'">
                <CInput
                  v-for="[prop, value] in Object.entries(
                    highlight.translations
                  )"
                  :key="prop"
                  :label="prop.toUpperCase() + ' Keywords'"
                  disabled
                  :value="value.keywords"
                />
              </div>
              <div v-else-if="highlight.type === 'contents'">
                <label for="contentIds">Content IDs</label>
                <multiselect
                  disabled
                  v-model="highlight.contentIds"
                  :multiple="true"
                  track-by="value"
                  label="label"
                  :options="content"
                  placeholder=""
                  id="contentIds"
                >
                </multiselect>
              </div>
              <div v-else>
                <label for="ajax">Publishers</label>
                <multiselect
                  :disabled="true"
                  v-model="highlight.publisherIds"
                  :multiple="true"
                  track-by="value"
                  label="label"
                  :options="publisherIds"
                  placeholder=""
                  id="ajax"
                >
                </multiselect>
              </div>
            </CCollapse>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CDataTable
              :loading="loading"
              :key="highlight_contents"
              sorter
              small
              :items-per-page="20"
              :items="highlight_contents"
              :fields="content_field"
            >
              <template #language="{ item }">
                <td>
                  {{ item.language | uppercase }}
                </td>
              </template>
              <template #action="{ item }">
                <td>
                  <CButtonToolbar v-if="highlight.type === 'keywords'">
                    <CButton
                      v-show="checkAction('disable_content_highlight')"
                      v-if="item.disabled === 0"
                      color="danger"
                      variant="outline"
                      @click="hide(item)"
                      v-c-tooltip="'Hide'"
                    >
                      <CIcon name="cil-x-circle"></CIcon>
                    </CButton>
                    <CButton
                      v-show="checkAction('enable_content_highlight')"
                      v-if="item.disabled !== 0"
                      color="dark"
                      variant="outline"
                      @click="unhide(item)"
                      v-c-tooltip="'Unhide'"
                    >
                      <CIcon name="cil-check-circle"></CIcon>
                    </CButton>
                  </CButtonToolbar>
                </td>
              </template>
            </CDataTable>
            <CPagination
              :activePage.sync="currentPage"
              :pages="pages"
              size=""
              align="center"
              @update:activePage="searchPage"
              v-show="highlight_contents.length > 0 || currentPage > 1"
            />
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<script>
import format from 'date-fns/format';
import fromUnixTime from 'date-fns/fromUnixTime';
import Multiselect from 'vue-multiselect';

export default {
  name: 'HighlightsContent',
  components: { Multiselect },
  data() {
    return {
      show_details: false,
      currentPage: 1,
      pages: 2,
      query: '',
      currentQuery: '',
      loading: false,
      highlight: {
        feedId: [],
        startDate: '',
        endDate: '',
        imageUrl: '',
        slug: '',
        type: '',
        languages: [],
        translations: {},
        publisherIds: [],
        contentIds: [],
      },
      highlight_contents: [],
    };
  },
  async created() {
    if (this.$store.getters.getFeeds.length === 0) {
      await this.$store.dispatch('listFeeds');
    }
    this.loading = true;
    this.$store
      .dispatch('listHighlightsById', parseInt(this.$route.params.id))
      .then((res) => {
        Object.entries(res[0]).forEach(([prop, val]) => {
          this.highlight[prop] = val;
        });
        this.getContents();
      })
      .catch((err) => {
        console.log(err);
      });
  },
  filters: {
    uppercase(val) {
      return val.toUpperCase();
    },
  },
  computed: {
    publisherIds() {
      return this.highlight?.publisherIds !== undefined
        ? this.highlight.publisherIds
        : [];
    },
    content() {
      return this.highlights?.contentIds !== undefined
        ? this.highlight.contentIds
        : [];
    },
    content_field() {
      if (!['publishers', 'contents'].includes(this.highlight.type)) {
        return [
          { key: 'publishedDate', label: 'Published Date' },
          { key: 'unique_id', label: 'Unique ID' },
          { key: 'title', label: 'Title' },
          { key: 'language', label: 'Language' },
          { key: 'publisher', label: 'Publisher' },
          { key: 'action', sorter: false, filter: false, label: 'Action' },
        ];
      } else {
        return [
          { key: 'publishedDate', label: 'Published Date' },
          { key: 'unique_id', label: 'Unique ID' },
          { key: 'title', label: 'Title' },
          { key: 'language', label: 'Language' },
          { key: 'publisher', label: 'Publisher' },
        ];
      }
    },
  },
  watch: {
    highlight_contents: function (newVal) {
      if (newVal.length == 0) {
        this.pages = this.currentPage;
      } else {
        this.pages = this.currentPage + 1;
      }
    },
  },
  methods: {
    getContents() {
      this.currentPage = 1;
      this.pages = 2;
      this.$store
        .dispatch('highlightsContent', {
          highlight: parseInt(this.$route.params.id),
        })
        .then((res) => {
          let array = [];
          res.data.data.forEach((data) => {
            let time = data.publishedDate;
            let date = fromUnixTime(time);
            date = format(date, 'yyyy-MM-dd HH:mm');
            let pass = {
              unique_id: data.uniqueId ?? data.unique_id,
              title: data.title,
              publishedDate: date,
              language: data.language,
              disabled: data.disabled,
              publisher: data.publisher,
            };
            array.push(pass);
          });
          this.highlight_contents = array;
          this.loading = false;
        })
        .catch((err) => {
          console.log(err);
          alert('Something went wrong in getting highlights content');
          this.loading = false;
        });
    },
    resetQuery() {
      this.query = '';
      this.currentQuery;
      this.getContents();
    },
    hide(item) {
      this.$swal
        .fire({
          icon: 'question',
          html: '<p>Hide the content?</p><p>' + item.title + '</p>',
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            let pass = {
              id: this.$route.params.id,
              unique_id: item.unique_id,
            };
            this.$store
              .dispatch('hideHighlightContent', pass)
              .then(() => {
                this.$swal.fire({
                  icon: 'success',
                  text: 'Article is hidden successfully from highlight!',
                  showCancelButton: false,
                });
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'error',
                  text: 'Something went wrong in hiding article!',
                  showCancelButton: false,
                });
              });
          }
        });
    },
    unhide(item) {
      this.$swal
        .fire({
          icon: 'question',
          html: '<p>Show the content?</p><p>' + item.title + '</p>',
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            let pass = {
              id: this.$route.params.id,
              unique_id: item.unique_id,
            };
            this.$store
              .dispatch('unhideHighlightContent', pass)
              .then(() => {
                this.$swal.fire({
                  icon: 'success',
                  text: 'Article is shown successfully from highlight!',
                  showCancelButton: false,
                });
              })
              .catch(() => {
                this.$swal.fire({
                  icon: 'error',
                  text: 'Something went wrong in showing article!',
                  showCancelButton: false,
                });
              });
          }
        });
    },
    search() {
      this.currentPage = 1;
      this.pages = 2;
      this.currentQuery = this.query;
      if (this.currentQuery !== '') {
        let type = 'title';
        if (this.currentQuery.match(/^[A|V|P][0-9]{4}_./g)) {
          type = 'uniqueId';
        }
        this.loading = true;
        this.$store
          .dispatch('highlightsContent', {
            highlight: parseInt(this.$route.params.id),
            type: type,
            search: this.currentQuery,
          })
          .then((res) => {
            this.highlight_contents.splice(0);
            let array = [];
            res.data.data.forEach((data) => {
              let time = data.publishedDate;
              let date = fromUnixTime(time);
              date = format(date, 'yyyy-MM-dd HH:mm');
              let pass = {
                unique_id: data.uniqueId ?? data.unique_id,
                title: data.title,
                publishedDate: date,
                language: data.language,
                disabled: data.disabled,
                publisher: data.publisher,
              };
              array.push(pass);
            });
            this.highlight_contents = array;
            this.loading = false;
          });
      } else {
        alert('search box empty');
      }
    },
    searchPage() {
      let keyword = null;
      let type = null;
      if (this.currentQuery !== '') {
        keyword = this.currentQuery;
        if (this.currentQuery.match(/^[A|V|P][0-9]{4}_./g)) {
          type = 'uniqueId';
        } else {
          type = 'title';
        }
      }
      this.loading = true;
      this.$store
        .dispatch('highlightsContent', {
          highlight: parseInt(this.$route.params.id),
          type: type,
          search: keyword,
          page: this.currentPage,
        })
        .then((res) => {
          this.highlight_contents.splice(0);
          let array = [];
          res.data.data.forEach((data) => {
            let time = data.publishedDate;
            let date = fromUnixTime(time);
            date = format(date, 'yyyy-MM-dd HH:mm');
            let pass = {
              unique_id: data.uniqueId ?? data.unique_id,
              title: data.title,
              publishedDate: date,
              language: data.language,
              disabled: data.disabled,
              publisher: data.publisher,
            };
            array.push(pass);
          });
          this.highlight_contents = array;
          if (this.highlight_contents.length > 0) {
            this.pages += 1;
          }
          this.loading = false;
        });
    },
  },
};
</script>
