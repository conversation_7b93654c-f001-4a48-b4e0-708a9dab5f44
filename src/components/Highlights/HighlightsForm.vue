<template>
  <div>
    <CCard>
      <CCardHeader>{{
        edit ? 'Edit Highlight' : 'Create Highlight'
      }}</CCardHeader>
      <CCardBody>
        <CRow>
          <CCol sm="12" md="6" lg="6">
            <BaseHighlightForm
              :highlights="highlight"
              @show-display="passDisplay"
              @startDisabled="startDateDisabled"
              @endDisabled="endDateDisabled"
              @update:translation="setTranslations"
              @update:publisher="setPublisher"
              @update:content="setContent"
            />
          </CCol>
          <CCol sm="12" md="6" lg="6">
            <HighlightsPreview :highlights="highlight" :cover="displayCover" />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol>
            <CButton
              color="success"
              variant="outline"
              block
              @click="submitHighlight"
              :disabled="is_disabled"
              >{{ edit ? 'Update' : 'Submit' }}</CButton
            >
          </CCol>
        </CRow>
      </CCardBody>
      <OverlayLoader v-if="$store.getters.getHighlightsLoading" />
    </CCard>
  </div>
</template>
<script>
import { mapActions } from 'vuex';
import uslug from 'uslug';
import BaseHighlightForm from '@/components/Highlights/BaseHighlightForm';
import HighlightsPreview from '@/components/Highlights/HighlightsPreview';
import OverlayLoader from '../views/OverlayLoader';
import format from 'date-fns/format';
import compareAsc from 'date-fns/compareAsc';
import isBefore from 'date-fns/isBefore';
import parseISO from 'date-fns/parseISO';
export default {
  name: 'HighlightsForm',
  components: { BaseHighlightForm, HighlightsPreview, OverlayLoader },
  data() {
    return {
      edit: this.$route.params.id !== undefined,
      highlight: {
        feedId: [],
        isPinned: false,
        startDate: '',
        endDate: '',
        imageUrl: '',
        slug: '',
        type: '',
        languages: [],
        translations: {},
        publisherIds: [],
        contentIds: [],
        isCommentable: false,
      },
      displayCover: '',
      current_date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
      isStartDateDisabled: false,
      isEndDateDisabled: false,
    };
  },
  computed: {
    is_disabled() {
      let now = new Date();
      let errors = [];
      const notprio = ['slug'];
      Object.entries(this.highlight).forEach(([prop, val]) => {
        if (!notprio.includes(prop)) {
          if (prop === 'feedId') {
            if (val.length === 0 || val === []) {
              errors.unshift(prop);
            }
          } else if (prop === 'languages') {
            if (val.length === 0 || val === []) {
              errors.unshift(prop);
            }
          } else if (prop === 'translations') {
            for (const prop2 in this.highlight[prop]) {
              for (const field in this.highlight[prop][prop2]) {
                if (this.highlight[prop][prop2][field] === '') {
                  errors.unshift(`${prop2}_` + `${field}`);
                }
              }
            }
          } else if (prop === 'startDate') {
            if (
              isBefore(parseISO(this.highlight.startDate), now) &&
              !this.edit
            ) {
              errors.unshift(prop);
            } else if (
              compareAsc(parseISO(this.highlight.startDate), now) === -1 &&
              !this.isStartDateDisabled
            ) {
              errors.unshift(prop);
            } else if (this.highlight.startDate === '') {
              errors.unshift(prop);
            }
          } else if (prop === 'endDate') {
            if (
              compareAsc(
                parseISO(this.highlight.startDate),
                parseISO(this.highlight.endDate)
              ) === 1
            ) {
              errors.unshift(prop);
            } else if (
              compareAsc(
                parseISO(this.highlight.startDate),
                parseISO(this.highlight.endDate)
              ) === 0
            ) {
              errors.unshift(prop);
            } else if (
              compareAsc(parseISO(this.highlight.endDate), now) === -1 &&
              !this.isEndDateDisabled
            ) {
              errors.unshift(prop);
            } else if (val === '') {
              errors.unshift(prop);
            }
          } else if (prop === 'contentIds') {
            if (this.highlight.type === 'content' && val.length === 0) {
              errors.unshift(prop);
            }
          } else if (prop === 'publisherIds') {
            if (this.highlight.type === 'publishers' && val.length === 0) {
              errors.unshift(prop);
            }
          } else if (val === '') {
            errors.unshift(prop);
          }
        }
      });
      if (this.edit) {
        let compare = compareAsc(
          parseISO(this.current_date),
          parseISO(this.highlight.endDate)
        );
        return errors.length > 0 || compare !== -1;
      } else {
        return errors.length > 0;
      }
    },
  },
  async created() {
    if (this.$store.getters.getFeeds.length === 0) {
      await this.listFeeds();
    }
    if (this.$route.params.id !== undefined) {
      this.currentHighlight(parseInt(this.$route.params.id))
        .then((res) => {
          const response = res[0];
          Object.entries(response).forEach(([prop, val]) => {
            if (prop === 'publisherIds') {
              this.highlight[prop] = response[prop].map((i) => {
                return { value: parseInt(i.value), label: i.label };
              });
            } else {
              this.highlight[prop] = val;
            }
          });
          this.displayCover = this.highlight.imageUrl;
        })
        .catch((err) => {
          console.log(err);
        });
    }
  },
  methods: {
    ...mapActions({
      createHighlight: 'createHighlight',
      updateHighlight: 'updateHighlight',
      currentHighlight: 'listHighlightsById',
      listFeeds: 'listFeeds',
    }),
    async submitHighlight() {
      let pass = {};
      Object.entries(this.highlight).forEach(([prop, val]) => {
        if (['feedId', 'publisherIds', 'contentIds'].includes(prop)) {
          let newArray = [];
          this.highlight[prop].forEach((item) => {
            newArray.unshift(item.value);
          });
          pass[prop] = newArray;
        } else {
          pass[prop] = val;
        }
      });
      if (this.edit) {
        this.updateHighlight(pass)
          .then(() => {
            this.$swal
              .fire({
                icon: 'success',
                text: 'Highlight successfully updated!',
                showCancelButton: false,
              })
              .then((res) => {
                if (res.isConfirmed) {
                  this.$router.push('/highlights');
                }
              });
          })
          .catch((err) => {
            let message = 'Something went wrong in updating highlight';
            if (err.response.status == 401) {
              message = 'Unauthorized. Check console for more info';
            }
            if (err.response.status == 404) {
              message = err.response.data.message;
            }

            if (err.response.status == 400) {
              message = err.response.data.message;
            }
            this.$swal.fire({
              icon: 'error',
              text: message,
              showCancelButton: false,
            });
          });
      } else {
        pass.slug = await this.createSlug(pass.translations);
        this.createHighlight(pass)
          .then(() => {
            this.$swal
              .fire({
                icon: 'success',
                text: 'Highlight successfully created!',
                showCancelButton: false,
              })
              .then((res) => {
                if (res.isConfirmed) {
                  this.$router.push('/highlights');
                }
              });
          })
          .catch((err) => {
            console.log(err);
            let message = 'Something went wrong in creating highlight';
            if (err.response.status == 401) {
              message = 'Unauthorized. Check console for more info';
            }
            if (err.response.status == 404) {
              message = err.response.data.message;
            }

            if (err.response.status == 400) {
              message = err.response.data.message;
            }
            this.$swal.fire({
              icon: 'error',
              text: message,
              showCancelButton: false,
            });
          });
      }
    },
    passDisplay(item) {
      this.displayCover = item.data;
      this.highlight.imageUrl = item.data;
    },
    generatePostfix() {
      let randomChars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      while (!/^(?=.*[A-Z])(?=.*\d).{6,}$/.test(result)) {
        result = '';
        for (let i = 0; i < 6; i++) {
          result += randomChars.charAt(
            Math.floor(Math.random() * randomChars.length)
          );
        }
      }
      return result;
    },
    startDateDisabled(payload) {
      this.isStartDateDisabled = payload;
    },
    endDateDisabled(payload) {
      this.isEndDateDisabled = payload;
    },
    setTranslations(val) {
      this.highlight.translations = val;
    },
    setPublisher(val) {
      this.highlight.publisherIds = val;
    },
    setContent(val) {
      this.highlight.contentIds = val;
    },
    async createSlug(translations) {
      const languages = ['en', 'ms', 'zh'];
      const rand = await this.generatePostfix();
      let title = '';
      for (const lang of languages) {
        if (translations?.[lang] !== undefined && translations[lang].title) {
          title = translations[lang].title;
          break;
        }
      }
      const slug = title + ' ' + rand;
      return uslug(slug, slug);
    },
  },
};
</script>
