<template>
  <div>
    <div class="form-check form-check-inline pin-highlight-margin">
      <input
        type="checkbox"
        class="form-check-input"
        id="pinHighlight"
        v-model="highlights.isPinned"
      />
      <label for="pinHighlight" class="form-check-label">Pin <PERSON></label>
    </div>

    <div class="form-check form-check-inline pin-highlight-margin">
      <input
        type="checkbox"
        class="form-check-input"
        id="isCommentable"
        v-model="highlights.isCommentable"
      />
      <label for="isCommentable" class="form-check-label"
        >Enable Comments</label
      >
    </div>

    <CRow class="mb-3">
      <CCol lg="6" md="12" sm="12">
        <label for="">Start Time</label>
        <vc-date-picker
          v-model="highlights.startDate"
          mode="dateTime"
          :masks="{ inputDateTime24hr: 'YYYY-MM-DD HH:mm:ss' }"
          :model-config="{ type: 'string', mask: 'YYYY-MM-DD HH:mm:ss' }"
          :min-date="min_start_date"
          is24hr
        >
          <template v-slot="{ inputValue, inputEvents }">
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text">
                  <CIcon name="cil-calendar"></CIcon>
                </span>
              </div>
              <input
                :disabled="disable_start"
                class="form-control"
                :value="inputValue"
                v-on="!disable_start ? inputEvents : {}"
              />
            </div>
          </template>
        </vc-date-picker>
        <span
          v-if="start_errors && !disable_start"
          v-text="start_errors_text"
          class="invalid-error"
        />
      </CCol>
      <CCol lg="6" md="12" sm="12">
        <label for="">End Time</label>
        <vc-date-picker
          v-model="highlights.endDate"
          mode="dateTime"
          :masks="{ inputDateTime24hr: 'YYYY-MM-DD HH:mm:ss' }"
          :model-config="{ type: 'string', mask: 'YYYY-MM-DD HH:mm:ss' }"
          :min-date="min_start_date"
          is24hr
        >
          <template v-slot="{ inputValue, inputEvents }">
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text">
                  <CIcon name="cil-calendar"></CIcon>
                </span>
              </div>
              <input
                :disabled="disableEdit"
                class="form-control"
                :value="inputValue"
                v-on="!disableEdit ? inputEvents : {}"
              />
            </div>
            <span
              v-if="end_errors && !disableEdit"
              v-text="end_error_text"
              class="invalid-error"
            />
          </template>
        </vc-date-picker>
      </CCol>
    </CRow>
    <div class="d-flex flex-column">
      <div class="mb-2">Language</div>
      <div class="d-flex flex-row">
        <div
          class="form-check form-check-inline"
          v-for="(option, optionIndex) in languageField"
          :key="option.value + optionIndex + 'lang'"
        >
          <input
            type="checkbox"
            class="form-check-input"
            :id="option.value + optionIndex + 'lang'"
            :value="option.value"
            v-model="highlights.languages"
          />
          <label
            :for="option.value + optionIndex + 'lang'"
            class="form-check-label"
            >{{ option.label }}</label
          >
        </div>
      </div>
      <span
        v-if="highlights.languages.length === 0"
        v-text="'Please select at least 1 language'"
        class="invalid-error"
      />
    </div>
    <br />
    <div class="d-flex flex-column">
      <div class="mb-2">Type</div>
      <div class="d-flex flex-row">
        <div
          class="form-check form-check-inline"
          v-for="(option, optionIndex) in highlightTypes"
          :key="option.value + optionIndex + 'type'"
        >
          <input
            type="radio"
            class="form-check-input"
            :id="option.value + optionIndex + 'type'"
            :value="option.value"
            v-model="highlights.type"
          />
          <label
            :for="option.value + optionIndex + 'type'"
            class="form-check-label"
            >{{ option.label }}</label
          >
        </div>
      </div>
      <span
        v-if="highlights.type === ''"
        v-text="'Please select highlight type'"
        class="invalid-error"
      />
    </div>
    <br />
    <CRow v-for="(item, name) in highlights.translations" :key="name">
      <CCol>
        <CCard>
          <CCardHeader>{{ name.toUpperCase() }}</CCardHeader>
          <CCardBody>
            <CInput
              :disabled="disableEdit"
              v-model="highlights.translations[name].title"
              label="Title"
              :is-valid="highlights.translations[name].title !== ''"
              invalid-feedback="Please enter the highlight title"
              placeholder="Max 20 EN/MS characters OR Max 10 ZH characters"
              :description="
                highlights.translations[name].title.length + ' Characters'
              "
            />
            <CTextarea
              :disabled="disableEdit"
              v-model="highlights.translations[name].description"
              label="Description"
              :is-valid="highlights.translations[name].description !== ''"
              placeholder="Max 75 EN/MS characters OR Max 35 ZH characters"
              invalid-feedback="Please enter the highlight description"
              :description="
                highlights.translations[name].description.length + ' Characters'
              "
            />
            <CInput
              v-if="
                highlights.type === 'keywords' &&
                highlights.translations[name].hasOwnProperty('keywords')
              "
              :disabled="disableEdit"
              v-model="highlights.translations[name].keywords"
              label="Keywords"
              :is-valid="highlights.translations[name].keywords !== ''"
              placeholder="Enter 1 or more keywords"
              invalid-feedback="Please enter at least ONE keyword"
            />
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
    <div>
      <label for="adCoverFile1">Highlight Image</label>
      <div>
        <label class="custom-file-upload">
          <input
            :disabled="disableEdit"
            type="file"
            @change="onFileChange($event)"
            id="adCoverFile1"
            ref="inputCover"
            class="form-control-file"
            data-content="cover"
            accept="video/mp4,image/*"
          />
          <i class="fa fa-cloud-upload"></i> Upload
        </label>
      </div>
      <span
        v-if="highlights.imageUrl === ''"
        v-text="'Please upload a featured image'"
        class="invalid-error"
      />
    </div>
    <CRow>
      <CCol lg="12" md="12" sm="12">
        <label for="feedOpt">Feed</label>
        <multiselect
          :disabled="disableEdit"
          :class="[{ 'invalid-border': highlights.feedId.length === 0 }]"
          id="feedOpt"
          v-model="highlights.feedId"
          :multiple="true"
          track-by="value"
          label="label"
          :options="feeds"
          placeholder=""
          @input="onChangeFeed"
        >
          <span slot="noResult"
            >Oops! No Feed with the name. Consider changing the search
            query.</span
          >
        </multiselect>
        <span
          v-if="highlights.feedId.length === 0"
          v-text="'Please select at least 1 feed'"
          class="invalid-error"
        />
      </CCol>
    </CRow>
    <CRow v-if="highlights.type === 'publishers'">
      <CCol lg="12" md="12" sm="12">
        <label for="ajax">Publisher</label>
        <multiselect
          :disabled="disableEdit"
          :class="[{ 'invalid-border': highlights.publisherIds.length === 0 }]"
          v-model="highlights.publisherIds"
          :multiple="true"
          track-by="value"
          label="label"
          :options="publisherList"
          :loading="isLoading"
          placeholder=""
          id="ajax"
          :searchable="true"
          :internal-search="false"
          :close-on-select="false"
          @search-change="asyncFindPublisher"
        >
          <span slot="noResult"
            >Oops! No Publisher with the name. Consider changing the search
            query.</span
          >
        </multiselect>
        <span
          v-if="highlights.publisherIds.length === 0"
          v-text="'Please select at least 1 publisher'"
          class="invalid-error"
        />
      </CCol>
    </CRow>
    <CRow v-if="highlights.type === 'contents'">
      <CCol>
        <label for="contentIds">Content IDs</label>
        <multiselect
          :disabled="disableEdit"
          :class="[{ 'invalid-border': highlights.contentIds.length === 0 }]"
          v-model="highlights.contentIds"
          :multiple="true"
          track-by="value"
          label="label"
          :options="contentIds"
          placeholder=""
          id="contentIds"
          :taggable="true"
          @tag="addContentTag"
        >
        </multiselect>
        <span
          v-if="highlights.contentIds.length === 0"
          v-text="'Please enter at least ONE content ID'"
          class="invalid-error"
        />
      </CCol>
    </CRow>
    <OverlayLoader v-if="loading" />
  </div>
</template>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>

<script>
import slugify from 'slugify';
import axios from 'axios';
import format from 'date-fns/format';
import Bugsnag from '@bugsnag/js';
import Multiselect from 'vue-multiselect';
// import DateRangePicker from "vue2-daterange-picker";
// import "vue2-daterange-picker/dist/vue2-daterange-picker.css";
import OverlayLoader from '../views/OverlayLoader';
import compareAsc from 'date-fns/compareAsc';
import { isBefore, parseISO, isFuture } from 'date-fns';
import _ from 'lodash';

export default {
  name: 'BaseHighlightForm',
  components: { Multiselect, OverlayLoader },
  data() {
    return {
      now: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
      edit: this.$route.params.id !== undefined,
      current_end_date: JSON.parse(JSON.stringify(this.highlights)).endDate,
      initialStartDate: this.highlights.startDate,
      initialEndDate: this.highlights.endDate,
      languageField: [
        { value: 'en', label: 'EN' },
        { value: 'ms', label: 'MS' },
        { value: 'zh', label: 'ZH' },
      ],
      highlightTypes: [
        { value: 'keywords', label: 'Keywords' },
        { value: 'publishers', label: 'Publishers' },
        { value: 'contents', label: 'Content IDs' },
      ],
      isLoading: false,
      publisherList: [],
      limitSearch: 10,
    };
  },
  props: {
    highlights: {
      type: Object,
      required: true,
    },
  },
  computed: {
    contentIds() {
      return this.highlights?.contentIds ? this.highlights.contentIds : [];
    },
    loading() {
      return this.$store.getters.getLoading;
    },
    disable_start() {
      if (this.edit && this.initialStartDate === this.highlights.startDate) {
        let res = isBefore(
          parseISO(this.highlights.startDate),
          parseISO(this.now)
        );
        this.$emit('startDisabled', res);
        return res;
      }
      return false;
    },
    disableEdit() {
      if (this.edit && this.initialEndDate === this.highlights.endDate) {
        let res = isBefore(
          parseISO(this.highlights.endDate),
          parseISO(this.now)
        );
        this.$emit('endDisabled', res);
        return res;
      }
      return false;
    },
    min_start_date() {
      let date = new Date();
      if (this.edit) {
        if (isBefore(parseISO(this.highlights.startDate), parseISO(this.now))) {
          date = this.highlights.startDate;
        }
      }
      return date;
    },
    feeds() {
      let use = [];
      let feed = this.$store.getters.getFeeds;
      feed.forEach((f) => {
        let data = {
          value: f.id,
          label: f.name,
        };
        use.unshift(data);
      });
      return use.sort((a, b) => a.label.localeCompare(b.label));
    },
    start_errors() {
      if (isBefore(parseISO(this.highlights.startDate), parseISO(this.now))) {
        return true;
      } else if (this.highlights.startDate === '') {
        return true;
      } else {
        return false;
      }
    },
    start_errors_text() {
      if (isBefore(parseISO(this.highlights.startDate), parseISO(this.now))) {
        return 'Start time must be AFTER current time';
      } else if (this.highlights.startDate === '') {
        return 'Please select a start time';
      } else {
        return '';
      }
    },
    end_errors() {
      if (
        isBefore(
          parseISO(this.highlights.endDate),
          parseISO(this.highlights.startDate)
        )
      ) {
        return true;
      } else if (
        compareAsc(
          parseISO(this.highlights.endDate),
          parseISO(this.highlights.startDate)
        ) === 0
      ) {
        return true;
      } else if (
        compareAsc(parseISO(this.highlights.endDate), parseISO(this.now)) === -1
      ) {
        return true;
      } else if (this.highlights.endDate === '') {
        return true;
      } else {
        return false;
      }
    },
    end_error_text() {
      if (
        isBefore(
          parseISO(this.highlights.endDate),
          parseISO(this.highlights.startDate)
        )
      ) {
        return 'End time must be AFTER start time';
      } else if (
        compareAsc(
          parseISO(this.highlights.endDate),
          parseISO(this.highlights.startDate)
        ) === 0
      ) {
        return 'End time must be AFTER start time';
      } else if (
        compareAsc(parseISO(this.highlights.endDate), parseISO(this.now)) === -1
      ) {
        return 'End time must be AFTER current time';
      } else if (this.highlights.endDate === '') {
        return 'Please select an end time';
      } else {
        return '';
      }
    },
  },
  mounted() {
    this.isLoading = true;
    this.$store
      .dispatch('listHighlightPublishers')
      .then((res) => {
        this.publisherList = res.data;
        this.isLoading = false;
      })
      .catch(() => {
        this.isLoading = false;
      });
  },
  methods: {
    endValidHours(selectedHour) {
      if (!isFuture(parseISO(this.highlights.endDate))) {
        const startHours = new Date(this.now).getHours();
        return selectedHour >= startHours;
      }
      return [];
    },
    addContentTag(newTag) {
      const tag = {
        value: newTag,
        label: newTag,
      };
      this.highlights.contentIds.push(tag);
    },
    asyncFindPublisher: _.debounce(function (query) {
      let find = null;
      if (query !== '') {
        find = { name: encodeURI(query) };
      }
      this.isLoading = true;
      this.$store
        .dispatch('listHighlightPublishers', find)
        .then((res) => {
          this.publisherList = res.data;
          this.isLoading = false;
        })
        .catch((err) => {
          console.log(err);
          this.isLoading = false;
        });
    }, 600),
    onChangeFeed(input) {
      if (input.length > 0) {
        if (
          input[input.length - 1].value === '0' ||
          input[input.length - 1].value === 'F_F_ALL'
        ) {
          let data = input[input.length - 1];
          this.highlights.feedId = [];
          this.highlights.feedId.push(data);
        } else {
          if (input.find((element) => element.value === '0') != null) {
            input = input.filter(function (item) {
              return item.value !== '0';
            });
            this.highlights.feedId = [];
            this.highlights.feedId = input;
          }
          if (input.find((element) => element.value === 'F_F_ALL') != null) {
            input = input.filter(function (item) {
              return item.value !== 'F_F_ALL';
            });
            this.highlights.feedId = [];
            this.highlights.feedId = input;
          }
        }
      }
    },
    onFileChange(event) {
      let input = event.target;
      if (input.files && input.files[0]) {
        let type = input.files[0].type;
        if (!type.includes('gif')) {
          let iSize = input.files[0].size / 1000 / 1000;
          if (iSize <= 1) {
            this.getCoverDimension(input);
          } else {
            //display warning
            alert('File size exceeded. Max 1mb for Image');
            console.log('file too big');
            this.$refs.inputCover.value = null;
          }
        } else {
          alert('No GIFs allowed');
        }
      }
    },
    getCoverDimension(input) {
      let file = input.files[0];
      if (!file || file.type.indexOf('image/') !== 0) {
        alert('image not found');
      } else {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (evt) => {
          let img = new Image();
          img.onload = () => {
            this.googleUpload(input);
          };
          img.src = evt.target.result;
        };
        reader.onerror = (evt) => {
          console.error(evt);
        };
      }
    },
    googleUpload: function (input) {
      let fileName = slugify(input.files[0].name);
      let fileData = new FormData();
      fileData.append('path', 'adwav/highlight/temp');
      fileData.append('file', input.files[0]);
      fileData.append('name', fileName);
      this.$store.commit('SET_GENERAL_LOADING', true);
      axios
        .post(process.env.VUE_APP_UPLOAD_FILE_TO_GCS, fileData)
        .then((res) => {
          this.$emit('show-display', { data: res.data.url });
          this.$store.commit('SET_GENERAL_LOADING', false);
        })
        .catch((error) => {
          this.$store.commit('SET_GENERAL_LOADING', false);
          alert('Something went wrong with uploading media');
          console.log('----Error----');
          console.log(error);
          Bugsnag.notify(error);
        });
    },
    labelAndSort(array) {
      let use = [];
      array.forEach((i) => {
        use.unshift({ label: i, value: i.toUpperCase() });
      });
      return use.sort((a, b) => a.label.localeCompare(b.label));
    },
  },
  watch: {
    'highlights.languages': function (newVal) {
      const langs = this.labelAndSort(newVal);
      let lang = {};
      langs.forEach((item) => {
        lang[item.label] = {
          title:
            this.highlights.translations?.[item.label] !== undefined
              ? this.highlights.translations[item.label].title
              : '',
          description:
            this.highlights.translations?.[item.label] !== undefined
              ? this.highlights.translations[item.label].description
              : '',
          keywords:
            this.highlights.translations?.[item.label] !== undefined
              ? this.highlights.translations[item.label].keywords
              : '',
        };
        if (this.highlights.type !== 'keywords') {
          delete lang[item.label].keywords;
        }
      });
      this.$emit('update:translation', lang);
    },
    'highlights.type': function (newVal, oldVal) {
      if (oldVal !== newVal) {
        if (newVal === 'keywords') {
          const langs = this.labelAndSort(this.highlights.languages);
          let use = {};
          langs.forEach((item) => {
            use[item.label] = {
              title: this.highlights.translations[item.label].title,
              description: this.highlights.translations[item.label].description,
              keywords:
                this.highlights.translations[item.label]?.keywords !== undefined
                  ? this.highlights.translations[item.label].keywords
                  : '',
            };
          });
          this.$emit('update:translation', use);
          this.$emit('update:publisher', []);
          this.$emit('update:content', []);
        } else if (newVal === 'contents') {
          const langs = this.labelAndSort(this.highlights.languages);
          let use = {};
          langs.forEach((item) => {
            use[item.label] = {
              title: this.highlights.translations[item.label].title,
              description: this.highlights.translations[item.label].description,
            };
          });
          const content =
            this.highlights?.contentIds !== undefined
              ? this.highlights.contentIds
              : [];
          this.$emit('update:translation', use);
          this.$emit('update:publisher', []);
          this.$emit('update:content', content);
        } else {
          const langs = this.labelAndSort(this.highlights.languages);
          let use = {};
          langs.forEach((item) => {
            use[item.label] = {
              title: this.highlights.translations[item.label].title,
              description: this.highlights.translations[item.label].description,
            };
          });
          const publisher =
            this.highlights?.publisherIds !== undefined
              ? this.highlights.publisherIds
              : [];
          this.$emit('update:translation', use);
          this.$emit('update:publisher', publisher);
          this.$emit('update:content', []);
        }
      }
    },
    'highlights.startDate': function (newVal, oldVal) {
      if (oldVal == '' && oldVal != newVal) {
        this.initialStartDate = this.highlights.startDate;
      }
    },
    'highlights.endDate': function (newVal, oldVal) {
      if (oldVal == '' && oldVal != newVal) {
        this.initialEndDate = this.highlights.endDate;
      }
    },
  },
};
</script>

<style scoped>
small {
  font-size: 100% !important;
}
.invalid-border {
  border: 1px solid #e55353;
  border-radius: 6px;
}
.invalid-error {
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #e55353;
}
input[type='file'] {
  display: none;
}

.custom-file-upload {
  border: 1px solid #ccc;
  display: inline-block;
  padding: 6px 12px;
  cursor: pointer;
}

.pin-highlight-margin {
  margin-bottom: 8px;
}
</style>
