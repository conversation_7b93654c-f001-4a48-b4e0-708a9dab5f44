<template>
  <div class="d-flex justify-content-center">
    <div class="card text-white base-card">
      <img
        v-if="display_cover !== ''"
        :src="display_cover"
        class="card-img"
        :alt="title"
      />
      <div v-else class="card-img empty-img"></div>
      <div class="card-img-overlay highlight-overlay">
        <span class="highlight-title">{{ title }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HighlightsPreview',
  props: ['highlights', 'cover'],
  data() {
    return {};
  },
  computed: {
    display_cover() {
      return this.cover;
    },
    title() {
      let title = 'Title here';
      if (
        this.highlights.translations?.en !== undefined &&
        this.highlights.translations.en.title !== ''
      ) {
        title = this.highlights.translations.en.title;
      } else if (
        this.highlights.translations?.ms !== undefined &&
        this.highlights.translations.ms.title !== ''
      ) {
        title = this.highlights.translations.ms.title;
      } else if (
        this.highlights.translations?.zh !== undefined &&
        this.highlights.translations.zh.title !== ''
      ) {
        title = this.highlights.translations.zh.title;
      }
      return title;
    },
  },
};
</script>
<style scoped>
.base-card {
  height: 200px;
  width: 265px;
  border-radius: 6px !important;
}
.base-card img {
  object-fit: cover;
  height: inherit;
  width: 100%;
}
.highlight-overlay {
  height: 80px;
  background-image: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 0.7),
    rgba(0, 0, 0, 1)
  );
  margin-top: 120px;
  border-bottom-left-radius: 6px !important;
  border-bottom-right-radius: 6px !important;
}
.highlight-title {
  font-size: 16px;
  font-weight: 700;
  line-height: 19px;
  text-overflow: ellipsis;
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 2;
  /* number of lines to show */
  -webkit-box-orient: vertical;
}
.empty-img {
  background: lightblue;
  height: inherit;
  border-radius: 6px;
}
</style>
