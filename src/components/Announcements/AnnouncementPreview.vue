<template>
  <div>
    <div class="d-flex flex-row justify-content-center">
      <div
        class="d-flex flex-row flex-wrap wrapper"
        :style="{ backgroundColor: announcement.backgroundColor }"
      >
        <div class="d-flex flex-column mr-auto">
          <div class="ann-title" :style="{ color: announcement.textColor }">
            {{
              announcement.languages.en.title !== ''
                ? announcement.languages.en.title
                : 'Announcement Title'
            }}
          </div>
          <div class="ann-body" :style="{ color: announcement.textColor }">
            {{
              announcement.languages.en.body_text !== ''
                ? announcement.languages.en.body_text
                : 'Announcement Body'
            }}
          </div>
          <div class="ann-link" v-if="announcement.languages.en.link !== ''">
            <a :href="announcement.languages.en.link" target="_blank"
              >Read More</a
            >
          </div>
        </div>
        <div style="padding-top: 1px; padding-right: 5px">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 14 14"
            :style="{ fill: announcement.textColor }"
          >
            <g transform="translate(0 0)">
              <path
                class="a"
                d="M7,14a7,7,0,1,1,7-7A7.008,7.008,0,0,1,7,14ZM4.7,3.952a.75.75,0,0,0-.531,1.28L5.939,7,4.172,8.768a.75.75,0,1,0,1.061,1.06L7,8.06,8.768,9.828a.75.75,0,1,0,1.06-1.06L8.06,7,9.828,5.233a.75.75,0,0,0-1.06-1.061L7,5.939,5.233,4.172A.747.747,0,0,0,4.7,3.952Z"
              />
            </g>
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'AnnouncementPreview',
  props: {
    announcement: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style scoped>
.wrapper {
  border: 1px solid #dfdfdf;
  border-radius: 6px;
  width: 340px;
  min-height: 106px;
  padding-left: 15px;
  padding-bottom: 14px;
}
.ann-title {
  font-weight: 800;
  line-height: 20px;
  font-size: 17px;
  margin-bottom: 5px;
  margin-top: 12px;
}
.ann-body {
  inline-size: 300px;
  font-size: 14px;
  line-height: 18px;
  font-weight: 200;
  overflow-wrap: break-word;
}
.ann-link {
  font-size: 14px;
  line-height: 16px;
  font-weight: 600;
  text-decoration: underline;
}

.ann-link a {
  color: #ec3535;
}
</style>
