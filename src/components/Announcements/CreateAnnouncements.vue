<template>
  <CCard>
    <CCardHeader>{{
      edit ? 'Edit Announcement' : 'Create Announcement'
    }}</CCardHeader>
    <CCardBody>
      <CRow>
        <CCol sm="12" md="8" lg="8">
          <BaseAnnouncementForm :announcement="announcement" />
        </CCol>
        <CCol sm="12" md="4" lg="4">
          <AnnouncementPreview :announcement="announcement" />
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CButton
            color="success"
            variant="outline"
            block
            @click="createAnnouncement"
            :disabled="is_disabled"
            >{{ edit ? 'Update' : 'Submit' }}</CButton
          >
        </CCol>
      </CRow>
    </CCardBody>
    <OverlayLoader v-if="$store.getters.getAnnLoading" />
  </CCard>
</template>
<script>
import OverlayLoader from '../views/OverlayLoader';
import BaseAnnouncementForm from '@/components/Announcements/BaseAnnouncementForm';
import AnnouncementPreview from '@/components/Announcements/AnnouncementPreview';
import format from 'date-fns/format';
import compareAsc from 'date-fns/compareAsc';
import parseISO from 'date-fns/parseISO';
import { GENERAL } from '@/constant/constants';

export default {
  name: 'CreateAnnouncements',
  components: { BaseAnnouncementForm, OverlayLoader, AnnouncementPreview },
  data() {
    return {
      edit: this.$route.params.id !== undefined,
      announcement: {
        feed_id: [],
        priority: '',
        start_date: '',
        end_date: '',
        languages: {
          en: { title: '', body_text: '', link: '' },
          ms: { title: '', body_text: '', link: '' },
          zh: { title: '', body_text: '', link: '' },
        },
        backgroundColor: GENERAL.default_announcement_background_color,
        textColor: GENERAL.default_announcement_text_color,
      },
      current_date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
    };
  },
  computed: {
    is_disabled() {
      let now = new Date();
      let errors = [];
      const notprio = ['link'];
      Object.entries(this.announcement).forEach(([prop, val]) => {
        if (prop === 'languages') {
          for (const prop2 in this.announcement[prop]) {
            for (const field in this.announcement[prop][prop2]) {
              if (
                !notprio.includes(field) &&
                this.announcement[prop][prop2][field] === ''
              ) {
                errors.unshift(`${prop2}_` + `${field}`);
              }
            }
          }
        } /* else if (prop === "start_date") {
          if (compareAsc(parseISO(this.announcement.start_date), now) === -1 ) {
            errors.unshift("start_date");
          } else if (val === "") {
            errors.unshift("start_date");
          }
        }*/ else if (prop === 'end_date') {
          if (
            compareAsc(
              parseISO(this.announcement.start_date),
              parseISO(this.announcement.end_date)
            ) === 1
          ) {
            errors.unshift('end_date');
          } else if (
            compareAsc(now, parseISO(this.announcement.end_date)) === 1
          ) {
            errors.unshift('end_date');
          } else if (val === '') {
            errors.unshift('end_date');
          }
        } else if (val === '') {
          errors.unshift(prop);
        }
      });
      if (this.edit) {
        let compare = compareAsc(
          parseISO(this.current_date),
          parseISO(this.announcement.end_date)
        );
        return errors.length > 0 || compare !== -1;
      } else {
        return errors.length > 0;
      }
    },
  },
  created() {
    if (this.$store.getters.getFeeds.length === 0) {
      this.$store.dispatch('listFeeds');
    }
    if (this.$route.params.id !== undefined) {
      this.$store
        .dispatch('getAnnouncementById', parseInt(this.$route.params.id))
        .then((res) => {
          Object.entries(res.data[0]).forEach(([prop, val]) => {
            this.announcement[prop] = val;
          });
        })
        .catch((err) => {
          console.log(err);
        });
    }
  },
  methods: {
    createAnnouncement() {
      let pass = {};
      Object.entries(this.announcement).forEach(([prop, val]) => {
        if (prop === 'feed_id') {
          let newFeed = [];
          this.announcement[prop].forEach((item) => {
            newFeed.unshift(item.value);
          });
          pass[prop] = newFeed;
        } else {
          pass[prop] = val;
        }
      });
      if (this.edit) {
        this.$store
          .dispatch('updateAnnouncement', pass)
          .then(() => {
            this.$swal
              .fire({
                icon: 'success',
                text: 'Announcement successfully updated!',
                showCancelButton: false,
              })
              .then((res) => {
                if (res.isConfirmed) {
                  this.$router.push('/announcements');
                }
              });
          })
          .catch(() => {
            this.$swal.fire({
              icon: 'error',
              text: 'Something went wrong in updating announcements',
              showCancelButton: false,
            });
          });
      } else {
        this.$store
          .dispatch('createAnnouncement', pass)
          .then(() => {
            this.$swal
              .fire({
                icon: 'success',
                text: 'Announcement successfully created!',
                showCancelButton: false,
              })
              .then((res) => {
                if (res.isConfirmed) {
                  this.$router.push('/announcements');
                }
              });
          })
          .catch(() => {
            this.$swal.fire({
              icon: 'error',
              text: 'Something went wrong in creating announcements',
              showCancelButton: false,
            });
          });
      }
    },
  },
};
</script>
