<template>
  <CCard>
    <CCardHeader>List of Announcements</CCardHeader>
    <CCardBody>
      <CRow>
        <CCol>
          <router-link :to="'announcements/upsert'">
            <CButton
              v-show="checkAction('announcement_add')"
              class="float-right mb-2"
              variant="outline"
              color="success"
              shape="pill"
            >
              Add Announcement
            </CButton>
          </router-link>
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CDataTable
            :loading="$store.getters.getAnnLoading"
            :key="announcementList.id"
            small
            :sorter-value="sorters"
            :sorter="{ external: true, resetable: false }"
            pagination
            :fields="list_fields"
            :items="announcementList"
            @update:sorter-value="onSorterChange"
          >
            <template #show_details="{ item }">
              <td>
                <CButtonToolbar>
                  <router-link :to="'announcements/upsert/' + item.id">
                    <CButton
                      v-show="checkAction('announcement_edit')"
                      color="warning"
                      v-c-tooltip="'Edit Announcement'"
                      variant="outline"
                      ><CIcon name="cil-pencil"></CIcon
                    ></CButton>
                  </router-link>
                  <CButton
                    v-show="
                      checkAction('announcement_delete') && !editable(item)
                    "
                    @click="deleteAnn(item)"
                    variant="outline"
                    color="danger"
                    v-c-tooltip="'Remove Announcement'"
                    ><CIcon name="cil-trash"></CIcon
                  ></CButton>
                </CButtonToolbar>
              </td>
            </template>
          </CDataTable>
          <CPagination
            @update:activePage="goToPage"
            :pages="pagination.last_page"
            :activePage.sync="pagination.current_page"
          />
        </CCol>
      </CRow>
    </CCardBody>
  </CCard>
</template>

<script>
import compareAsc from 'date-fns/compareAsc';
import parseISO from 'date-fns/parseISO';
import format from 'date-fns/format';
import { mapGetters, mapActions } from 'vuex';
export default {
  name: 'ListAnnoucements',
  data() {
    return {
      list_fields: [
        { key: 'start_date', label: 'Start Time' },
        { key: 'end_date', label: 'End Time' },
        { key: 'titleShow', sorter: true, label: 'Title' },
        { key: 'descShow', sorter: true, label: 'Desc' },
        { key: 'feedShow', sorter: false, label: 'Feed' },
        { key: 'prioShow', sorter: true, label: 'Priority' },
        { key: 'linkShow', sorter: true, label: 'Link' },
        { key: 'show_details', sorter: false, filter: false, label: 'Actions' },
      ],
      current_date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
      sorters: { column: 'end_date', asc: false },
      columnMapping: {
        start_date: 'announcements.start_date',
        end_date: 'announcements.end_date',
        titleShow: 'announcements_lang.title',
        descShow: 'announcements_lang.body_text',
        prioShow: 'announcements.priority',
        linkShow: 'announcements_lang.link',
      },
    };
  },
  filters: {
    fill(val) {
      if (val !== '') {
        return val;
      } else {
        return '-';
      }
    },
  },
  computed: {
    ...mapGetters({
      announcementList: 'getAnnList',
      pagination: 'getAnnouncementPagination',
    }),
  },
  async created() {
    await this.$store.dispatch('listFeeds');
    this.listAnnouncements();
  },
  methods: {
    ...mapActions({
      listAnnouncements: 'listAnnouncements',
    }),
    async onSorterChange(updatedSorter) {
      this.sorters = updatedSorter;
      let sortBy = this.sorters.column;
      let asc = this.sorters.asc;
      const mappedSortBy = this.columnMapping[sortBy] || null;
      this.listAnnouncements({ sortBy: mappedSortBy, asc: asc });
    },
    deleteAnn(item) {
      this.$swal
        .fire({
          icon: 'question',
          html:
            '<p>Remove this announcement?</p><p>' +
            item.languages.en.title +
            '</p>',
          confirmButtonText: 'Yes',
        })
        .then((res) => {
          if (res.isConfirmed) {
            this.$store.dispatch('removeAnnouncement', item);
          }
        });
    },
    editable(item) {
      if (
        compareAsc(parseISO(this.current_date), parseISO(item.end_date)) === -1
      ) {
        return false;
      }
      return true;
    },
    goToPage(currentPage) {
      let sortBy = null;
      let asc = null;
      let mappedSortBy = null;
      if (this.sorters.column != null && this.sorters.asc != null) {
        sortBy = this.sorters.column;
        asc = this.sorters.asc;
        mappedSortBy = this.columnMapping[sortBy] || null;
      }
      this.listAnnouncements({
        sortBy: mappedSortBy,
        asc: asc,
        currentPage: currentPage,
      });
    },
  },
};
</script>
