<template>
  <div>
    <CRow>
      <CCol>
        <CRow>
          <CCol lg="12" md="12" sm="12">
            <label for="feedOpt">Feed</label>
            <multiselect
              :disabled="!editable"
              :class="[{ 'invalid-border': announcement.feed_id.length === 0 }]"
              id="feedOpt"
              v-model="announcement.feed_id"
              :multiple="true"
              track-by="value"
              label="label"
              :options="feeds"
              placeholder=""
              @input="onChangeFeed"
            >
              <span slot="noResult"
                >Oops! No Feed with the name. Consider changing the search
                query.</span
              >
            </multiselect>
            <span
              v-if="announcement.feed_id.length === 0"
              v-text="'Please select at least 1 feed'"
              class="invalid-error"
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol>
            <CRow>
              <CCol>Priority</CCol>
              <CCol>
                <div
                  class="form-check form-check-inline"
                  v-for="(option, optionIndex) in priority_list"
                  :key="option.value + optionIndex + 'prio'"
                >
                  <input
                    :disabled="!editable"
                    type="radio"
                    class="form-check-input"
                    :id="option.value + optionIndex + 'prio'"
                    :value="option.value"
                    v-model="announcement.priority"
                  />
                  <label
                    :for="option.value + optionIndex + 'prio'"
                    class="form-check-label"
                    >{{ option.label }}</label
                  >
                </div>
              </CCol>
            </CRow>
            <span
              v-if="announcement.priority === ''"
              class="invalid-error"
              v-text="'Please select the priority'"
            ></span>
          </CCol>
        </CRow>
        <br />

        <CRow>
          <CCol>
            <label>Background Color</label>
            <div class="color">
              <input type="color" v-model="announcement.backgroundColor" />
              <p>{{ announcement.backgroundColor }}</p>
            </div>
          </CCol>
          <CCol>
            <label>Text Color</label>
            <div class="color">
              <input type="color" v-model="announcement.textColor" />
              <p>{{ announcement.textColor }}</p>
            </div>
          </CCol>
        </CRow>
        <br />

        <CRow class="mb-3">
          <CCol lg="6" md="12" sm="12">
            <label for="">Start Time</label>
            <vc-date-picker
              v-model="announcement.start_date"
              :disabled="!start_editable"
              mode="dateTime"
              :masks="{ inputDateTime24hr: 'YYYY-MM-DD HH:mm' }"
              :model-config="{ type: 'string', mask: 'YYYY-MM-DD HH:mm' }"
              is24hr
            >
              <template v-slot="{ inputValue, inputEvents, togglePopover }">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span
                      class="input-group-text"
                      @click="start_editable && togglePopover"
                    >
                      <CIcon name="cil-calendar"></CIcon>
                    </span>
                  </div>
                  <input
                    :disabled="!start_editable"
                    class="form-control"
                    :value="inputValue"
                    v-on="start_editable ? inputEvents : {}"
                  />
                </div>
              </template>
            </vc-date-picker>
            <span
              v-if="start_errors && start_editable"
              v-text="start_errors_text"
              class="invalid-error"
            />
          </CCol>
          <CCol lg="6" md="12" sm="12">
            <label for="">End Time</label>
            <vc-date-picker
              v-model="announcement.end_date"
              mode="dateTime"
              :masks="{ inputDateTime24hr: 'YYYY-MM-DD HH:mm' }"
              :model-config="{ type: 'string', mask: 'YYYY-MM-DD HH:mm' }"
              is24hr
            >
              <template v-slot="{ inputValue, inputEvents, togglePopover }">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span
                      class="input-group-text"
                      @click="editable && togglePopover"
                    >
                      <CIcon name="cil-calendar"></CIcon>
                    </span>
                  </div>
                  <input
                    :disabled="!editable"
                    class="form-control"
                    :value="inputValue"
                    v-on="editable ? inputEvents : {}"
                  />
                </div>
                <span
                  v-if="end_errors && editable"
                  v-text="end_error_text"
                  class="invalid-error"
                />
              </template>
            </vc-date-picker>
          </CCol>
        </CRow>
      </CCol>
    </CRow>

    <CRow v-for="(value, name) in announcement.languages" :key="name">
      <CCol>
        <CInput
          :disabled="!editable"
          v-model="announcement.languages[name].title"
          placeholder="Max 30 EN/MS characters OR Max 15 ZH characters"
          :is-valid="announcement.languages[name].title !== ''"
          invalid-feedback="Please enter the announcement title"
          :label="name.toString().toUpperCase() + ' Title'"
          :description="
            announcement.languages[name].title.length + ' Characters'
          "
        />
        <CTextarea
          :disabled="!editable"
          :label="name.toString().toUpperCase() + ' Desc'"
          v-model="announcement.languages[name].body_text"
          placeholder="Max 100 EN/MS characters OR Max 45 ZH characters"
          :description="
            announcement.languages[name].body_text.length + ' Characters'
          "
          :is-valid="announcement.languages[name].body_text != ''"
          invalid-feedback="Please enter the announcement description"
        />
        <CInput
          :disabled="!editable"
          :label="name.toString().toUpperCase() + ' Link'"
          v-model="announcement.languages[name].link"
          placeholder="Must be a Newswav URL"
          :invalid-feedback="
            announcement.languages[name].link !== ''
              ? 'Please insert a Newswav URL (https://newswav.com)'
              : ''
          "
          :is-valid="nwPostUrlValidator"
        />
      </CCol>
    </CRow>
    <br />
  </div>
</template>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>

<script>
import Multiselect from 'vue-multiselect';
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';
import compareAsc from 'date-fns/compareAsc';
import parseISO from 'date-fns/parseISO';
export default {
  name: 'BaseAnnouncementForm',
  data() {
    return {
      now: new Date(),
      edit: this.$route.params.id !== undefined,
    };
  },
  components: { Multiselect },
  props: {
    announcement: {
      type: Object,
      required: true,
    },
  },
  computed: {
    start_editable() {
      if (this.edit && parseISO(this.announcement.start_date) <= this.now) {
        return false;
      }
      return true;
    },
    editable() {
      if (this.edit) {
        return parseISO(this.announcement.end_date) >= this.now;
      }
      return true;
    },
    feeds() {
      let use = [];
      let feed = this.$store.getters.getFeeds;
      feed.forEach((f) => {
        let data = {
          value: f.id,
          label: f.name,
        };
        use.unshift(data);
      });
      return use.sort((a, b) => a.label.localeCompare(b.label));
    },
    priority_list() {
      return this.$store.getters.getPriority;
    },
    start_errors() {
      if (
        compareAsc(parseISO(this.announcement.start_date), this.now) === -1 ||
        this.announcement.start_date === ''
      ) {
        return true;
      }
      return false;
    },
    start_errors_text() {
      if (compareAsc(parseISO(this.announcement.start_date), this.now) === -1) {
        return 'Start time must be AFTER current time';
      }

      if (this.announcement.start_date === '') {
        return 'Please select a start time';
      }
      return '';
    },
    end_errors() {
      if (
        compareAsc(
          parseISO(this.announcement.start_date),
          parseISO(this.announcement.end_date)
        ) !== -1 ||
        compareAsc(this.now, parseISO(this.announcement.end_date)) === 1 ||
        this.announcement.end_date === ''
      ) {
        return true;
      }
      return false;
    },
    end_error_text() {
      if (
        compareAsc(
          parseISO(this.announcement.start_date),
          parseISO(this.announcement.end_date)
        ) !== -1
      ) {
        return 'End time must be AFTER start time';
      }
      if (compareAsc(this.now, parseISO(this.announcement.end_date)) === 1) {
        return 'End time must be AFTER the current time';
      }
      if (this.announcement.end_date === '') {
        return 'Please select an end time';
      }
      return '';
    },
  },
  created() {
    if (this.$store.getters.getFeeds.length === 0) {
      this.$store.dispatch('listFeeds');
    }
  },
  methods: {
    nwPostUrlValidator(val) {
      if (val != '') {
        // eslint-disable-next-line no-useless-escape
        return /^https\:\/\/(dev\.)?newswav.com/i.test(val);
      }
      return true;
    },
    onChangeFeed(input) {
      if (input.length > 0) {
        if (
          input[input.length - 1].value === '0' ||
          input[input.length - 1].value === 'F_F_ALL'
        ) {
          let data = input[input.length - 1];
          this.announcement.feed_id = [];
          this.announcement.feed_id.push(data);
        } else {
          if (input.find((element) => element.value === '0') != null) {
            input = input.filter(function (item) {
              return item.value !== '0';
            });
            this.announcement.feed_id = [];
            this.announcement.feed_id = input;
          }
          if (input.find((element) => element.value === 'F_F_ALL') != null) {
            input = input.filter(function (item) {
              return item.value !== 'F_F_ALL';
            });
            this.announcement.feed_id = [];
            this.announcement.feed_id = input;
          }
        }
      }
    },
  },
};
</script>
<style scoped>
small {
  font-size: 100% !important;
}
.invalid-border {
  border: 1px solid #e55353;
  border-radius: 6px;
}
.invalid-error {
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #e55353;
}
.color {
  display: flex;
  gap: 12px;
}
</style>
