<template>
  <div>
    <CCard>
      <CCardHeader>User Profile</CCardHeader>
      <CCardBody>
        <CInput v-model="user.name" readonly label="Name" />
        <CInput v-model="user.email" readonly label="Email" />
        <EditPassword :user="user" />
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import EditPassword from './EditPassword';
export default {
  name: 'Profile',
  components: { EditPassword },
  computed: {
    user() {
      return this.$store.getters.getUser;
    },
  },
};
</script>

<style scoped></style>
