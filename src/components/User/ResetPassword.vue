<template>
  <div>
    <CContainer class="mt-5">
      <CCard>
        <CCardBody>
          <CCardTitle>Set New Password</CCardTitle>
          <CInput v-model="user.email" readonly label="Email"></CInput>
          <CInput
            label="New Password"
            v-model="user.new_pw"
            :type="new_pw_type"
            :invalid-feedback="'New password must be at least 8 characters, contains at least 1 capital letter and 1 number'"
            :is-valid="isPassValid"
          >
            <template #append-content>
              <div
                @click="showPassword('new_pw')"
                style="width: 30px; height: 1.5em"
              >
                <svg
                  v-if="new_pw_type === 'password'"
                  width="1.2em"
                  height="1.2em"
                  viewBox="0 0 16 16"
                  class="bi bi-eye"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.134 13.134 0 0 0 1.66 2.043C4.12 11.332 5.88 12.5 8 12.5c2.12 0 3.879-1.168 5.168-2.457A13.134 13.134 0 0 0 14.828 8a13.133 13.133 0 0 0-1.66-2.043C11.879 4.668 10.119 3.5 8 3.5c-2.12 0-3.879 1.168-5.168 2.457A13.133 13.133 0 0 0 1.172 8z"
                  />
                  <path
                    fill-rule="evenodd"
                    d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"
                  />
                </svg>
                <svg
                  v-else
                  width="1.2em"
                  height="1.2em"
                  viewBox="0 0 16 16"
                  class="bi bi-eye-slash"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7.028 7.028 0 0 0-2.79.588l.77.771A5.944 5.944 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.134 13.134 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755-.165.165-.337.328-.517.486l.708.709z"
                  />
                  <path
                    d="M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829l.822.822zm-2.943 1.299l.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829z"
                  />
                  <path
                    d="M3.35 5.47c-.18.16-.353.322-.518.487A13.134 13.134 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7.029 7.029 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709z"
                  />
                  <path
                    fill-rule="evenodd"
                    d="M13.646 14.354l-12-12 .708-.708 12 12-.708.708z"
                  />
                </svg>
              </div>
            </template>
          </CInput>
          <CInput
            label="Confirm New Password"
            v-model="user.confirm_pw"
            :is-valid="checkSimilar"
            :type="conf_pw_type"
            :invalid-feedback="'New Password and Confirm New Password do not match.'"
          >
            <template #append-content>
              <div
                @click="showPassword('conf_pw')"
                style="width: 30px; height: 1.5em"
              >
                <svg
                  v-if="conf_pw_type === 'password'"
                  width="1.2em"
                  height="1.2em"
                  viewBox="0 0 16 16"
                  class="bi bi-eye"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.134 13.134 0 0 0 1.66 2.043C4.12 11.332 5.88 12.5 8 12.5c2.12 0 3.879-1.168 5.168-2.457A13.134 13.134 0 0 0 14.828 8a13.133 13.133 0 0 0-1.66-2.043C11.879 4.668 10.119 3.5 8 3.5c-2.12 0-3.879 1.168-5.168 2.457A13.133 13.133 0 0 0 1.172 8z"
                  />
                  <path
                    fill-rule="evenodd"
                    d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"
                  />
                </svg>
                <svg
                  v-else
                  width="1.2em"
                  height="1.2em"
                  viewBox="0 0 16 16"
                  class="bi bi-eye-slash"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7.028 7.028 0 0 0-2.79.588l.77.771A5.944 5.944 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.134 13.134 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755-.165.165-.337.328-.517.486l.708.709z"
                  />
                  <path
                    d="M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829l.822.822zm-2.943 1.299l.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829z"
                  />
                  <path
                    d="M3.35 5.47c-.18.16-.353.322-.518.487A13.134 13.134 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7.029 7.029 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709z"
                  />
                  <path
                    fill-rule="evenodd"
                    d="M13.646 14.354l-12-12 .708-.708 12 12-.708.708z"
                  />
                </svg>
              </div>
            </template>
          </CInput>
          <CButton @click="submitReset" color="info">Submit</CButton>
        </CCardBody>
      </CCard>
    </CContainer>
  </div>
</template>

<script>
export default {
  name: 'ResetPassword',
  data() {
    return {
      user: {
        token: atob(this.$route.params.token),
        email: atob(this.$route.params.email),
        new_pw: '',
        confirm_pw: '',
      },
      new_pw_type: 'password',
      conf_pw_type: 'password',
    };
  },
  methods: {
    submitReset() {
      if (this.user.new_pw.length > 0 && this.user.confirm_pw.length > 0) {
        if (this.user.new_pw === this.user.confirm_pw) {
          this.$store
            .dispatch('resetPassword', this.user)
            .then((res) => {
              alert(res);
              this.$router.push('/login');
            })
            .catch((err) => {
              alert(err);
            });
        } else {
          alert('New Password and Confirm New Password do not match');
        }
      } else {
        alert('Please fill in all fields');
      }
    },
    isPassValid(val) {
      return val ? /^(?=.*[A-Z])(?=.*\d).{8,}$/.test(val) : false;
    },
    checkSimilar(val) {
      return val ? this.isPassValid(val) && val === this.user.new_pw : false;
    },
    showPassword(from) {
      if (from === 'new_pw') {
        if (this.new_pw_type === 'password') {
          this.new_pw_type = 'text';
        } else {
          this.new_pw_type = 'password';
        }
      } else if (from === 'conf_pw') {
        if (this.conf_pw_type === 'password') {
          this.conf_pw_type = 'text';
        } else {
          this.conf_pw_type = 'password';
        }
      }
    },
  },
};
</script>

<style scoped></style>
