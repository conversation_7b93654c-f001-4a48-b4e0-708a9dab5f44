<template>
  <div>
    <CContainer>
      <div class="mt-5 d-flex justify-content-center">
        <CCard class="col-lg-6 col-md-12">
          <CCardBody>
            <CForm @submit.prevent="loginUser">
              <CCardTitle>Newswav Dashboard Login</CCardTitle>
              <CInput
                v-model="user.email"
                label="Email"
                :is-valid="emailValidator"
              />
              <CInput
                v-model="user.password"
                label="Password"
                :type="pw_type"
                :is-valid="isPassValid"
              >
                <template #append-content>
                  <div @click="showPassword" style="width: 30px; height: 1.5em">
                    <svg
                      v-if="pw_type === 'password'"
                      width="1.2em"
                      height="1.2em"
                      viewBox="0 0 16 16"
                      class="bi bi-eye"
                      fill="currentColor"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.134 13.134 0 0 0 1.66 2.043C4.12 11.332 5.88 12.5 8 12.5c2.12 0 3.879-1.168 5.168-2.457A13.134 13.134 0 0 0 14.828 8a13.133 13.133 0 0 0-1.66-2.043C11.879 4.668 10.119 3.5 8 3.5c-2.12 0-3.879 1.168-5.168 2.457A13.133 13.133 0 0 0 1.172 8z"
                      />
                      <path
                        fill-rule="evenodd"
                        d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"
                      />
                    </svg>
                    <svg
                      v-else
                      width="1.2em"
                      height="1.2em"
                      viewBox="0 0 16 16"
                      class="bi bi-eye-slash"
                      fill="currentColor"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7.028 7.028 0 0 0-2.79.588l.77.771A5.944 5.944 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.134 13.134 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755-.165.165-.337.328-.517.486l.708.709z"
                      />
                      <path
                        d="M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829l.822.822zm-2.943 1.299l.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829z"
                      />
                      <path
                        d="M3.35 5.47c-.18.16-.353.322-.518.487A13.134 13.134 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7.029 7.029 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709z"
                      />
                      <path
                        fill-rule="evenodd"
                        d="M13.646 14.354l-12-12 .708-.708 12 12-.708.708z"
                      />
                    </svg>
                  </div>
                </template>
              </CInput>
              <router-link to="/forgot-password">
                Forgot Password?
              </router-link>
              <br />
              <CButton
                class="mt-2"
                color="primary"
                type="submit"
                :disabled="loading"
              >
                Login
                <CSpinner color="info" size="sm" v-if="loading" />
              </CButton>
            </CForm>
          </CCardBody>
        </CCard>
      </div>
    </CContainer>
  </div>
</template>

<script>
import Bugsnag from '@bugsnag/js';
export default {
  name: 'Login',
  data() {
    return {
      user: {
        email: '',
        password: '',
      },
      pw_type: 'password',
    };
  },
  created() {
    if (this.$store.getters.isLoggedIn && this.$store.getters.isTokenValid) {
      this.$router.push('/dashboard');
    }
  },
  computed: {
    loading() {
      return this.$store.getters.isAuthLoading;
    },
  },
  methods: {
    emailValidator(val) {
      return val
        ? /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/.test(
            val
          )
        : false;
    },
    isPassValid(val) {
      return val ? /^(?=.*[A-Z])(?=.*\d).{8,}$/.test(val) : false;
    },
    loginUser() {
      this.$store
        .dispatch('login', this.user)
        .then(() => {
          this.$router.push('/dashboard');
        })
        .catch((err) => {
          alert(err);
          Bugsnag.notify(err);
        });
    },
    showPassword() {
      if (this.pw_type === 'password') {
        this.pw_type = 'text';
      } else {
        this.pw_type = 'password';
      }
    },
  },
};
</script>

<style scoped></style>
