<template>
  <div>
    <CContainer class="mt-5">
      <CCard>
        <CCardBody>
          <CCardTitle>Forgot Password</CCardTitle>
          <CInput
            v-model="user.email"
            label="Email"
            type="email"
            :invalid-feedback="'Please enter a valid email'"
            :is-valid="emailValidator"
          />
          <CButton color="info" @click="sendReset">Submit</CButton>
        </CCardBody>
      </CCard>
    </CContainer>
  </div>
</template>

<script>
export default {
  name: 'ForgotPassword',
  data() {
    return {
      user: {
        email: '',
      },
    };
  },
  methods: {
    emailValidator(val) {
      return val
        ? /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/.test(
            val
          )
        : false;
    },
    sendReset() {
      this.$store
        .dispatch('sendResetEmail', this.user)
        .then((res) => {
          alert(res);
          this.$router.push('/login');
        })
        .catch((err) => {
          alert(err);
          console.log(err);
        });
    },
  },
};
</script>

<style scoped></style>
