<template>
  <div>
    <CModal title="User Logout" :show.sync="show" color="warning" centered>
      Are you sure you want to logout?
      <template #footer>
        <CButton @click="$emit('cancel-logout')">Cancel</CButton>
        <CButton
          color="warning"
          @click="$emit('proceed-logout')"
          :disabled="loading"
          >Logout
          <CSpinner size="sm" color="info" v-if="loading" />
        </CButton>
      </template>
    </CModal>
  </div>
</template>

<script>
export default {
  name: 'Logout',
  props: ['show'],
  computed: {
    loading() {
      return this.$store.getters.isAuthLoading;
    },
  },
};
</script>

<style scoped></style>
