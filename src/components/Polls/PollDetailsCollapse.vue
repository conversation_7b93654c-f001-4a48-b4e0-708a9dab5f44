<template>
  <div>
    <CRow>
      <CCol>
        <CInput v-model="poll.text" label="Poll Question (EN)" readonly />
        <CInput v-model="poll.text_MS" label="Poll Question (MS)" readonly />
        <CInput v-model="poll.text_ZH" label="Poll Question (ZH)" readonly />
        <CInput v-model="poll.name" label="Poll Name" readonly />
      </CCol>
    </CRow>
    <CRow>
      <CCol>
        <CInput v-model="poll.option_type" label="Poll Option Type" readonly />
        <CInput v-model="poll.language" label="Poll Language" readonly />
        <CInput
          v-model="poll.feedback_chars_limit"
          label="Poll Feedback Character Limit"
          readonly
        />
        <CInput v-model="poll.is_sponsored" label="Is Sponsored" readonly />
        <CInput
          v-if="poll.is_sponsored === 'Yes'"
          v-model="poll.max_desired_responses"
          label="Max Responses"
          readonly
        />
        <CInput v-model="poll.random" label="Randomise Poll Options" readonly />
      </CCol>
      <CCol>
        <CInput
          v-model="poll.article_unique_id"
          label="Poll Article ID"
          readonly
        />
        <CInput v-model="inject_in_feed" label="Inject In Feed" readonly />
        <CInput v-model="feed_list" label="List in Poll Feed" readonly />
        <div v-if="poll.is_sponsored === 'Yes'">
          <CInput v-model="anonymous" label="Is Anonymous" readonly />
          <CInput label="Advertiser" v-model="poll.advertiser" readonly />
        </div>
        <div v-else>
          <CInput v-model="anonymous" label="Is Anonymous" readonly />
        </div>
      </CCol>
    </CRow>
  </div>
</template>

<script>
export default {
  name: 'PollDetailsCollapse',
  props: {
    poll: {
      type: Object,
      required: true,
    },
  },
  computed: {
    anonymous() {
      return this.poll.anonymous === 0 ? 'No' : 'Yes';
    },
    feed_list() {
      return this.poll.feed_listing === 0 ? 'No' : 'Yes';
    },
    inject_in_feed() {
      return this.poll.inject_in_feed === 0 ? 'No' : 'Yes';
    },
  },
};
</script>

<style scoped></style>
