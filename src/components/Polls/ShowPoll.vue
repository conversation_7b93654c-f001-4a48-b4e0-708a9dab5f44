<template>
  <div>
    <div v-show="loading" class="text-center align-middle">
      <CSpinner color="primary" style="width: 4rem; height: 4rem" />
    </div>
    <div v-show="!loading">
      <CRow>
        <CCol>
          <CButton
            variant="outline"
            shape="pill"
            color="primary"
            @click="goBack"
            >Back</CButton
          >
        </CCol>
        <CCol>
          <CButtonToolbar class="float-right">
            <CDropdown
              v-if="checkAction('poll_inject')"
              toggler-text="Inject Poll"
              color="danger"
              variant="outline"
              shape="pill"
            >
              <CDropdownItem
                @click="injectToFeed"
                v-text="
                  poll.inject_in_feed === 0 ? 'In Feed' : 'Remove From Feed'
                "
              />
              <CDropdownItem
                @click="injectToPollTab"
                v-text="
                  poll.feed_listing === 0
                    ? 'In Poll Tab'
                    : 'Remove From Poll Tab'
                "
              />
            </CDropdown>
          </CButtonToolbar>
        </CCol>
      </CRow>
      <br />
      <CRow>
        <CCol>
          <CCard>
            <CCardHeader>
              <CRow>
                <CCol>
                  Poll Question : {{ poll.text }} [ ID : {{ poll.id }} ]
                </CCol>
                <CCol>
                  <CButtonToolbar class="float-right">
                    <CButton
                      @click="poll_collapse = !poll_collapse"
                      color="info"
                      variant="outline"
                      shape="pill"
                    >
                      Poll Details
                    </CButton>
                    <EditPollModal
                      :key="poll.id"
                      v-if="checkAction('poll_edit')"
                      :section="false"
                      :poll="poll"
                    />
                    <!--                                    <CButton @click="injectToFeed" color="danger" variant="outline" shape="pill"-->
                    <!--                                             v-text="poll.inject_in_feed === 0 ? 'Inject To Feed' : 'Remove From Feed'">-->
                    <!--                                    </CButton>-->
                  </CButtonToolbar>
                </CCol>
              </CRow>
            </CCardHeader>
            <CCardBody>
              <CRow class="dashboard">
                <CCol>
                  <CWidgetIcon
                    text="Status"
                    v-bind:header="loading ? 'Loading..' : poll.status"
                    v-bind:color="getStatusColor(poll.status)"
                    :icon-padding="false"
                  >
                    <CIcon
                      v-bind:name="getStatusIcon(poll.status)"
                      width="24"
                    ></CIcon>
                  </CWidgetIcon>
                </CCol>
                <CCol>
                  <CWidgetIcon
                    text="Voting Status"
                    v-bind:header="loading ? 'Loading' : poll.voting_status"
                    v-bind:color="getStatusColor(poll.voting_status)"
                    :icon-padding="false"
                  >
                    <CIcon name="cil-power-standby" width="24"></CIcon>
                  </CWidgetIcon>
                </CCol>
                <CCol>
                  <CWidgetIcon
                    text="Total Responses"
                    v-bind:header="loading ? 'Loading' : responses"
                    color="info"
                    :icon-padding="false"
                  >
                    <CIcon name="cil-mouse" width="24"></CIcon>
                  </CWidgetIcon>
                </CCol>
              </CRow>
              <CRow>
                <CCol>
                  <CCollapse :show="poll_collapse">
                    <CCardBody>
                      <PollDetailsCollapse :poll="poll" />
                    </CCardBody>
                  </CCollapse>
                </CCol>
              </CRow>
              <CRow>
                <CCol>
                  <CRow class="dashboard">
                    <CCol>
                      <CCallout color="warning">
                        <small class="text-muted">Published Date</small><br />
                        <strong class="h4">{{
                          loading ? 'Loading...' : published_date
                        }}</strong>
                      </CCallout>
                    </CCol>
                    <CCol>
                      <CCallout color="warning">
                        <small class="text-muted">Close Date</small><br />
                        <strong class="h4">{{
                          loading ? 'Loading...' : poll.close_at
                        }}</strong>
                      </CCallout>
                    </CCol>
                  </CRow>
                  <CRow>
                    <CCol>
                      <div
                        class="progress-group"
                        v-if="
                          poll.status === 'active' ||
                          poll.voting_status === 'open'
                        "
                      >
                        <div class="progress-group-header">
                          <CIcon
                            name="cil-calendar-check"
                            class="progress-group-icon"
                          />
                          <span class="title">Poll Progress</span>
                          <span class="ml-auto font-weight-bold">
                            {{ loading ? 'Loading...' : pollProgress }} %</span
                          >
                        </div>
                        <div class="progress-group-bars">
                          <CProgress
                            class="progress-xs"
                            v-bind:value="loading ? 0 : pollProgress"
                            color="warning"
                          />
                        </div>
                      </div>
                    </CCol>
                  </CRow>
                </CCol>
              </CRow>
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
      <br />
      <CRow>
        <CCol>
          <CCard>
            <CCardHeader>
              <CRow>
                <CCol> Poll Answers</CCol>
                <CCol>
                  <CButtonToolbar class="float-right">
                    <AddAnswerModal
                      v-if="checkAction('poll_answer_add')"
                      :created_ans="answers"
                      :poll="poll"
                    />
                    <ShowFeedbackAnswers
                      v-if="user_answers.length > 0"
                      :feedback_ans="user_answers"
                    />
                  </CButtonToolbar>
                </CCol>
              </CRow>
            </CCardHeader>
            <CCardBody>
              <CDataTable
                :key="answers.length"
                :items-per-page="5"
                :fields="answers_field"
                :items="answers"
                sorter
                small
                table-filter
                v-bind:loading="answer_loading"
                pagination
              >
                <template #action="{ item }">
                  <td>
                    <CButtonToolbar>
                      <EditAnswerModal
                        v-if="checkAction('poll_answer_edit')"
                        :answer="item"
                        :from_wizard="false"
                        :poll="poll"
                      />
                      <CButton
                        :disabled="poll.status == 'archived'"
                        v-if="checkAction('poll_answer_delete')"
                        variant="outline"
                        @click="deleteAnswer(item.id)"
                        color="danger"
                        v-c-tooltip="'Delete Answer'"
                      >
                        <CIcon name="cil-trash"></CIcon>
                      </CButton>
                    </CButtonToolbar>
                  </td>
                </template>
              </CDataTable>
            </CCardBody>
          </CCard>
          <OverlayLoader v-if="update_loading" />
        </CCol>
      </CRow>
    </div>
  </div>
</template>

<script>
import PollDetailsCollapse from './PollDetailsCollapse';
import EditPollModal from './EditPollModal';
import EditAnswerModal from '../Answers/EditAnswerModal';
import AddAnswerModal from '../Answers/AddAnswerModal';
import OverlayLoader from '../views/OverlayLoader';
import ShowFeedbackAnswers from '../Answers/ShowFeedbackAnswers';

export default {
  name: 'ShowPoll',
  components: {
    ShowFeedbackAnswers,
    OverlayLoader,
    AddAnswerModal,
    EditAnswerModal,
    EditPollModal,
    PollDetailsCollapse,
  },
  data() {
    return {
      answers_field: [
        { key: 'text', label: 'Answer Option' },
        { key: 'total_votes', label: 'Total Votes' },
        { key: 'is_feedback', label: 'Feedback' },
        { key: 'is_rate', label: 'Rate' },
        { key: 'skip', label: 'Skip' },
        { key: 'action', sorter: false, label: 'Action' },
      ],
      poll_collapse: false,
    };
  },
  computed: {
    published_date() {
      let base = new Date(this.poll.published_date);
      let dd = base.getDate();
      if (dd < 10) dd = '0' + dd;
      let mm = base.getMonth() + 1;
      if (mm < 10) mm = '0' + mm;
      let yy = base.getFullYear();
      return yy + '-' + mm + '-' + dd;
    },
    loading() {
      return this.$store.getters.getPollLoading;
    },
    answer_loading() {
      return this.$store.getters.getAnswerLoading;
    },
    poll() {
      return this.$store.getters.getOnePoll;
    },
    answers() {
      return this.$store.getters.getOnePollAnswers;
    },
    user_answers() {
      return this.$store.getters.getOnePollUserAnswers;
    },
    pollProgress() {
      let today = new Date();
      let start = new Date(this.poll.published_date);
      let end = new Date(this.poll.close_at);
      let result = Math.round(((today - start) / (end - start)) * 100);
      return result > 100 ? 100 : result;
    },
    responses() {
      let total = 0;
      this.answers.forEach((ans) => {
        total += ans.total_votes;
      });
      return ' ' + total;
    },
    update_loading() {
      return this.$store.getters.getUpdateLoading;
    },
  },
  created() {
    this.$store.dispatch('getPoll', this.$route.params.poll_id);
  },
  methods: {
    goBack() {
      this.$store.commit('SET_CLEAR_ONE_POLL');
      this.$router.push('/polls');
    },
    getStatusIcon(status) {
      return status === 'open' || status === 'active'
        ? 'cil-media-play'
        : status === 'scheduled'
          ? 'cil-av-timer'
          : 'cil-book';
    },
    getStatusColor(status) {
      return status === 'active' || status === 'open'
        ? 'success'
        : status === 'scheduled'
          ? 'warning'
          : status === 'archived'
            ? 'dark'
            : 'danger';
    },
    deleteAnswer(id) {
      let msg = 'This will delete the answer from the poll. Continue?';
      if (confirm(msg)) {
        this.$store.dispatch('deleteAnswer', id);
      }
    },
    injectToFeed() {
      let msg = '';
      if (this.poll.inject_in_feed === 0) {
        msg = 'This will show the poll in the feed of the app. Continue?';
      } else {
        msg = 'This will remove the poll from the feed in the app. Continue?';
      }
      if (confirm(msg)) {
        let data = JSON.parse(JSON.stringify(this.poll));
        data.inject_in_feed = data.inject_in_feed === 0 ? 1 : 0;
        data.is_sponsored = data.is_sponsored === 'Yes' ? 1 : 0;
        data.random = data.random === 'Yes' ? 1 : 0;
        if (data.status === 'archived' || data.status === 'scheduled') {
          data.status = 'active';
        }
        this.$store.dispatch('updatePoll', data);
      }
    },
    injectToPollTab() {
      let msg = '';
      if (this.poll.feed_listing === 0) {
        msg = 'This will show the poll in the poll tab. Continue?';
      } else {
        msg = 'This will remove the poll from the poll tab. Continue?';
      }
      if (confirm(msg)) {
        let data = JSON.parse(JSON.stringify(this.poll));
        data.feed_listing = data.feed_listing === 0 ? 1 : 0;
        data.is_sponsored = data.is_sponsored === 'Yes' ? 1 : 0;
        data.random = data.random === 'Yes' ? 1 : 0;
        if (data.status === 'archived' || data.status === 'scheduled') {
          data.status = 'active';
        }
        this.$store.dispatch('updatePoll', data);
      }
    },
  },
};
</script>

<style scoped>
@media (max-width: 750px) {
  .dashboard {
    display: flex;
    flex-direction: column;
  }
}
</style>
