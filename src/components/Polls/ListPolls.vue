<template>
  <div>
    <CRow>
      <CCol> </CCol>
    </CRow>
    <br />
    <CRow>
      <CCol>
        <CCard>
          <CCardHeader> List of Polls </CCardHeader>
          <CCardBody>
            <CRow>
              <CCol>
                <div v-if="checkAction('poll_add')">
                  <router-link :to="'/polls/create-polls'">
                    <CButton
                      class="float-right"
                      variant="outline"
                      color="success"
                      shape="pill"
                    >
                      Create New Poll
                    </CButton>
                  </router-link>
                </div>
              </CCol>
            </CRow>
            <br />
            <CRow>
              <CCol>
                <CDataTable
                  v-bind:loading="loading"
                  :items="polls"
                  small
                  :columnFilter="{ external: true }"
                  :sorter="{ external: true, resetable: false }"
                  pagination
                  :fields="fields"
                  :key="polls.id"
                  :column-filter-value="filters"
                  @update:column-filter-value="onFilterChange"
                  @update:sorter-value="onSorterChange"
                >
                  <template #name="{ item }">
                    <td style="max-width: 500px">
                      {{ item.name }}
                    </td>
                  </template>
                  <template #published_date="{ item }">
                    <td>
                      {{ item.published_date | dates }}
                    </td>
                  </template>
                  <template v-if="loading" #no-items-view>
                    <div style="height: 300px"></div>
                  </template>
                  <template #is_sponsored="{ item }">
                    <td>
                      <CBadge :color="getBadge(item.is_sponsored)">{{
                        item.is_sponsored
                      }}</CBadge>
                    </td>
                  </template>
                  <template #total_responses="{ item }">
                    <td>
                      {{ item.total_responses | numbers }}
                    </td>
                  </template>
                  <template #show_details="{ item }">
                    <td>
                      <CButtonToolbar>
                        <EditPollModal
                          :key="item.id"
                          v-if="checkAction('poll_edit')"
                          :poll="item"
                          :section="true"
                        />
                        <div v-show="checkAction('poll_show')">
                          <CButton
                            @click="redirectTo(item.id)"
                            color="primary"
                            variant="outline"
                            v-c-tooltip="'Show Poll'"
                          >
                            <CIcon name="cil-envelope-open"></CIcon>
                          </CButton>
                        </div>
                        <CButton
                          v-if="checkAction('poll_archive')"
                          variant="outline"
                          v-bind:color="getColor(item.status)"
                          v-c-tooltip="'Toggle Archive Poll'"
                          @click="deletePoll(item)"
                        >
                          <CIcon v-bind:name="getIcon(item.status)"></CIcon>
                        </CButton>
                      </CButtonToolbar>
                    </td>
                  </template>
                </CDataTable>
                <CPagination
                  @update:activePage="goToPage"
                  :pages="pagination.last_page"
                  :activePage.sync="pagination.current_page"
                />
              </CCol>
            </CRow>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  </div>
</template>

<script>
import EditPollModal from './EditPollModal';
import { mapGetters, mapActions } from 'vuex';

export default {
  name: 'ListPolls',
  components: { EditPollModal },
  data() {
    return {
      fields: [
        { key: 'id', label: 'Poll ID', filter: true },
        { key: 'name', label: 'Poll Name' },
        { key: 'published_date', label: 'Start Date', filter: false },
        { key: 'close_at', label: 'End Date', filter: false },
        { key: 'option_type', label: 'Type', filter: false },
        { key: 'status', label: 'Status', filter: false },
        { key: 'is_sponsored', label: 'Sponsored', filter: false },
        { key: 'total_responses', label: 'Total Responses', filter: false },
        { key: 'show_details', sorter: false, label: 'Actions', filter: false },
      ],
      searchKeyword: '',
      filters: { id: null, name: null },
      sorters: { asc: null, column: null },
      debounceTimer: null,
    };
  },
  filters: {
    dates(val) {
      let base = new Date(val);
      let dd = base.getDate();
      if (dd < 10) dd = '0' + dd;
      let mm = base.getMonth() + 1;
      if (mm < 10) mm = '0' + mm;
      let yy = base.getFullYear();
      return yy + '-' + mm + '-' + dd;
    },
    sponsored(val) {
      return val === 1 ? 'Yes' : 'No';
    },
    numbers(val) {
      return val.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
    },
  },
  computed: {
    ...mapGetters({
      loading: 'getVideoLoading',
      polls: 'getPolls',
      pagination: 'getPollPagination',
    }),
    polls() {
      return this.$store.getters.getAllPoll;
    },
    loading() {
      return this.$store.getters.getPollLoading;
    },
  },
  created() {
    this.getPolls(1);
  },
  methods: {
    ...mapActions({
      getPolls: 'getPolls',
    }),
    async onFilterChange(updatedFilters) {
      clearTimeout(this.debounceTimer);

      this.filters = updatedFilters;
      this.debounceTimer = setTimeout(() => {
        let id = null;
        let name = null;
        let sortBy = null;
        let asc = null;
        if (this.filters.id) {
          id = this.filters.id;
        }
        if (this.filters.name) {
          name = this.filters.name;
        }

        if (this.sorters.column != null && this.sorters.asc != null) {
          sortBy = this.sorters.column;
          asc = this.sorters.asc;
        }

        this.getPolls({ pollId: id, pollName: name, sortBy: sortBy, asc: asc });
      }, 1000);
    },
    async onSorterChange(updatedSorter) {
      this.sorters = updatedSorter;
      let sortBy = this.sorters.column;
      let asc = this.sorters.asc;
      let id = null;
      let name = null;
      if (this.filters.id) {
        id = this.filters.id;
      }
      if (this.filters.name) {
        name = this.filters.name;
      }

      this.getPolls({ pollId: id, pollName: name, sortBy: sortBy, asc: asc });
    },
    redirectTo(id) {
      this.$router.push('/polls/show-poll/' + id);
    },
    goToPage(currentPage) {
      let id = null;
      let name = null;
      let sortBy = null;
      let asc = null;
      if (this.filters.id) {
        id = this.filters.id;
      }
      if (this.filters.name) {
        name = this.filters.name;
      }
      if (this.sorters.column != null && this.sorters.asc != null) {
        sortBy = this.sorters.column;
        asc = this.sorters.asc;
      }

      this.getPolls({
        pollId: id,
        pollName: name,
        sortBy: sortBy,
        asc: asc,
        currentPage: currentPage,
      });
    },
    async deletePoll(poll) {
      let msg = '';
      if (poll.status === 'archived') {
        msg = 'This will un-archive the poll. Are you sure?';
      } else {
        msg = 'This will archive the poll. Are you sure?';
      }
      if (confirm(msg)) {
        let data = JSON.parse(JSON.stringify(poll));
        if (data.status === 'archived') {
          data.status = 'active';
        } else {
          data.status = 'archived';
        }
        data.is_sponsored = data.is_sponsored === 'Yes' ? 1 : 0;
        data.random = data.random === 'Yes' ? 1 : 0;
        await this.$store.dispatch('archivePoll', data);
      }
    },
    getColor(status) {
      return status === 'archived' ? 'success' : 'danger';
    },
    getIcon(status) {
      return status == 'archived' ? 'cil-action-redo' : 'cil-book';
    },
    getBadge(status) {
      return status === 'Yes' ? 'success' : 'danger';
    },
  },
};
</script>

<style scoped></style>
