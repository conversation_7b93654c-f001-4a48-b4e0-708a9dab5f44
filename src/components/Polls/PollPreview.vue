<template>
  <div>
    <div class="mb-2">Poll Preview</div>
    <CCard>
      <CCardBody>
        <CRow class="mb-2">
          <CCol>
            <div
              style="
                background-color: red;
                color: white;
                padding: 5px;
                width: 50px;
              "
            >
              <strong>Polls</strong>
            </div>
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol>
            <strong>{{ poll.text }}</strong>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <div v-if="created_ans.length > 0">
              <CRow
                v-for="ans in created_ans"
                :key="ans.id"
                v-bind:class="'mb-2 ml-2'"
              >
                <CCol>
                  <CRow class="mb-2">
                    <CCol
                      style="
                        max-width: 10px;
                        height: 30px;
                        border-color: red;
                        border-style: dashed;
                      "
                    ></CCol>
                    <CCol class="align-text-middle">
                      {{ ans.text }}
                    </CCol>
                  </CRow>
                </CCol>
              </CRow>
            </div>
            <div v-else>
              <CRow>
                <CCol class="answerSlot"> Created answers will show here </CCol>
              </CRow>
            </div>
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
export default {
  name: 'PollPreview',
  props: {
    created_ans: {
      type: Array,
      default: () => ({}),
    },
    poll: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style scoped>
.answerSlot {
  height: 250px;
  padding: 20px;
  background-color: #b1e5c2;
  margin-left: 10px;
  margin-right: 10px;
}
</style>
