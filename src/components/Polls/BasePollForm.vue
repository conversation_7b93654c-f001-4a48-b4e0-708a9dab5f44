<template>
  <div>
    <CRow>
      <CCol>
        <CInput
          v-model="poll.text"
          label="Poll Question (EN)"
          placeholder="Enter poll question"
          v-bind:invalid-feedback="'Poll question is required'"
          :is-valid="validator"
        />
        <CInput
          v-model="poll.text_MS"
          label="Poll Question (MS)"
          placeholder="Enter poll question"
        />
        <CInput
          v-model="poll.text_ZH"
          label="Poll Question (ZH)"
          placeholder="Enter poll question"
        />
        <CInput
          v-model="poll.name"
          label="Poll Name"
          placeholder="Enter poll name"
          v-bind:invalid-feedback="'Poll name is required'"
          :is-valid="validator"
        />
      </CCol>
    </CRow>
    <CRow>
      <CCol>
        <CSelect
          :value.sync="poll.option_type"
          label="Poll Option Type"
          placeholder="Select Poll Option Type"
          invalid-feedback="Poll Option Type is required"
          :is-valid="validator"
          :options="option_item"
        />
        <CInput
          v-model="publish_date"
          type="datetime-local"
          id="start_date"
          label="Poll Publish Date"
          v-bind:invalid-feedback="'Publish Date of Poll is required'"
          v-bind:is-valid="from_edit ? true : dateValidator"
        />
        <CSelect
          :value.sync="poll.language"
          :options="language_item"
          label="Poll Language"
          placeholder="Select Poll Language"
          :disabled="!from_edit"
          :is-valid="validator"
          invalid-feedback="Poll Language is required"
        />
        <CSelect
          :value.sync="poll.is_sponsored"
          label="Is Sponsored Poll"
          placeholder="Select Sponsored Status"
          :options="sponsored_item"
          @change="setSponsored"
        />
        <CSelect
          :value.sync="poll.random"
          label="Randomise Poll Options"
          :options="sponsored_item"
        />
        <!--        <div class="form-group" v-if="sponsored">-->
        <!--          <label for="maxresp">Max Responses</label>-->
        <!--          <input id="maxresp" :class="!/[0-9]+/g.test(poll.max_desired_responses) && poll.max_desired_responses === '' ? 'is-invalid form-control' : 'is-valid form-control'  "-->
        <!--                 type="text"-->
        <!--                 @input="numbersOnly">-->
        <!--          <div class="invalid-feedback" v-if=" !/[0-9]+/g.test(poll.max_desired_responses) && poll.max_desired_responses === ''">-->
        <!--            Max Responses is required and numbers only-->
        <!--          </div>-->
        <!--        </div>-->
        <CInput
          v-if="sponsored"
          label="Max Responses"
          invalid-feedback="Max Responses is required and numbers only"
          :value.sync="poll.max_desired_responses"
          :is-valid="validateNum"
        />
      </CCol>
      <CCol>
        <CInput
          v-if="from_wizard"
          readonly
          v-model="poll.status"
          label="Poll Status"
        />
        <CSelect
          v-else
          :value.sync="poll.status"
          label="Poll Status"
          placeholder="Select Poll Status"
          invalid-feedback="Poll Status is required"
          :is-valid="validator"
          v-bind:options="status_item"
        />
        <CSelect
          :value.sync="poll.voting_status"
          label="Poll Voting Status"
          placeholder="Select Poll Voting Status"
          invalid-feedback="Poll Voting Status is required"
          :is-valid="validator"
          :options="voting_item"
        />
        <CInput
          v-model="poll.close_at"
          type="date"
          id="end_date"
          label="Poll Close Date"
          v-bind:invalid-feedback="'Close Date of Poll is required'"
          v-bind:is-valid="from_edit ? true : dateValidator"
        />
        <CSelect
          :value.sync="poll.anonymous"
          label="Is the Poll Anonymous"
          placeholder="Select Poll Anonymity"
          :options="anonymous_item"
        />
        <CInput
          v-if="sponsored"
          v-model="poll.advertiser"
          label="Advertiser Name"
          invalid-feedback="Advertiser Name is required"
          :is-valid="advertiserValid"
        />
      </CCol>
    </CRow>
    <OverlayLoader v-if="loading" />
  </div>
</template>

<script>
import OverlayLoader from '../views/OverlayLoader';

export default {
  name: 'BasePollForm',
  components: { OverlayLoader },
  props: {
    poll: {
      type: Object,
    },
    from_edit: {
      type: Boolean,
    },
    from_wizard: {
      type: Boolean,
    },
  },
  data() {
    return {
      sponsored: false,
      status_item: [
        { value: 'active', label: 'Active' },
        { value: 'scheduled', label: 'Scheduled' },
        { value: 'archived', label: 'Archived' },
      ],
      option_item: [
        { value: 'single', label: 'Single' },
        { value: 'single_feedback', label: 'Single Feedback' },
        { value: 'multi', label: 'Multiple' },
        { value: 'multi_feedback', label: 'Multiple Feedback' },
        { value: 'feedback', label: 'Feedback' },
        { value: 'rate', label: 'Rate' },
      ],
      language_item: [
        { value: 'en', label: 'English' },
        { value: 'zh', label: 'Chinese' },
        { value: 'ms', label: 'Malay' },
      ],
      voting_item: [
        { value: 'open', label: 'Open' },
        { value: 'close', label: 'Close' },
      ],
      anonymous_item: [
        { value: 1, label: 'Yes' },
        { value: 0, label: 'No' },
      ],
      sponsored_item: [
        { value: 'Yes', label: 'Yes' },
        { value: 'No', label: 'No' },
      ],
    };
  },
  mounted() {
    if (this.poll.is_sponsored === 'Yes') {
      this.sponsored = true;
    }
  },
  computed: {
    loading() {
      return this.$store.getters.getLoading;
    },
    publish_date: {
      get() {
        let base = new Date();
        if (this.poll.published_date !== '') {
          base = new Date(this.poll.published_date);
        }
        let dd = base.getDate();
        if (dd < 10) dd = '0' + dd;

        let mm = base.getMonth() + 1;
        if (mm < 10) mm = '0' + mm;
        let yy = base.getFullYear();

        let hh = base.getHours();
        if (hh < 10) hh = '0' + hh;

        let ii = base.getMinutes();
        if (ii < 10) ii = '0' + ii;

        return yy + '-' + mm + '-' + dd + 'T' + hh + ':' + ii;
      },
      set(newVal) {
        this.published_date = newVal;
        this.poll.published_date = newVal;
      },
    },
  },
  methods: {
    validator(val) {
      return val ? val !== '' : false;
    },
    validateNum(val) {
      return val ? /[0-9]+/g.test(val) && val !== '' : false;
    },
    advertiserValid(val) {
      return val ? (val !== '' && val !== null) || val.length > 0 : false;
    },
    dateValidator(val) {
      let today = new Date().setHours(0, 0, 0, 0);
      let compare = new Date(val).setHours(0, 0, 0, 0);
      return val ? compare >= today : false;
    },
    setSponsored(e) {
      let val = e.target.value;
      if (val === 'Yes') {
        this.sponsored = true;
        this.$emit('set-val', { prop: 'is_sponsored', val: 'Yes' });
      } else {
        this.sponsored = false;
        this.$emit('set-val', { prop: 'is_sponsored', val: 'No' });
      }
    },
  },
};
</script>

<style scoped></style>
