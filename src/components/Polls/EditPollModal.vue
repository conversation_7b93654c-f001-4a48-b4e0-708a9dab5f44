<template>
  <div>
    <div v-show="checkAction('poll_edit')">
      <CButton
        :disabled="poll.status == 'archived'"
        v-if="!section"
        class="float-right"
        variant="outline"
        shape="pill"
        color="dark"
        @click="show = true"
        >Edit Poll
      </CButton>
      <CButton
        :disabled="poll.status == 'archived'"
        v-else
        color="warning"
        variant="outline"
        @click="show = true"
        v-c-tooltip="'Edit'"
      >
        <CIcon name="cil-pencil"></CIcon>
      </CButton>
    </div>
    <CForm @submit="editPoll">
      <CModal
        title="Edit Poll"
        color="default"
        size="lg"
        :show.sync="show"
        centered
        :close-on-backdrop="false"
      >
        <BasePollForm :poll="newPoll" :from_edit="true" @set-val="setVal" />
        <template slot="footer">
          <CButton color="success" variant="outline" type="submit"
            >Edit</CButton
          >
        </template>
      </CModal>
    </CForm>
  </div>
</template>

<script>
import BasePollForm from './BasePollForm';

export default {
  name: 'EditPollModal',
  components: { BasePollForm },
  props: {
    poll: {
      type: Object,
      required: true,
    },
    section: {
      type: Boolean,
    },
  },
  data() {
    return {
      show: false,
    };
  },
  computed: {
    newPoll() {
      let obj = {};
      Object.entries(this.poll).forEach(([prop, val]) => {
        obj[prop] = val;
      });
      return obj;
    },
  },
  methods: {
    editPoll(e) {
      e.preventDefault();
      let msg = 'Poll will be updated. Continue?';
      if (confirm(msg)) {
        let newObj = {};
        Object.entries(this.newPoll).forEach(([prop, val]) => {
          newObj[prop] = val;
        });
        newObj.is_sponsored = this.newPoll.is_sponsored !== 'Yes' ? 0 : 1;
        newObj.advertiser =
          this.newPoll.is_sponsored !== 'Yes' ? null : this.newPoll.advertiser;
        newObj.max_desired_responses =
          this.newPoll.is_sponsored !== 'Yes'
            ? ''
            : this.newPoll.max_desired_responses;
        newObj.random = this.newPoll.random === 'Yes' ? 1 : 0;
        if (this.validateValues(newObj)) {
          this.$store
            .dispatch('updatePoll', newObj)
            .then(() => {
              this.show = !this.show;
            })
            .catch((err) => {
              this.show = !this.show;
              console.log(err);
            });
        } else {
          alert('Please fill in all the required fields before proceeding');
        }
      }
    },
    validateValues(item) {
      let pass = true;
      let check = [
        'text',
        'name',
        'status',
        'language',
        'option_type',
        'close_at',
        'voting_status',
        'published_date',
      ];
      Object.entries(item).forEach(([prop, val]) => {
        if (check.includes(prop) && val === '') {
          pass = false;
        }
        if (prop === 'is_sponsored' && val === 1) {
          Object.entries(item).forEach(([prop2, val2]) => {
            if (prop2 === 'advertiser' && (val2 === '' || val2 === null)) {
              pass = false;
            }
            if (prop2 === 'max_desired_responses' && !/[0-9]+/g.test(val2)) {
              pass = false;
            }
          });
        }
      });
      return pass;
    },
    setVal(pass) {
      this.newPoll[pass.prop] = pass.val;
    },
  },
};
</script>

<style scoped></style>
