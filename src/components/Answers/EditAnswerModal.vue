<template>
  <div>
    <CButton
      :disabled="poll.status ? poll.status == 'archived' : false"
      color="warning"
      variant="outline"
      v-c-tooltip="'Edit Answer'"
      @click="show = true"
    >
      <CIcon name="cil-pencil"></CIcon>
    </CButton>
    <CForm @submit="editAnswer">
      <CModal
        title="Edit Answer"
        color="default"
        :show.sync="show"
        centered
        :close-on-backdrop="false"
      >
        <BaseAnswerForm
          :is_edit="true"
          :answer="answer"
          :from_wizard="from_wizard"
        />
        <template slot="footer">
          <CButton
            color="success"
            variant="outline"
            type="submit"
            :disabled="is_disabled"
            >Edit</CButton
          >
        </template>
        <OverlayLoader v-if="answer_loading" />
      </CModal>
    </CForm>
  </div>
</template>

<script>
import BaseAnswerForm from './BaseAnswerForm';
import OverlayLoader from '../views/OverlayLoader';

export default {
  name: 'EditAnswerModal',
  components: { OverlayLoader, BaseAnswerForm },
  props: {
    answer: {
      type: Object,
      required: true,
    },
    from_wizard: {
      type: Boolean,
    },
    poll: {
      type: Object,
    },
  },
  computed: {
    answer_loading() {
      return this.$store.getters.getAnswerLoading;
    },
    is_disabled() {
      let errors = [];
      Object.entries(this.answer).forEach(([prop, val]) => {
        if (prop === 'text' && val === '') {
          errors.push('text');
        }
        if (prop === 'is_feedback' && val === '') {
          errors.push('is_feedback');
        }
        if (prop === 'is_rate' && val.length === 0) {
          errors.push('is_rate');
        }
        if (prop === 'skip' && val === '') {
          errors.push('skip');
        }
      });
      return errors.length > 0;
    },
  },
  data() {
    return {
      show: false,
    };
  },
  methods: {
    editAnswer(e) {
      e.preventDefault();
      let msg = 'Answer will be updated. Continue?';
      if (confirm(msg)) {
        this.$store
          .dispatch('updateAnswer', this.answer)
          .then(() => {
            this.show = !this.show;
          })
          .catch((err) => {
            this.show = !this.show;
            console.log(err);
          });
      }
    },
  },
};
</script>

<style scoped></style>
