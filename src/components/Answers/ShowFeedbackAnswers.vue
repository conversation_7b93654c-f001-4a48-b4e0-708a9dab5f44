<template>
  <div>
    <CButton shape="pill" variant="outline" color="info" @click="show = true"
      >Feedback Answers</CButton
    >
    <CModal title="User Feedback Answers" size="lg" :show.sync="show" centered>
      <CDataTable
        sorter
        pagination
        items-per-page-select
        :items-per-page="5"
        table-filter
        :fields="feedback_fields"
        :items="feedback_ans"
      >
      </CDataTable>
      <template slot="footer">
        <div></div>
      </template>
    </CModal>
  </div>
</template>

<script>
export default {
  name: 'ShowFeedbackAnswers',
  props: {
    feedback_ans: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      show: false,
      feedback_fields: [
        { key: 'content', label: 'Answer' },
        { key: 'user_rate', label: 'User Rated' },
        { key: 'skip', label: 'Skip' },
      ],
    };
  },
};
</script>

<style scoped></style>
