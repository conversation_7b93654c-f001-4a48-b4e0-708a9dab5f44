<template>
  <div>
    <CButton
      :disabled="poll.status ? poll.status == 'archived' : false"
      shape="pill"
      variant="outline"
      color="success"
      @click="show = true"
      >Add Answer</CButton
    >
    <CForm @submit.prevent="createAnswer">
      <CModal
        title="Add Answer"
        :show.sync="show"
        centered
        :close-on-backdrop="false"
      >
        <BaseAnswerForm :answer="answer" :is_edit="false" />
        <template slot="footer">
          <CButton
            shape="pill"
            variant="outline"
            color="success"
            type="submit"
            :disabled="is_disabled"
            >Add</CButton
          >
        </template>
        <OverlayLoader v-if="answer_loading" />
      </CModal>
    </CForm>
  </div>
</template>

<script>
import BaseAnswerForm from './BaseAnswerForm';
import OverlayLoader from '../views/OverlayLoader';

export default {
  name: 'AddAnswerModal',
  components: { OverlayLoader, BaseAnswerForm },
  props: {
    poll: {
      type: Object,
      required: true,
    },
    created_ans: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      show: false,
      answer: {
        poll_id: this.$route.params.poll_id,
        is_feedback: '',
        is_rate: '',
        skip: '',
        text: '',
        text_MS: '',
        text_ZH: '',
        total_votes: 0,
      },
      config: {
        headers: {
          token: process.env.VUE_APP_TOKEN_VALUE,
        },
      },
    };
  },
  computed: {
    answer_loading() {
      return this.$store.getters.getAnswerLoading;
    },
    is_disabled() {
      let errors = [];
      Object.entries(this.answer).forEach(([prop, val]) => {
        if (prop === 'text' && val === '') {
          errors.push('text');
        }
        if (prop === 'is_feedback' && val === '') {
          errors.push('is_feedback');
        }
        if (prop === 'is_rate' && val === '') {
          errors.push('is_rate');
        }
        if (prop === 'skip' && val === '') {
          errors.push('skip');
        }
      });
      return errors.length > 0;
    },
  },
  methods: {
    createAnswer() {
      let msg = 'Answer will be added to the poll. Continue?';
      if (confirm(msg)) {
        this.$store
          .dispatch('addPollAnswer', this.answer)
          .then(() => {
            this.show = !this.show;
          })
          .catch(() => {
            this.show = !this.show;
          });
      }
    },
  },
};
</script>

<style scoped></style>
