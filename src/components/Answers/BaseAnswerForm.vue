<template>
  <div>
    <div v-if="!is_edit">
      <CRow>
        <CCol>
          <CInput
            v-model="answer.text"
            label="Poll Answer (EN)"
            v-bind:invalid-feedback="'Poll Answer (EN) is required'"
            :is-valid="validator"
          />
          <CInput v-model="answer.text_MS" label="Poll Answer (MS)" />
          <CInput v-model="answer.text_ZH" label="Poll Answer (ZH)" />
          <CSelect
            label="Is Rate"
            :value.sync="answer.is_rate"
            :options="is_items"
            placeholder="Please Select if is Rating Type"
            :selected="emitSelect"
          />
          <CSelect
            label="Is Feedback"
            :value.sync="answer.is_feedback"
            :options="is_items"
            placeholder="Please Select if is Feedback Type"
            :selected="emitSelect"
          />
          <CSelect
            label="Is Skip"
            :value.sync="answer.skip"
            :options="is_items"
            placeholder="Please Select if is Skip Button"
            :selected="emitSelect"
          />
        </CCol>
      </CRow>
    </div>
    <div v-else>
      <CRow>
        <CCol>
          <CInput
            v-model="answer.text"
            label="Poll Answer (EN)"
            v-bind:invalid-feedback="'Poll Answer (EN) is required'"
            :is-valid="validator"
          />
          <CInput v-model="answer.text_MS" label="Poll Answer (MS)" />
          <CInput v-model="answer.text_ZH" label="Poll Answer (ZH)" />
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CInput v-model="answer.total_votes" readonly label="Total Votes" />
        </CCol>
      </CRow>
      <CRow>
        <CCol>
          <CSelect
            :value.sync="answer.is_rate"
            :options="is_items"
            label="Is Rate"
            :disabled="!from_wizard"
            :invalid-feedback="from_wizard ? 'Is Rate is required' : ''"
            :is-valid="from_wizard ? validator : true"
          />
        </CCol>
        <CCol>
          <CSelect
            :value.sync="answer.is_feedback"
            :options="is_items"
            label="Is Feedback"
            :invalid-feedback="from_wizard ? 'Is Feedback is required' : ''"
            :is-valid="from_wizard ? validator : true"
            :disabled="!from_wizard"
          />
        </CCol>
        <CCol>
          <CSelect
            :value.sync="answer.skip"
            :options="is_items"
            label="Is Skip"
            :disabled="!from_wizard"
            :invalid-feedback="from_wizard ? 'Is Skip is required' : ''"
            :is-valid="from_wizard ? validator : true"
          />
        </CCol>
      </CRow>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BaseAnswerForm',
  props: {
    from_wizard: {
      type: Boolean,
    },
    answer: {
      type: Object,
      required: true,
    },
    is_edit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      is_items: [
        { value: 1, label: 'Yes' },
        { value: 0, label: 'No' },
      ],
    };
  },
  methods: {
    emitSelect() {
      this.$emit('pass-select');
    },
    validator(val) {
      return val ? val !== '' : false;
    },
  },
};
</script>

<style scoped></style>
