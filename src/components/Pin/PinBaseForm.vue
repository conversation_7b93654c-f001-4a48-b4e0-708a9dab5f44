<template>
  <div>
    <CInput
      v-model="pin.unique_id"
      label="Unique ID"
      :is-valid="validUniqueId"
      :invalid-feedback="uniqueIdErrorMsg"
      :disabled="from_edit"
    />
    <div>
      <CRow>
        <CCol>
          <label>{{ feedOptionLabel }}</label>
          <multiselect
            v-model="pin.feed"
            :options="feedOptions"
            label="name"
            track-by="id"
            :allow-empty="false"
            :preselect-first="true"
          >
            <span slot="noResult"
              >Oops! No feed with the name. Consider changing the search
              query.</span
            ></multiselect
          >
        </CCol>
      </CRow>
      <br />
    </div>
    <div>
      <CRow>
        <CCol>Pin Time</CCol>
        <CCol>
          <div
            class="form-check form-check-inline"
            v-for="(option, optionIndex) in schedule_options"
            :key="option.value + optionIndex + 'schedule'"
          >
            <input
              class="form-check-input"
              type="radio"
              :id="option.value + optionIndex + 'schedule'"
              :value="option.value"
              v-model="pin.scheduled"
            />
            <label
              class="form-check-label"
              :for="option.value + optionIndex + 'schedule'"
              >{{ option.label }}</label
            >
          </div>
        </CCol>
      </CRow>
      <br />
    </div>
    <div>
      <CRow>
        <CCol v-show="pin.scheduled == 1">
          <label for="">Start Date</label>
          <vc-date-picker
            v-model="pin.start"
            mode="dateTime"
            :model-config="datePickerModelConfig"
            :min-date="this.currentDate()"
            :valid-hours="startValidHours"
          >
            <template v-slot="{ inputValue, inputEvents }">
              <div class="input-group">
                <div class="input-group-prepend">
                  <span class="input-group-text">
                    <CIcon name="cil-calendar"></CIcon>
                  </span>
                </div>
                <input
                  class="form-control"
                  :value="inputValue"
                  v-on="inputEvents"
                />
              </div>
            </template>
          </vc-date-picker>
          <span
            v-if="startDateErrors && !disableStartDate"
            v-text="startDateErrorMsg"
            class="invalid-error"
          />
        </CCol>
        <CCol>
          <label for="">End Date</label>
          <vc-date-picker
            v-model="pin.end"
            mode="dateTime"
            :model-config="datePickerModelConfig"
            :min-date="minEndDate"
          >
            <template v-slot="{ inputValue, inputEvents }">
              <div class="input-group">
                <div class="input-group-prepend">
                  <span class="input-group-text">
                    <CIcon name="cil-calendar"></CIcon>
                  </span>
                </div>
                <input
                  :disabled="disableEndDate"
                  class="form-control"
                  :value="inputValue"
                  v-on="inputEvents"
                />
              </div>
            </template>
          </vc-date-picker>
          <span
            v-if="endDateErrors && !disableEndDate"
            v-text="endDateErrorMsg"
            class="invalid-error"
          />
        </CCol>
      </CRow>
      <br />
    </div>
    <CRow>
      <CCol>Main Language</CCol>
      <CCol>
        <div
          class="form-check form-check-inline"
          :key="option + optionIndex"
          v-for="(option, optionIndex) in language_options"
          :for="option.value + optionIndex + 'pin'"
        >
          <input
            class="form-check-input"
            type="checkbox"
            :value="option.value"
            :id="option.value + optionIndex + 'pin'"
            v-model="pin.language"
          />
          <label
            class="form-check-label"
            :for="option.value + optionIndex + 'pin'"
            >{{ option.label }}</label
          >
        </div>
      </CCol>
    </CRow>

    <br />
    <CRow>
      <CCol>Match Sub-Language</CCol>
      <CCol>
        <div
          class="form-check form-check-inline"
          v-for="(option, optionIndex) in yesno_options"
          :key="option.value + optionIndex + 'match'"
        >
          <input
            class="form-check-input"
            type="radio"
            :id="option.value + optionIndex + 'match'"
            :value="option.value"
            v-model="pin.matchSubLang"
          />
          <label
            class="form-check-label"
            :for="option.value + optionIndex + 'match'"
            >{{ option.label }}</label
          >
        </div>
      </CCol>
    </CRow>
    <br />
  </div>
</template>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<script>
import { format, isBefore, parseISO, compareAsc, isToday } from 'date-fns';
import { mapGetters, mapActions } from 'vuex';
import Multiselect from 'vue-multiselect';
export default {
  name: 'PinBaseForm',
  components: { Multiselect },
  props: {
    pin: {
      type: Object,
      required: true,
    },
    article: {
      type: Boolean,
      default: true,
    },
    from_edit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      initialStartDate: this.pin.start,
      initialEndDate: this.pin.end,
      datePickerModelConfig: {
        type: 'string',
        mask: 'YYYY-MM-DD HH:mm:ss',
      },
      yesno_options: [
        { value: '1', label: 'Yes' },
        { value: '0', label: 'No' },
      ],
      schedule_options: [
        { value: '0', label: 'Now' },
        { value: '1', label: 'Scheduled' },
      ],
      language_options: [
        { value: 'en', label: 'EN' },
        { value: 'ms', label: 'MS' },
        { value: 'zh', label: 'ZH' },
      ],
    };
  },
  mounted() {
    this.listPin();
  },
  computed: {
    ...mapGetters({
      pinFeeds: 'getPinFeeds',
    }),
    validUniqueId() {
      let valid = true;
      if (this.pin.unique_id !== '' || this.pin.unique_id.length > 0) {
        const currentType = this.pin.type;
        const firstChar = this.pin.unique_id.charAt(0);
        if (currentType === 'article' && firstChar !== 'A') {
          valid = false;
        } else if (currentType === 'video' && firstChar !== 'V') {
          valid = false;
        } else if (currentType === 'podcast' && firstChar !== 'P') {
          valid = false;
        }
      } else {
        valid = false;
      }
      return valid;
    },
    uniqueIdErrorMsg() {
      let text = '';
      if (!this.validUniqueId) {
        text = "Unique ID doesn't match content type";
      }
      if (this.pin.unique_id === '') {
        text = 'Unique ID is required';
      }
      return text;
    },
    startDateErrors() {
      if (isBefore(parseISO(this.pin.start), parseISO(this.currentDate()))) {
        return true;
      } else if (this.pin.start === '') {
        return true;
      } else {
        return false;
      }
    },
    startDateErrorMsg() {
      if (isBefore(parseISO(this.pin.start), parseISO(this.currentDate()))) {
        return 'Start Date must be AFTER current time';
      } else if (this.pin.start === '') {
        return 'Start Date of pin is required';
      } else {
        return '';
      }
    },
    disableStartDate() {
      if (this.from_edit && this.initialStartDate === this.pin.start) {
        const res = isBefore(
          parseISO(this.pin.start),
          parseISO(this.currentDate())
        );
        this.$emit('disable:date-field', { field: 'start', value: res });
        return res;
      }
      return false;
    },
    disableEndDate() {
      if (this.from_edit && this.initialEndDate === this.pin.end) {
        const res = isBefore(
          parseISO(this.pin.end),
          parseISO(this.currentDate())
        );
        this.$emit('disable:date-field', { field: 'end', value: res });
        return res;
      }
      return false;
    },
    minEndDate() {
      if (this.from_edit) {
        if (isBefore(parseISO(this.pin.start), parseISO(this.currentDate()))) {
          return new Date();
        }
      }
      return this.pin.start;
    },
    endDateErrors() {
      if (this.pin.scheduled == 1) {
        if (isBefore(parseISO(this.pin.end), parseISO(this.pin.start))) {
          return true;
        } else if (
          compareAsc(parseISO(this.pin.end), parseISO(this.pin.start)) === 0
        ) {
          return true;
        }
      } else {
        if (isBefore(parseISO(this.pin.end), parseISO(this.currentDate()))) {
          return true;
        }
      }
      if (this.pin.end === '') {
        return true;
      }
      return false;
    },
    endDateErrorMsg() {
      if (this.pin.scheduled == 1) {
        if (isBefore(parseISO(this.pin.end), parseISO(this.pin.start))) {
          return 'End Date must be AFTER Start Date';
        } else if (
          compareAsc(parseISO(this.pin.end), parseISO(this.pin.start)) === 0
        ) {
          return 'End Date must be AFTER Start Date';
        }
      } else {
        if (isBefore(parseISO(this.pin.end), parseISO(this.currentDate()))) {
          return 'End Date must be AFTER Current Date/Time';
        }
      }
      if (this.pin.end === '') {
        return 'End Date of pin is required';
      } else {
        return '';
      }
    },
    feedOptions() {
      const special = ['article', 'video'];
      const feeds = [];
      if (special.includes(this.pin.type)) {
        Object.entries(this.pinFeeds).forEach(([prop, val]) => {
          if (special.includes(prop)) {
            val.content.forEach((i) => {
              const idx = feeds.findIndex((f) => f.id === i.id);
              if (idx === -1) {
                feeds.push(i);
              }
            });
          }
        });
      } else {
        feeds.push(...this.pinFeeds[this.pin.type].content);
      }
      return feeds;
    },
    feedOptionLabel() {
      let label = 'Feed';
      if (this.pin.type === 'podcast') {
        label = 'Podcast Feed';
      } else if (this.pin.type === 'highlight') {
        label = 'Active Highlight Feed';
      }
      return label;
    },
  },
  methods: {
    ...mapActions({
      listPin: 'listPinFeeds',
    }),
    startValidHours(selectedHour) {
      if (isToday(parseISO(this.pin.s))) {
        const nowHour = new Date(this.pin.s).getHours();
        return selectedHour >= nowHour;
      }
      return [];
    },
    currentDate() {
      return format(new Date(), 'yyyy-MM-dd HH:mm:ss');
    },
  },
};
</script>

<style scoped>
.invalid-border {
  border: 1px solid #e55353;
  border-radius: 6px;
}
.invalid-error {
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #e55353;
}
</style>
