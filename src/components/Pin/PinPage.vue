<template>
  <div>
    <CCard>
      <CCardHeader>Pin Content</CCardHeader>
      <CCardBody>
        <div v-show="checkAction('pin_add')">
          <CForm>
            <CRow>
              <CCol>Type</CCol>
              <CCol>
                <div
                  class="form-check form-check-inline"
                  v-for="(option, optionIndex) in type_options"
                  :key="option.value + optionIndex + 'match'"
                >
                  <input
                    class="form-check-input"
                    type="radio"
                    :id="option.value + optionIndex + 'match'"
                    :value="option.value"
                    v-model="pin.type"
                    @change="typeChange"
                  />
                  <label
                    class="form-check-label"
                    :for="option.value + optionIndex + 'match'"
                    >{{ option.label }}</label
                  >
                </div>
              </CCol>
            </CRow>
            <br />
            <PinBaseForm :pin="pin" />
            <CButton
              block
              color="success"
              variant="outline"
              :disabled="is_disabled"
              @click="createPin"
              >Submit</CButton
            >
          </CForm>
        </div>

        <hr />
        <div v-show="checkAction('pin_list')">
          <h4>Pinned Items</h4>
          <CDataTable
            :key="forceRenderTable"
            pagination
            small
            :fields="pin_fields"
            :items-per-page="10"
            :items="pinnedItems"
          >
            <template #start="{ item }">
              <td v-if="displayDate(item.start)">
                {{ item.s }}
              </td>
              <td v-else>
                {{ `-` }}
              </td>
            </template>
            <template #end="{ item }">
              <td>
                {{ item.e }}
              </td>
            </template>
            <template #type="{ item }">
              <td>
                {{ item.type[0].toUpperCase() + item.type.substr(1) }}
              </td>
            </template>
            <template #feed="{ item }">
              <td>
                {{ item.feed !== null ? item.feed.name : 'Highlight' }}
              </td>
            </template>
            <template #language="{ item }">
              <td>
                {{ item.language.join(', ').toUpperCase() }}
              </td>
            </template>
            <template #matchSubLang="{ item }">
              <td>
                {{ item.matchSubLang == 1 ? 'Yes' : 'No' }}
              </td>
            </template>
            <template #action="{ item }">
              <td>
                <CButtonToolbar>
                  <div v-if="checkDate(item)">
                    <PinEditModal
                      :pin="item"
                      :from_edit="true"
                      :key="item.id"
                    />
                    <div v-show="checkAction('pin_unpin')">
                      <CButton
                        :key="item.id"
                        variant="outline"
                        color="danger"
                        v-c-tooltip="'Unpin'"
                        @click="unpin(item)"
                      >
                        <CIcon name="cil-trash"></CIcon>
                      </CButton>
                    </div>
                  </div>
                </CButtonToolbar>
              </td>
            </template>
          </CDataTable>
        </div>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import { format, isBefore, parseISO } from 'date-fns';
import { mapGetters, mapActions } from 'vuex';
import PinBaseForm from './PinBaseForm';
import PinEditModal from './PinEditModal';
export default {
  name: 'PinPage',
  components: { PinBaseForm, PinEditModal },
  data() {
    return {
      now: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
      pin_fields: [
        { key: 'start', label: 'Start Date' },
        { key: 'end', label: 'End Date' },
        { key: 'type', label: 'Type' },
        { key: 'unique_id', label: 'Unique ID' },
        { key: 'feed', label: 'Feed' },
        { key: 'language', label: 'Main Language' },
        { key: 'matchSubLang', label: 'Match Sub-Language' },
        { key: 'action', label: 'Action' },
      ],
      type_options: [
        { value: 'article', label: 'Article' },
        { value: 'video', label: 'Video' },
        { value: 'podcast', label: 'Podcast' },
        { value: 'highlight', label: 'Highlight' },
      ],
      pin: {
        id: 0,
        unique_id: '',
        language: [],
        feed: [],
        scheduled: 0,
        start: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
        end: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
        matchSubLang: 0,
        type: 'article',
      },
    };
  },
  created() {
    this.listPinned();
  },
  computed: {
    ...mapGetters({
      pinnedItems: 'getPinnedItems',
      forceRenderTable: 'getPinReload',
      pinnedLoading: 'getPinnedLoading',
    }),
    is_disabled() {
      let errors = [];
      Object.entries(this.pin).forEach(([prop, val]) => {
        if (prop === 'unique_id') {
          if (val.length > 0 || val !== '') {
            const currentType = this.pin.type;
            const firstChar = this.pin.unique_id.charAt(0);
            if (currentType === 'article' && firstChar !== 'A') {
              errors.unshift(prop);
            } else if (currentType === 'video' && firstChar !== 'V') {
              errors.unshift(prop);
            } else if (currentType === 'podcast' && firstChar !== 'P') {
              errors.unshift(prop);
            }
          }
        }
        if (prop === 'start') {
          if (this.pin.scheduled == 1 && this.pin.start !== '') {
            if (isBefore(parseISO(val), parseISO(this.now))) {
              errors.unshift(prop);
            }
          }
        }
        if (prop === 'end') {
          if (this.pin.scheduled == 1 && this.pin.end !== '') {
            if (isBefore(parseISO(val), parseISO(this.pin.start))) {
              errors.unshift(prop);
            }
          } else if (this.pin.scheduled == 0 && this.pin.end !== '') {
            if (isBefore(parseISO(val), parseISO(this.now))) {
              errors.unshift(prop);
            }
          }
        }
        if (val.length === 0 || val === '' || val === null) {
          errors.unshift(prop);
        }
      });
      return errors.length > 0;
    },
  },
  methods: {
    ...mapActions({
      listPinned: 'getPinned',
      sendPin: 'submitPin',
      undoPin: 'unpinItem',
    }),
    typeChange() {
      this.pin.feed = [];
      this.pin.unique_id = '';
    },
    createPin() {
      let msg = `Pin Content ID ${this.pin.unique_id}`;
      const currentType = this.pin.type;
      const firstChar = this.pin.unique_id.charAt(0);
      if (this.pin.scheduled === '0') {
        const currentHour = new Date().getHours();
        this.pin.start = this.now;
        this.pin.s = this.now;
        this.pin.start_hour = currentHour;
      }
      if (currentType === 'article' && firstChar === 'A') {
        msg = msg.replace('Content', 'Article');
      } else if (currentType === 'video' && firstChar === 'V') {
        msg = msg.replace('Content', 'Video');
      } else if (currentType === 'podcast' && firstChar === 'P') {
        msg = msg.replace('Content', 'Podcast');
      }
      this.$swal
        .fire({
          icon: 'question',
          text: msg,
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Yes',
          cancelButtonText: 'Cancel',
          buttonsStyling: true,
          reverseButtons: true,
        })
        .then((res) => {
          if (res.isConfirmed) {
            this.sendPin(this.pin);
            this.resetPin();
          }
        });
    },
    unpin(pin) {
      let msg = `Unpin Content ${pin.unique_id}?`;
      if (pin.type == 'article') {
        msg = `Unpin Article ID ${pin.unique_id}?`;
      }
      if (pin.type == 'video') {
        msg = `Unpin Video ID ${pin.unique_id}?`;
      }
      if (pin.type == 'podcast') {
        msg = `Unpin Podcast ID ${pin.unique_id}?`;
      }
      this.$swal({
        text: msg,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes',
        cancelButtonText: 'Cancel',
        reverseButtons: true,
        buttonsStyling: true,
      }).then((isConfirm) => {
        if (isConfirm.value === true) {
          this.undoPin(pin);
        }
      });
    },
    checkDate(pin) {
      return isBefore(new Date(), parseISO(pin.end));
    },
    displayDate(date) {
      let compare = new Date('1970-01-01').setHours(0);
      let current = new Date(date).setHours(0);
      if (current == compare) {
        return false;
      }
      return true;
    },
    resetPin() {
      this.now = format(new Date(), 'yyyy-MM-dd HH:mm:ss');
      this.pin = {
        id: 0,
        unique_id: '',
        language: [],
        feed: [],
        scheduled: 0,
        start: this.now,
        end: this.now,
        matchSubLang: 0,
        type: 'article',
      };
    },
  },
};
</script>

<style scoped></style>
