<template>
  <div>
    <div v-show="checkAction('pin_edit')">
      <CButton
        class="float-right"
        variant="outline"
        color="warning"
        @click="show = true"
        v-c-tooltip="'Edit Pin'"
        ><CIcon name="cil-pencil"></CIcon
      ></CButton>
    </div>
    <CModal
      title="Edit Pin"
      color="default"
      size="lg"
      :show.sync="show"
      centered
      @update:show="onClose"
    >
      <PinBaseForm
        :pin="pin_data"
        :from_edit="from_edit"
        @disable:date-field="toDisable"
      />
      <template slot="footer">
        <CButton
          block
          color="success"
          variant="outline"
          :disabled="isEditDisabled"
          @click="updatePinData"
          >Edit</CButton
        >
      </template>
    </CModal>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import { format } from 'date-fns';
import PinBaseForm from './PinBaseForm';
export default {
  name: 'EditPinModal',
  components: { PinBaseForm },

  props: {
    pin: {
      type: Object,
      required: true,
    },
    from_edit: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      startEditable: false,
      show: false,
      pin_data: JSON.parse(JSON.stringify(this.pin)),
      current_data: JSON.parse(JSON.stringify(this.pin)),
      e_pin: this.pin,
    };
  },
  watch: {
    pin() {
      this.pin_data = JSON.parse(JSON.stringify(this.pin));
    },
  },
  computed: {
    isEditDisabled() {
      let errors = [];
      Object.entries(this.pin_data).forEach(([prop, val]) => {
        /*        if(prop === 'start' && this.pin_data.scheduled == 1 && this.startEditable){
          if(isBefore(parseISO(val), parseISO(this.now))){
            errors.unshift(prop)
          }
        }
        if(prop === 'end'){
          if(isBefore(parseISO(val), parseISO(this.now))){
            errors.unshift(prop);
          }
        }*/
        if (val.length === 0 || val === '' || val === null) {
          errors.unshift(prop);
        }
      });

      if (JSON.stringify(this.current_data) === JSON.stringify(this.pin_data)) {
        return true;
      }

      return errors.length > 0;
    },
  },
  methods: {
    ...mapActions({
      editPin: 'editPin',
    }),
    updatePinData() {
      let msg = `Update Pin Content ID ${this.pin_data.unique_id}`;
      const currentType = this.pin.type;
      const firstChar = this.pin.unique_id.charAt(0);

      if (this.pin_data.scheduled === '0') {
        const now = format(new Date(), 'yyyy-MM-dd HH:mm:ss');
        const currentHour = new Date().getHours();
        this.pin_data.start = now;
        this.pin_data.s = now;
        this.pin_data.start_hour = currentHour;
      }
      if (currentType === 'article' && firstChar === 'A') {
        msg = msg.replace('Content', 'Article');
      } else if (currentType === 'video' && firstChar === 'V') {
        msg = msg.replace('Content', 'Video');
      } else if (currentType === 'podcast' && firstChar === 'P') {
        msg = msg.replace('Content', 'Podcast');
      }
      this.$swal({
        icon: 'question',
        text: msg,
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes',
        cancelButtonText: 'Cancel',
        reverseButtons: true,
        buttonsStyling: true,
      }).then((res) => {
        if (res.isConfirmed) {
          this.editPin(this.pin_data);
        }
      });
    },
    onClose() {
      this.pin_data = JSON.parse(JSON.stringify(this.pin));
    },
    toDisable(payload) {
      if (payload.field === 'start') {
        this.startEditable = payload.value;
      }
    },
  },
};
</script>

<style scoped></style>
