<template>
  <div>
    <CCard>
      <CCardHeader> Daily eCPM </CCardHeader>
      <CCardBody>
        <CRow>
          <CCol>
            <CCard>
              <date-range-picker
                v-model="dateRange"
                class="float-left"
                :ranges="false"
                :locale-data="{ firstDay: 1, format: 'yyyy/mm/dd' }"
                @update="updateDate"
              >
              </date-range-picker>
            </CCard>
          </CCol>
        </CRow>
        <CRow>
          <CCol>
            <CDataTable
              v-bind:loading="cpmloading"
              :sorter-value="{ column: 'trackDate', asc: false }"
              sorter
              small
              items-per-page-select
              :items-per-page="10"
              :items="cpm"
              :fields="fields"
              pagination
            >
              <template #trackDate="{ item }">
                <td>
                  {{ item.trackDate ? formatDate(item.trackDate, '/') : '-' }}
                </td>
              </template>
              <template #fb="{ item }">
                <td>
                  {{ item.fb ? item.fb.toFixed(3) : '-' }}
                </td>
              </template>
              <template #admob="{ item }">
                <td>
                  {{ item.admob ? item.admob.toFixed(3) : '-' }}
                </td>
              </template>
              <template #hms="{ item }">
                <td>
                  {{ item.hms ? item.hms.toFixed(3) : '-' }}
                </td>
              </template>
              <template #outbrain="{ item }">
                <td>
                  {{ item.outbrain ? item.outbrain.toFixed(3) : '-' }}
                </td>
              </template>
            </CDataTable>
          </CCol>
        </CRow>
        <hr />

        <CRow>
          <CCol>
            <vue-csv-import
              v-show="checkAction('ecpm_update')"
              v-model="csv"
              :map-fields="['Date', 'Source', 'CPM']"
            ></vue-csv-import>
            <CButtonToolbar class="float-right">
              <CButton
                v-show="checkUpload()"
                @click="submitCSV"
                color="success"
                variant="outline"
                shape="pill"
                >Upload</CButton
              >
            </CButtonToolbar>
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
import DateRangePicker from 'vue2-daterange-picker';
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';
import VueCsvImport from '@/components/CSVReader/CSVReader';

export default {
  name: 'ListCPM',
  components: { VueCsvImport, DateRangePicker },
  computed: {
    cpm() {
      return this.$store.getters.getCPM;
    },
    cpmloading() {
      return this.$store.getters.getCPMLoading;
    },
  },
  data() {
    let startDate = new Date();
    let endDate = new Date();
    startDate.setDate(startDate.getDate() - 7);
    endDate.setDate(endDate.getDate() - 1);
    return {
      fields: [
        { key: 'trackDate', sorter: true, label: 'Date' },
        { key: 'fb', sorter: true, label: 'Facebook (MYR)' },
        { key: 'admob', sorter: true, label: 'Admob (MYR)' },
        { key: 'hms', sorter: true, label: 'Huawei (MYR)' },
        { key: 'outbrain', sorter: true, label: 'Outbrain (MYR)' },
      ],
      csv: null,
      dateRange: { startDate, endDate },
    };
  },
  filters: {},
  created() {
    let startDate = new Date();
    let endDate = new Date();
    startDate.setDate(startDate.getDate() - 7);
    endDate.setDate(endDate.getDate() - 1);
    startDate = this.formatDate(startDate);
    endDate = this.formatDate(endDate);
    this.$store.dispatch('getCPM', { startDate, endDate });
  },
  methods: {
    updateDate(data) {
      let startDate = data.startDate;
      let endDate = data.endDate;
      this.dateRange = { startDate, endDate };
      endDate = this.formatDate(data.endDate);
      startDate = this.formatDate(data.startDate);
      this.$store.dispatch('getCPM', { startDate, endDate });
    },
    formatDate(data, separator = '-') {
      const d = new Date(data);
      const ye = new Intl.DateTimeFormat('en', { year: 'numeric' }).format(d);
      const mo = new Intl.DateTimeFormat('en', { month: 'numeric' }).format(d);
      const da = new Intl.DateTimeFormat('en', { day: '2-digit' }).format(d);
      return `${ye}${separator}${mo}${separator}${da}`;
    },
    submitCSV() {
      let cpm = { cpm: this.csv };
      this.$store.dispatch('submitCPM', cpm);
    },
    checkUpload() {
      if (this.csv == null || this.csv == '') {
        return false;
      }
      return true;
    },
  },
};
</script>

<style scoped></style>
