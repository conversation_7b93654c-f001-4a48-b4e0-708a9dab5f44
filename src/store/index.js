import Vue from 'vue';
import Vuex from 'vuex';

import advertisers from './modules/Advertiser';
import advertisements from './modules/Advertisement';
import campaigns from './modules/Campaign';
import polls from './modules/Poll';
import newsletters from './modules/Newsletter';
import search from './modules/Search';
import youtubevideos from './modules/YoutubeVideos';
import commentreport from './modules/CommentReport';
import publishers from './modules/Publisher';
import lead_forms from './modules/LeadForm';
// import wavmakers from './modules/WavMaker'
// import sabah from './modules/SabahElection'
import covid from './modules/COVID';
import user from './modules/User';
import pn from './modules/PushNotification';
import blocked from './modules/Blocked';
import pin from './modules/Pin';
import allcontent from './modules/AllContent';
import allmikrowavcontent from './modules/AllMikrowav';
import mostviewedarticles from './modules/MostViewedArticles';
import revenue from './modules/Revenue';
import cpm from './modules/CPM';
import preview from './modules/AdwavPreview';
import contentreport from './modules/ContentReport';
import announce from './modules/Announcements';

// import wavmakers from './modules/WavMaker'
// import sabah from './modules/SabahElection'
import highlights from './modules/Highlights';
import election from './modules/Election';
import cache from './modules/Cache';

Vue.use(Vuex);

const state = {
  sidebarShow: 'responsive',
  sidebarMinimize: false,
  loading: false,
  pageLoading: false,
};

const getters = {
  getLoading: (state) => state.loading,
  getPageLoading: (state) => state.pageLoading,
};

const actions = {
  setPageLoading({ commit }, status) {
    commit('SET_PAGE_LOADING', status);
  },
};

const mutations = {
  toggleSidebarDesktop(state) {
    const sidebarOpened = [true, 'responsive'].includes(state.sidebarShow);
    state.sidebarShow = sidebarOpened ? false : 'responsive';
  },
  toggleSidebarMobile(state) {
    const sidebarClosed = [false, 'responsive'].includes(state.sidebarShow);
    state.sidebarShow = sidebarClosed ? true : 'responsive';
  },
  set(state, [variable, value]) {
    state[variable] = value;
  },
  SET_GENERAL_LOADING: (state, val) => (state.loading = val),
  SET_PAGE_LOADING: (state, val) => (state.pageLoading = val),
};

export default new Vuex.Store({
  state,
  mutations,
  getters,
  actions,
  modules: {
    advertisers,
    advertisements,
    campaigns,
    polls,
    newsletters,
    search,
    covid,
    user,
    pn,
    blocked,
    commentreport,
    youtubevideos,
    pin,
    publishers,
    lead_forms,
    allcontent,
    allmikrowavcontent,
    mostviewedarticles,
    revenue,
    cpm,
    preview,
    announce,
    contentreport,
    highlights,
    election,
    cache,
    // wavmakers,
    // sabah,
  },
});
