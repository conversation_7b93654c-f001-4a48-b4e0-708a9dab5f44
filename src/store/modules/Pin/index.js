import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const state = {
  pinned: [],
  loading: false,
  pinreload: 0,
  pinFeeds: {
    article: {
      content: [],
      feedIds: [],
    },
    video: {
      content: [],
      feedIds: [],
    },
    podcast: {
      content: [],
      feedIds: [],
    },
  },
};

const getters = {
  getPinnedItems: (state) => state.pinned,
  getPinnedLoading: (state) => state.loading,
  getPinReload: (state) => state.pinreload,
  getPinFeeds: (state) => state.pinFeeds,
};

const actions = {
  getPinned({ commit, rootState }) {
    commit('SET_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_GET_PINNED_ITEMS + '?r=' + r,
        headers.createHeaders(rootState.user.token, 'abdulNewswav')
      )
      .then((res) => {
        res.data.forEach((i) => {
          i.end = i.e;
          i.start = i.s;
          return i;
        });
        commit('SET_PINNED', res.data);
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  submitPin({ commit, rootState }, pin) {
    commit('SET_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .post(
        process.env.VUE_APP_PIN_ITEM + '?r=' + r,
        pin,
        headers.createHeaders(rootState.user.token, 'abdulNewswav')
      )
      .then((res) => {
        res.data.pinned.forEach((i) => {
          i.end = i.e;
          i.start = i.s;
          return i;
        });
        commit('SET_PINNED', res.data.pinned);
        commit('SET_LOADING', false);
        commit('SET_RELOAD', r);
        Vue.swal({
          showConfirmButton: true,
          showCancelButton: false,
          confirmButtonColor: '#3085d6',
          confirmButtonText: 'Ok',
          buttonsStyling: true,
          icon: 'success',
          title: 'Success',
          text: res.data.message,
        });
      })
      .catch((err) => {
        console.log(err);
        Bugsnag.notify(err);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        if (err.response.status == 404) {
          message = err.response.data.message;
          title = 'Not Found';
        }

        if (err.response.status == 400) {
          message = err.response.data.message;
          title = 'Error';
        }
        Vue.swal({
          toast: true,
          position: 'top-end',
          showConfirmButton: true,
          confirmButtonText: 'Ok',
          showCancelButton: false,
          timer: 5000,
          icon: 'error',
          title: title,
          text: `${message}`,
        });

        commit('SET_LOADING', false);
      });
  },
  editPin({ commit, rootState }, pin) {
    commit('SET_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .put(
        process.env.VUE_APP_EDIT_PIN_ITEM + pin.type + '/' + pin.id + '?r=' + r,
        pin,
        headers.createHeaders(rootState.user.token, 'abdulNewswav')
      )
      .then((res) => {
        res.data.pinned.forEach((i) => {
          i.end = i.e;
          i.start = i.s;
          return i;
        });
        commit('SET_PINNED', res.data.pinned);
        commit('SET_LOADING', false);
        commit('SET_RELOAD', r);
        Vue.swal({
          toast: true,
          position: 'top-end',
          showConfirmButton: true,
          showCancelButton: false,
          confirmButtonText: 'Ok',
          timer: 5000,
          icon: 'success',
          title: 'Success',
          text: res.data.message,
        });
      })
      .catch((err) => {
        console.log(err);
        Bugsnag.notify(err);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        if (err.response.status == 404) {
          message =
            'Article/Video ID not found database. Check console for more info';
          title = 'manoii';
        }
        if (err.response.status == 400) {
          message = err.response.data.message;
          title = 'Error';
        }

        Vue.swal({
          toast: true,
          position: 'top-end',
          showConfirmButton: true,
          showCancelButton: false,
          confirmButtonText: 'Ok',
          timer: 5000,
          icon: 'error',
          title: title,
          text: `${message}`,
        });

        commit('SET_LOADING', false);
      });
  },
  unpinItem({ commit, rootState, dispatch }, pin) {
    commit('SET_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .delete(
        process.env.VUE_APP_UNPIN_ITEM + pin.type + '/' + pin.id + '?r=' + r,
        headers.createHeaders(rootState.user.token, 'abdulNewswav')
      )
      .then((res) => {
        commit('SET_LOADING', false);
        commit('SET_RELOAD', r);
        dispatch('getPinned');
        Vue.swal({
          showConfirmButton: true,
          showCancelButton: false,
          confirmButtonColor: '#3085d6',
          confirmButtonText: 'Ok',
          buttonsStyling: true,
          icon: 'success',
          title: 'Success',
          text: res.data.message,
        });
      })
      .catch((err) => {
        console.log(err);
        Bugsnag.notify(err);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        if (err.response.status == 400) {
          message = err.response.data.message;
          title = 'Error';
        }
        Vue.swal({
          toast: true,
          position: 'top-end',
          showConfirmButton: true,
          showCancelButton: false,
          confirmButtonText: 'Ok',
          timer: 5000,
          icon: 'error',
          title: title,
          text: `${message}`,
        });

        commit('SET_LOADING', false);
      });
  },
  async listPinFeeds({ commit, rootState }) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    return axios
      .get(
        process.env.VUE_APP_GET_PIN_FEEDS + '?r=' + r,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        commit('SET_PIN_FEEDS', res.data);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. You have no permission to access feeds data';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(error);
        Bugsnag.notify(error);
      });
  },
};

const mutations = {
  SET_LOADING: (state, val) => (state.loading = val),
  SET_PINNED: (state, data) => (state.pinned = data),
  SET_RELOAD: (state, data) => (state.pinreload = data),
  SET_PIN_FEEDS: (state, data) => (state.pinFeeds = data),
};

export default {
  state,
  getters,
  actions,
  mutations,
};
