import axios from 'axios';
import * as headers from '@/helpers/headers';
import { getUpdateScheduleText } from '@/helpers/pn';
import Vue from 'vue';

const state = {
  article_loading: false,
  pn_loading: false,
  feed_options: JSON.parse(localStorage.getItem('feeds')) || [],
  scheduled_pns: [],
  scheduled_loading: false,
  ai_pn_configurations: [],
  title_suggestions: [],
  description_suggestions: [],
  slack_channels: [],
};

const getters = {
  getAiPnConfigurations: (state) => state.ai_pn_configurations,
  getTitleSuggestions: (state) => state.title_suggestions,
  getDescriptionSuggestions: (state) => state.description_suggestions,
  getArticleLoading: (state) => state.article_loading,
  getPNLoading: (state) => state.pn_loading,
  getFeedOptions: (state) => state.feed_options,
  getScheduledPNs: (state) => state.scheduled_pns,
  getScheduledLoading: (state) => state.scheduled_loading,
  getSlackChannels: (state) => state.slack_channels,
};

const actions = {
  listFeed({ commit, rootState }) {
    axios
      .get(
        process.env.VUE_APP_LIST_APP_FEED,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then((res) => {
        let feeds = [];
        res.data.map((item) => {
          let data = {
            value: item.id,
            label: item.name,
          };
          if (!feeds.find((f) => f.value === data.value)) {
            feeds.push(data);
          }
        });
        let defaults = {
          value: '',
          label: 'All',
        };
        feeds.sort((a, b) => a.label.localeCompare(b.label));
        feeds.unshift(defaults);
        commit('SET_FEED_OPTIONS', feeds);
        localStorage.setItem('feeds', JSON.stringify(feeds));
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
      });
  },
  getArticleData({ commit, rootState }, id) {
    commit('SET_ARTICLE_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .get(
          process.env.VUE_APP_GET_ARTICLE_BY_UNIQUE_ID + id,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          commit('SET_ARTICLE_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          console.log(err.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          commit('SET_ARTICLE_LOADING', false);
          reject(err);
        });
    });
  },
  sendPush({ commit, rootState }, pn) {
    commit('SET_PN_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_SEND_PN,
          pn,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_SEND_PN_TOKEN
          )
        )
        .then((res) => {
          commit('SET_PN_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          if (err.response.status == 404) {
            // If an error with status 404 occurs, return the message itself.
            message = err.response.data.error;
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          commit('SET_PN_LOADING', false);
          reject(err);
        });
    });
  },
  getSlackChannels({ commit, rootState }) {
    commit('SET_PN_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .get(
          process.env.VUE_APP_GET_SLACK_CHANNELS,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          commit('SET_SLACK_CHANNELS', res.data);
          resolve(res);
        })
        .catch((err) => {
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          commit('SET_PN_LOADING', false);
          reject(err);
        });
    });
  },
  listAiPushNotificationConfigurations({ commit, rootState }, id = null) {
    commit('SET_PN_LOADING', true);
    let url = process.env.VUE_APP_LIST_AI_PN_CONFIGURATIONS;
    if (id) {
      url = process.env.VUE_APP_LIST_AI_PN_CONFIGURATIONS + '/' + id;
    }
    return new Promise((resolve, reject) => {
      axios
        .get(
          url,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          if (!id) {
            commit('SET_AI_PN_CONFIGURATIONS', res.data);
          }
          commit('SET_PN_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          commit('SET_PN_LOADING', false);
          reject(err);
        });
    });
  },
  createAiPnConfiguration({ rootState, commit }, config) {
    commit('SET_PN_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_UPSERT_AI_PN_CONFIGURATION,
          config,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          commit('SET_PN_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_PN_LOADING', false);
          reject(err.response.data.error);
        });
    });
  },
  updateAiPnConfiguration({ rootState, commit }, payload) {
    const { id, config } = payload;
    commit('SET_PN_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .put(
          process.env.VUE_APP_UPSERT_AI_PN_CONFIGURATION + '/' + id,
          config,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          commit('SET_PN_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_PN_LOADING', false);
          reject(err);
        });
    });
  },
  removeAiPushNotificationConfiguration({ rootState, commit }, id) {
    commit('SET_PN_LOADING', true);
    axios
      .delete(
        process.env.VUE_APP_DELETE_AI_PN_CONFIGURATION + id,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then(() => {
        alert('Configuration was successfully deleted!');
        commit('SET_PN_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        commit('SET_PN_LOADING', false);
      });
  },
  getTitleAndDescriptionSuggestions({ commit, rootState }, id) {
    commit('SET_PN_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .get(
          process.env.VUE_APP_GET_PN_TITLE_AND_DESCRIPTION_SUGGESTIONS + id,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          commit('SET_TITLE_SUGGESTIONS', res.data['titles']);
          commit('SET_DESCRIPTION_SUGGESTIONS', res.data['descriptions']);
          commit('SET_PN_LOADING', false);
        })
        .catch((err) => {
          console.log(err.response.data);
          let message = 'Content not found. Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          commit('SET_PN_LOADING', false);
          reject(err);
        });
    });
  },
  sendPush2({ commit }, pn) {
    commit('SET_PN_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_SEND_PN_2,
          pn,
          headers.createHeadersForPn2(process.env.VUE_APP_SEND_PN_2_TOKEN)
        )
        .then((res) => {
          commit('SET_PN_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          let message = 'Check console for more info';
          let title = 'Error';

          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          if (err.response.status === 404 || err.response.status === 422) {
            message = err.response.data.error;
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          commit('SET_PN_LOADING', false);
          reject(err);
        });
    });
  },
  sendTestPush({ commit, rootState }, pn) {
    commit('SET_PN_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_SEND_TEST_PN,
          pn,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_TOKEN_VALUE
          )
        )
        .then((res) => {
          let profileID = pn.profileId.split('_');
          commit('SET_PN_LOADING', false);
          alert(
            `Article ID ${pn.articleId} is successfully sent to Profile ID ${profileID[1]}`
          );
          resolve(res);
        })
        .catch((err) => {
          console.log(err.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          commit('SET_PN_LOADING', false);
          alert(
            'API returns failure. Please retry & do contact tech team if the issue persists'
          );
          reject(err);
        });
    });
  },
  checkPushHistory({ rootState }, pn) {
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_CHECK_PN_HISTORY,
          pn,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          console.log(err.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';

            Vue.notify({
              group: 'error',
              title: title,
              type: 'error',
              text: `${err.response.status} : ${message}`,
              duration: -1,
              position: 'bottom right',
              closeOnClick: true,
            });
          }

          reject(err);
        });
    });
  },

  logPush({ rootState }, pn) {
    axios
      .post(
        process.env.VUE_APP_LOG_PN,
        pn,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then((res) => {
        console.log(res);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
      });
  },
  sendVideoPN({ rootState }, vid) {
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_SEND_VIDEO_PN,
          vid,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_VIDEOPN_TOKEN
          )
        )
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  sendTestVideoPN({ commit, rootState }, vid) {
    axios
      .post(
        process.env.VUE_APP_SEND_TEST_VIDEO_PN,
        vid,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_ADMIN_VIDEOPN_TOKEN
        )
      )
      .then(() => {
        let profileID = vid.profileId.split('_');
        commit('SET_PN_LOADING', false);
        alert(
          `Video ID ${vid.videoId} is successfully sent to Profile ID ${profileID[1]}`
        );
      });
  },
  sendPodcastPN({ rootState }, pod) {
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_SEND_PODCAST_PN,
          pod,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_VIDEOPN_TOKEN
          )
        )
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  sendTestPodcastPN({ commit, rootState }, pod) {
    axios
      .post(
        process.env.VUE_APP_SEND_TEST_PODCAST_PN,
        pod,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_ADMIN_VIDEOPN_TOKEN
        )
      )
      .then(() => {
        let profileID = pod.profileId.split('_');
        commit('SET_PN_LOADING', false);
        alert(
          `Podcast ID ${pod.podcastId} is successfully sent to Profile ID ${profileID[1]}`
        );
      });
  },
  stopSendingPN({ rootState }, pn) {
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_STOP_SENDING_PN,
          pn,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          Vue.swal({
            icon: 'success',
            text: `Cancel PN has been sent for Resource ${pn.passId}`,
            showCancelButton: false,
          });
          resolve(res);
        })
        .catch((err) => {
          Vue.swal({
            icon: 'error',
            text: `Something went wrong in cancelling PN for Resource ${pn.passId}`,
            showCancelButton: false,
          });
          reject(err);
        });
    });
  },
  deletePN({ rootState }, pn) {
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_DELETE_PN,
          pn,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_SEND_PN_TOKEN
          )
        )
        .then((res) => {
          Vue.swal({
            icon: 'success',
            text: `Delete PN has been sent for Resource ${pn.passId}`,
            showCancelButton: false,
          });
          resolve(res);
        })
        .catch((err) => {
          Vue.swal({
            icon: 'error',
            text: `Something went wrong in deleting PN for Resource ${pn.passId}`,
            showCancelButton: false,
          });
          reject(err);
        });
    });
  },
  deleteTestPN({ rootState }, pn) {
    axios
      .post(
        process.env.VUE_APP_DELETE_TEST_PN,
        pn,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then(() => {
        let profileID = pn.profileId.split('_');
        Vue.swal({
          icon: 'success',
          text: `Unique ID ${pn.uniqueId} PN has been successfully deleted for Profile ID ${profileID[1]}`,
          showCancelButton: false,
        });
      })
      .catch(() => {
        let profileID = pn.profileId.split('_');
        Vue.swal({
          icon: 'error',
          text: `Something went wrong in deleting PN for Article ID ${pn.uniqueId} on Profile ID ${profileID[1]}`,
          showCancelButton: false,
        });
      });
  },
  checkPeriod({ rootState }) {
    return new Promise((resolve, reject) => {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      axios
        .get(
          process.env.VUE_APP_CHECK_PN_PERIOD + '?r=' + r,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  triggerAutoPN({ rootState }, pn) {
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_TRIGGER_AUTO_PN,
          pn,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  listScheduledPN({ commit, rootState }, id = null) {
    commit('SET_PN_LOADING', true);
    let url = process.env.VUE_APP_SCHEDULED_PN;
    if (id) {
      url = process.env.VUE_APP_SCHEDULED_PN + id;
    }
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    return new Promise((resolve, reject) => {
      axios
        .get(url + '?r=' + r, headers.createHeaders(rootState.user.token))
        .then((res) => {
          if (res.data.length > 0) {
            const fields = [
              'articleId',
              'title',
              'body',
              'platform',
              'postfix',
              'matchSubLang',
              'language',
              'ignoreSegment',
              'pnPeriod',
              'skipUserFeedView',
              'instant',
              'nonPreiodic',
              'pnTopicId',
              'nonActiveUsers',
              'has_title',
              'matchInterest',
            ];
            const custom = [
              'platform',
              'pnTopicId',
              'has_title',
              'title',
              'body',
              'language',
            ];
            res.data.forEach((data) => {
              data.languageShow = data.payload.language;
              if (
                data.payload?.pnPeriod !== undefined &&
                data.payload.pnPeriod.length > 0
              ) {
                data.payload.pnPeriod = data.payload.pnPeriod.split(',');
              } else {
                data.payload.pnPeriod = [];
              }
              if (data.payload.pnPeriod.length > 0) {
                let pnPeriodShow = [];
                data.payload.pnPeriod.forEach((item) => {
                  switch (item) {
                    case '1':
                      pnPeriodShow.push('Low');
                      break;
                    case '2':
                      pnPeriodShow.push('Medium');
                      break;
                    case '3':
                      pnPeriodShow.push('High');
                      break;
                  }
                });
                data.pnPeriodShow = pnPeriodShow.join().replace(/,/g, ', ');
              } else {
                data.pnPeriodShow = 'Auto';
              }
              fields.forEach((f) => {
                if (!(data.payload?.[f] !== undefined)) {
                  if (custom.includes(f)) {
                    if (f === 'has_title') {
                      if (
                        data.payload.title !== ' ' ||
                        data.payload.title === undefined
                      ) {
                        data.payload[f] = '1';
                      } else {
                        data.payload[f] = '0';
                      }
                    } else if (f === 'language') {
                      data.payload[f] = 'all';
                    } else {
                      data.payload[f] = '';
                    }
                  } else {
                    data.payload[f] = '0';
                  }
                }
              });
              if (
                data.languageShow == undefined ||
                data.languageShow.length === 0
              ) {
                data.languageShow = data.payload.language;
              }
              data.match_sub_lang =
                data.payload.matchSubLang === '1' ? 'Yes' : 'No';
              return data;
            });
          }
          if (id === null) {
            commit('SET_SCHEDULED_PNS', res.data);
            commit('SET_PN_LOADING', false);
          } else {
            resolve(res);
          }
        })
        .catch((err) => {
          if (id === null) {
            console.log(err);
            alert('Something went wrong in getting Scheduled PN list');
            commit('SET_PN_LOADING', false);
          } else {
            reject(err);
          }
        });
    });
  },
  updateScheduledPN({ commit, rootState }, pn) {
    commit('SET_SCHEDULED_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .put(
          process.env.VUE_APP_SCHEDULED_PN + pn.id,
          pn,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          const fields = [
            'articleId',
            'title',
            'body',
            'platform',
            'postfix',
            'matchSubLang',
            'language',
            'ignoreSegment',
            'pnPeriod',
            'skipUserFeedView',
            'instant',
            'nonPreiodic',
            'pnTopicId',
            'nonActiveUsers',
            'has_title',
            'matchInterest',
          ];
          const custom = [
            'platform',
            'pnTopicId',
            'has_title',
            'title',
            'body',
            'language',
          ];
          if (pn.status === 'cancelled') {
            Vue.swal.fire({
              icon: 'success',
              text: 'PN is successfully removed from schedule',
              showCancelButton: false,
            });
            resolve(res);
            commit('SET_REMOVE_SCHEDULED_PNS', pn.id);
          } else {
            res.data.forEach((data) => {
              data.languageShow = data.payload.language;
              if (
                data.payload?.pnPeriod !== undefined &&
                data.payload.pnPeriod.length > 0
              ) {
                data.payload.pnPeriod = data.payload.pnPeriod.split(',');
              } else {
                data.payload.pnPeriod = [];
              }
              if (data.payload.pnPeriod.length > 0) {
                let pnPeriodShow = [];
                data.payload.pnPeriod.forEach((item) => {
                  switch (item) {
                    case '1':
                      pnPeriodShow.push('Low');
                      break;
                    case '2':
                      pnPeriodShow.push('Medium');
                      break;
                    case '3':
                      pnPeriodShow.push('High');
                      break;
                  }
                });
                data.pnPeriodShow = pnPeriodShow.join().replace(/,/g, ', ');
              } else {
                data.pnPeriodShow = 'Auto';
              }
              fields.forEach((f) => {
                if (!(data.payload?.[f] !== undefined)) {
                  if (custom.includes(f)) {
                    if (f === 'has_title') {
                      if (
                        data.payload.title !== ' ' ||
                        data.payload.title === undefined
                      ) {
                        data.payload[f] = '1';
                      } else {
                        data.payload[f] = '0';
                      }
                    } else if (f === 'language') {
                      data.payload.language = 'all';
                    } else {
                      data.payload[f] = '';
                    }
                  } else {
                    data.payload[f] = '0';
                  }
                }
              });
              if (
                data.languageShow == undefined ||
                data.languageShow.length === 0
              ) {
                data.languageShow = data.payload.language;
              }
              data.match_sub_lang =
                data.payload.matchSubLang === '1' ? 'Yes' : 'No';
              return data;
            });
            commit('SET_UPDATE_SCHEDULED_PNS', res.data[0]);
            resolve(res);
            let text = getUpdateScheduleText(pn);
            Vue.swal({
              icon: 'success',
              text: text,
              showCancelButton: false,
            });
          }
          commit('SET_SCHEDULED_LOADING', false);
        })
        .catch((err) => {
          let text = err.response.data.msg;
          if (err.response.data.error !== '') {
            text = '<p>' + err.response.data.error + '</p>';
          }
          Vue.swal({
            icon: 'error',
            html: text,
            showCancelButton: false,
          });
          commit('SET_SCHEDULED_LOADING', false);
          reject(err);
        });
    });
  },
  deleteScheduledPN({ commit, rootState }, pn) {
    commit('SET_PN_LOADING', true);
    axios
      .put(
        process.env.VUE_APP_SCHEDULED_PN + pn.id,
        pn,
        headers.createHeaders(rootState.user.token)
      )
      .then(() => {
        commit('SET_REMOVE_SCHEDULED_PNS', pn.id);
        commit('SET_PN_LOADING', false);
      })
      .catch(() => {
        alert('Something went wrong');
        commit('SET_PN_LOADING', false);
      });
  },
  scalePods({ commit, rootState }, pods) {
    commit('SET_PN_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_SCALE_PODS,
          pods,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          console.log(err);
          alert('Something went wrong when scaling pods');
          commit('SET_PN_LOADING', false);
          reject(err);
        });
    });
  },
  updateScalePodsLog({ commit, rootState }, data) {
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_UPDATE_SCALE_LOG,
          data,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          commit('SET_PN_LOADING', false);
          alert(
            'Successfully scale pods! Please be sure to monitor traffic and scale pods accordingly.'
          );
          resolve(res);
        })
        .catch((err) => {
          console.log(err);
          alert('Something went wrong when updating scale down time');
          commit('SET_PN_LOADING', false);
          reject(err);
        });
    });
  },
};

const mutations = {
  SET_ARTICLE_LOADING: (state, val) => (state.article_loading = val),
  SET_PN_LOADING: (state, val) => (state.pn_loading = val),
  SET_FEED_OPTIONS: (state, list) => (state.feed_options = list),
  SET_SCHEDULED_PNS: (state, list) => (state.scheduled_pns = list),
  SET_UPDATE_SCHEDULED_PNS: (state, nSch) => {
    const idx = state.scheduled_pns.findIndex((pn) => pn.id === nSch.id);
    if (idx !== -1) {
      state.scheduled_pns.splice(idx, 1, nSch);
    }
  },
  SET_REMOVE_SCHEDULED_PNS: (state, id) =>
    (state.scheduled_pns = state.scheduled_pns.filter((pn) => pn.id !== id)),
  SET_SCHEDULED_LOADING: (state, val) => (state.scheduled_loading = val),
  SET_AI_PN_CONFIGURATIONS: (state, list) =>
    (state.ai_pn_configurations = list),
  SET_TITLE_SUGGESTIONS: (state, list) => (state.title_suggestions = list),
  SET_DESCRIPTION_SUGGESTIONS: (state, list) =>
    (state.description_suggestions = list),
  SET_SLACK_CHANNELS: (state, list) => (state.slack_channels = list),
};

export default {
  state,
  getters,
  actions,
  mutations,
};
