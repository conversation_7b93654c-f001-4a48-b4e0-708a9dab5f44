import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const state = {
  cpm: [],
  cpmloading: false,
};

const getters = {
  getCPM: (state) => state.cpm,
  getCPMLoading: (state) => state.cpmloading,
};

const actions = {
  getCPM({ commit, rootState }, { startDate, endDate }) {
    commit('SET_CPM_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    let url = process.env.VUE_APP_LIST_CPM;
    return new Promise((resolve, reject) => {
      axios
        .get(
          url + '?startDate=' + startDate + '&endDate=' + endDate + '&r=' + r,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          let cpm = res.data;
          console.log(cpm);
          commit('SET_CPM', res.data);
          commit('SET_CPM_LOADING', false);
          resolve(cpm);
        })
        .catch((err) => {
          commit('SET_CPM_LOADING', false);
          reject(err);
        });
    });
  },

  submitCPM({ commit, rootState }, cpm) {
    commit('SET_GENERAL_LOADING', true, { root: true });
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_LIST_CPM + '?r=' + r,
          cpm,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          Vue.swal({
            showConfirmButton: true,
            showCancelButton: false,
            confirmButtonColor: '#3085d6',
            confirmButtonText: 'Ok',
            buttonsStyling: true,
            icon: 'success',
            title: 'Success',
            text: 'CPM Uploaded',
          });
          resolve(res);
        })
        .catch((err) => {
          let message = 'Check console for more info';
          commit('SET_GENERAL_LOADING', false, { root: true });
          console.log('----Error----');
          console.log(err);
          Bugsnag.notify(err);
          if (err.response.data.message) {
            message = err.response.data.message;
          }
          Vue.swal({
            icon: 'error',
            text: message,
            showCancelButton: false,
          });
          reject(err);
        });
    });
  },
  submitBonus({ commit, rootState }, bonus) {
    commit('SET_GENERAL_LOADING', true, { root: true });
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_SUBMIT_BONUS + '?r=' + r,
          bonus,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          let message = res.data.message;
          Vue.swal({
            showConfirmButton: true,
            showCancelButton: false,
            confirmButtonColor: '#3085d6',
            confirmButtonText: 'Ok',
            buttonsStyling: true,
            icon: 'success',
            title: 'Success',
            text: message,
          });
          resolve(res);
        })
        .catch((err) => {
          let message = 'Check console for more info';
          commit('SET_GENERAL_LOADING', false, { root: true });
          console.log('----Error----');
          console.log(err);
          Bugsnag.notify(err);
          if (err.response.data.message) {
            message = err.response.data.message;
          }
          Vue.swal({
            icon: 'error',
            text: message,
            showCancelButton: false,
          });
          reject(err);
        });
    });
  },
};

const mutations = {
  SET_CPM: (state, cpm) => (state.cpm = cpm),
  SET_CPM_LOADING: (state, cpm) => (state.cpmloading = cpm),
};

export default {
  state,
  getters,
  actions,
  mutations,
};
