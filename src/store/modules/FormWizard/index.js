import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const state = {
  submittedCampaign: false,
  submittedAd: false,
};

const getters = {
  isAdSubmitted: (state) => state.submittedAd,
};

const actions = {
  addAdvertisement({ commit, rootState }, adv) {
    commit('SET_AD_SUBMITTED', false);
    axios
      .post(
        process.env.VUE_APP_ADD_ADVERTISEMENT_ENDPOINT,
        adv,
        headers.createHeaders(rootState.user.token)
      )
      .then(() => {
        commit('SET_AD_SUBMITTED', true);
        // alert("Advertisement has been added")
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        Bugsnag.notify(err);
      });
  },
};

const mutations = {
  SET_AD_SUBMITTED: (state, value) => (state.submittedAd = value),
};

export default {
  state,
  getters,
  mutations,
  actions,
};
