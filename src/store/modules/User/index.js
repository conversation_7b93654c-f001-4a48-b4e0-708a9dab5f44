import axios from 'axios';
import Bugsnag from '@bugsnag/js';

const state = {
  dryMode: JSON.parse(localStorage.getItem('dryMode')) || false,
  auth_loading: false,
  user: JSON.parse(localStorage.getItem('user')) || {},
  token: localStorage.getItem('token') || null,
  token_expire_at: localStorage.getItem('expiry') || null,
  roles: [],
  actions: [],
  sidebar_loading: false,
};

const getters = {
  isAuthLoading: (state) => state.auth_loading,
  isLoggedIn: (state) => {
    return (
      state.token !== null &&
      state.user !== {} &&
      state.token_expire_at !== null
    );
  },
  isTokenValid: (state) => {
    let now = new Date().getTime();
    let expire = new Date(state.token_expire_at).getTime();
    return expire > now;
  },
  getUser: (state) => state.user,
  getTokenExpire: (state) => state.token_expire_at,
  getRoles: (state) => state.roles,
  getSidebarLoading: (state) => state.sidebar_loading,
  getUserToken: (state) => state.token,
  getActions: (state) => state.actions,
};

const actions = {
  login({ commit }, user) {
    commit('SET_AUTH_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(process.env.VUE_APP_LOGIN, user)
        .then((res) => {
          commit('SET_USER', res.data.user);
          commit('SET_TOKEN', res.data.access_token);
          commit('SET_EXPIRY_DATE', res.data.expires_in);
          localStorage.setItem('user', JSON.stringify(res.data.user));
          localStorage.setItem('token', res.data.access_token);
          localStorage.setItem('expiry', res.data.expires_in);
          axios.defaults.headers.common['Authorization'] =
            `Bearer ${res.data.access_token}`;
          resolve(res);
          commit('SET_AUTH_LOADING', false);
        })
        .catch((err) => {
          reject(err.response.data.message);
          Bugsnag.notify(err);
          commit('SET_AUTH_LOADING', false);
        });
    });
  },
  logout(context) {
    context.commit('SET_AUTH_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_LOGOUT,
          {},
          { headers: { Authorization: 'Bearer ' + context.state.token } }
        )
        .then(() => {
          context.commit('SET_USER', {});
          context.commit('SET_TOKEN', null);
          context.commit('SET_EXPIRY_DATE', null);
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          localStorage.removeItem('expiry');
          delete axios.defaults.headers.common['Authorization'];
          context.commit('SET_AUTH_LOADING', false);
          resolve();
        })
        .catch((err) => {
          context.commit('SET_AUTH_LOADING', false);
          context.commit('SET_USER', {});
          context.commit('SET_TOKEN', null);
          context.commit('SET_EXPIRY_DATE', null);
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          localStorage.removeItem('expiry');
          delete axios.defaults.headers.common['Authorization'];
          context.commit('SET_AUTH_LOADING', false);
          reject(err);
          Bugsnag.notify(err);
        });
    });
  },
  sendResetEmail(context, email) {
    return new Promise((resolve, reject) => {
      axios
        .post(process.env.VUE_APP_SEND_RESET_LINK, email)
        .then((res) => {
          resolve(res.data.message);
        })
        .catch((err) => {
          reject(err.response.data.message);
          Bugsnag.notify(err);
        });
    });
  },
  changePassword(context, user) {
    return new Promise((resolve, reject) => {
      axios
        .post(process.env.VUE_APP_CHANGE_PASSWORD, user, {
          headers: { Authorization: 'Bearer ' + context.state.token },
        })
        .then((res) => {
          resolve(res.data.message);
        })
        .catch((err) => {
          reject(err.response.data.message);
          Bugsnag.notify(err);
        });
    });
  },
  resetPassword(context, user) {
    return new Promise((resolve, reject) => {
      axios
        .post(process.env.VUE_APP_RESET_PASSWORD, user)
        .then((res) => {
          resolve(res.data.message);
        })
        .catch((err) => {
          reject(err.response.data.message);
        });
    });
  },
  checkToken(context) {
    return new Promise((resolve, reject) => {
      axios
        .post(
          'http://localhost/api/valid-token',
          {},
          { headers: { Authorization: 'Bearer ' + context.state.token } }
        )
        .then((res) => {
          context.commit('SET_TOKEN_VALID', !res.data.token_valid);
          resolve(res);
        })
        .catch((err) => {
          context.commit('SET_TOKEN_VALID', false);
          reject(err);
        });
    });
  },
  getRoles(context) {
    return new Promise((resolve, reject) => {
      context.commit('SET_SIDEBAR_LOADING', true);
      axios
        .get(process.env.VUE_APP_GET_ROLE, {
          headers: { Authorization: 'Bearer ' + context.state.token },
        })
        .then((res) => {
          context.commit('SET_ROLES', res.data.roles);
          context.commit('SET_ACTIONS', res.data.actions);
          context.commit('SET_SIDEBAR_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          console.log(err);
          reject(err.response.data.message);
          Bugsnag.notify(err);
        });
    });
  },
};

const mutations = {
  SET_AUTH_LOADING: (state, val) => (state.auth_loading = val),
  SET_USER: (state, user) => (state.user = user),
  SET_TOKEN: (state, token) => (state.token = token),
  SET_EXPIRY_DATE: (state, date) => (state.token_expire_at = date),
  SET_ROLES: (state, roles) => (state.roles = roles),
  SET_ACTIONS: (state, actions) => (state.actions = actions),
  SET_SIDEBAR_LOADING: (state, val) => (state.sidebar_loading = val),
  SET_DRY_MODE: (state, val) => {
    state.dryMode = val;
    localStorage.setItem('dryMode', JSON.stringify(state.dryMode));
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};
