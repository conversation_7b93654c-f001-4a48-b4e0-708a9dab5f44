import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const state = {
  loading: false,
  list_newsletter: [],
  list_articles: [],
  newsletter: {},
};

const getters = {
  getNewsletterList: (state) => state.list_newsletter,
  getArticleNewsletterList: (state) => state.list_articles,
  getNewsletter: (state) => state.newsletter,
  getNewsletterLoading: (state) => state.loading,
};

const actions = {
  insertNewsletterArticle({ commit, rootState }, article) {
    commit('SET_LOADING', true);
    axios
      .post(
        process.env.VUE_APP_CREATE_NEWSLETTER_ARTICLES + article.newsletter_id,
        article,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        commit('SET_ADD_TO_LIST_ARTICLES', res.data);
        alert('Article Added successfully');
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log('---Error---');
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  updateNewsletterArticle({ commit, rootState }, article) {
    commit('SET_LOADING', true);
    axios
      .put(
        process.env.VUE_APP_EDIT_NEWSLETTER_ARTICLES + article.id,
        article,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        commit('SET_UPDATE_ARTICLE_IN_LIST', res.data);
        alert('Article has been updated successfully');
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  getNewsletters({ commit, rootState }) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    commit('SET_LOADING', true);
    axios
      .get(
        process.env.VUE_APP_GET_NEWSLETTER + '?r=' + r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        commit('SET_LIST_NEWSLETTER', res.data.newsletter);
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  getOneNewsletter({ commit, rootState }, id) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    commit('SET_LOADING', true);
    axios
      .get(
        process.env.VUE_APP_GET_NEWSLETTER + '/' + id + '?r=' + r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        commit('SET_NEWSLETTER', res.data.newsletter);
        commit('SET_LIST_ARTICLES', res.data.articles);
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  editNewsletter({ commit, rootState }, newsletter) {
    commit('SET_LOADING', true);
    axios
      .put(
        process.env.VUE_APP_EDIT_NEWSLETTER + newsletter.id,
        newsletter,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        commit('SET_NEWSLETTER', res.data.newsletter);
        commit('SET_LOADING', false);
        alert('Newsletter has been updated successfully!');
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  removeArticleFromNewsletter({ commit, rootState }, article) {
    commit('SET_LOADING', true);
    axios
      .delete(
        process.env.VUE_APP_DELETE_NEWSLETTER_ARTICLE + article.id,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then(() => {
        alert('Article has been removed!');
        commit('SET_REMOVE_ARTICLE_IN_LIST', article.id);
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  createChimpCampaign({ commit, rootState }, newsletter) {
    axios
      .get(
        process.env.VUE_APP_CREATE_MAILCHIMP_CAMPAIGN + newsletter.unique_id,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        commit('SET_NEWSLETTER', res.data);
        alert('MailChimp Campaign is created!');
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        Bugsnag.notify(err);
      });
  },
  sendChimpCampaign({ commit, rootState }, newsletter) {
    axios
      .get(
        process.env.VUE_APP_SEND_MAILCHIMP_CAMPAIGN + newsletter.unique_id,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        commit('SET_NEWSLETTER', res.data);
        alert('MailChimp Campaign is sending!');
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        Bugsnag.notify(err);
      });
  },
};

const mutations = {
  SET_LOADING: (state, val) => (state.loading = val),
  SET_LIST_NEWSLETTER: (state, list) => (state.list_newsletter = list),
  SET_LIST_ARTICLES: (state, list) => (state.list_articles = list),
  SET_NEWSLETTER: (state, news) => (state.newsletter = news),

  SET_ADD_TO_LIST_ARTICLES: (state, article) =>
    state.list_articles.push(article),
  SET_UPDATE_ARTICLE_IN_LIST: (state, article) => {
    const index = state.list_articles.findIndex(
      (list) => list.id === article.id
    );
    if (index != -1) {
      state.campaigns.splice(index, 1, article);
    }
  },
  SET_REMOVE_ARTICLE_IN_LIST: (state, id) => {
    state.list_articles = state.list_articles.filter((list) => list.id != id);
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};
