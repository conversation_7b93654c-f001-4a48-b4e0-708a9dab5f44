import axios from 'axios';
import * as headers from '@/helpers/headers';

const state = {
  rev: [],
  summary: {},
  pagination: {},
  revloading: false,
  revpubloading: false,
  revpub: [],
};

const getters = {
  getPaginatedRevenues: (state) => state.rev,
  getRevSummary: (state) => state.summary,
  getREVLoading: (state) => state.revloading,
  getRevPublishers: (state) => state.revpub,
  getRevPubLoading: (state) => state.revpubloading,
  getRevenuePagination: (state) => state.pagination,
};

const actions = {
  getRevPublishers({ commit, rootState }) {
    commit('SET_REV_PUB_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    let url = process.env.VUE_APP_PUBLISHER_LIST;
    return new Promise((resolve, reject) => {
      axios
        .get(
          url + '?r=' + r,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          let data = res.data;
          commit('SET_REV_PUB', res.data);
          commit('SET_REV_PUB_LOADING', false);
          resolve(data);
        })
        .catch((err) => {
          commit('SET_REV_PUB_LOADING', false);
          reject(err);
        });
    });
  },
  getAllRevenues({ commit, rootState }, { startDate, endDate, publisher_ids }) {
    commit('SET_REV_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    let url = process.env.VUE_APP_ALL_PUBLISHER_REVENUE;
    return new Promise((resolve, reject) => {
      axios
        .get(
          url +
            '?startDate=' +
            startDate +
            '&endDate=' +
            endDate +
            '&r=' +
            r +
            '&publisher_ids=' +
            publisher_ids,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          let response = res.data.data;
          console.log(response);
          commit('SET_REV_LOADING', false);
          resolve(response);
        })
        .catch((err) => {
          commit('SET_REV_LOADING', false);
          reject(err);
        });
    });
  },
  getPaginatedRevenues(
    { commit, rootState },
    {
      startDate,
      endDate,
      publisher_ids,
      currentPage,
      sortBy = null,
      asc = null,
    }
  ) {
    commit('SET_REV_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    let url = process.env.VUE_APP_PUBLISHER_REVENUE;
    let page = currentPage ? currentPage : 1;
    url =
      url + '?startDate=' + startDate + '&endDate=' + endDate + '&page=' + page;

    if (sortBy != null && asc != null) {
      url = url + '&sortBy=' + sortBy + '&sortDirection=' + asc;
    }

    page;
    return new Promise((resolve, reject) => {
      axios
        .get(
          url + '&r=' + r + '&publisher_ids=' + publisher_ids,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          let data = res.data;
          commit('SET_REV', res.data.data);
          commit('SET_REV_SUMMARY', res.data.summary);
          commit('SET_REVENUE_PAGINATION', res.data.pagination);
          commit('SET_REV_LOADING', false);
          resolve(data);
        })
        .catch((err) => {
          commit('SET_REV_LOADING', false);
          reject(err);
        });
    });
  },
};

const mutations = {
  SET_REV: (state, data) => (state.rev = data),
  SET_REV_SUMMARY: (state, data) => (state.summary = data),
  SET_REV_LOADING: (state, data) => (state.revloading = data),
  SET_REV_PUB_LOADING: (state, data) => (state.revpubloading = data),
  SET_REV_PUB: (state, data) => (state.revpub = data),
  SET_REVENUE_PAGINATION: (state, data) => (state.pagination = data),
};

export default {
  state,
  getters,
  actions,
  mutations,
};
