import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const state = {
  electionStates: {
    general: true,
    states: [],
  },
  electionCandidates: [],
  electionData: [],
  loading: false,
  hasElection: true,
};

const getters = {
  getElectionStates: (state) => state.electionStates,
  getElectionCandidates: (state) => state.electionCandidates,
  getElectionData: (state) => state.electionData,
  getElectionLoading: (state) => state.loading,
  getHasElection: (state) => state.hasElection,
};

const actions = {
  async getElectionStates({ commit, rootState }) {
    return new Promise((resolve, reject) => {
      commit('SET_LOADING', true);
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      axios
        .get(
          process.env.VUE_APP_ELECTION_STATES + '?r=' + r,
          headers.createHeaders(rootState.user.token, 'abdulNewswav')
        )
        .then((res) => {
          commit('SET_ELECTION_STATES', res.data);
          commit('SET_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          console.log(err.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          if (err.response.status == 404) {
            message = err.response.data.message;
            title = 'Not Found';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          console.log(err);
          commit('SET_ELECTION', false);
          commit('SET_LOADING', false);
          Bugsnag.notify(err);
          reject(err);
        });
    });
  },
  async getElectionCandidates({ commit, rootState }, { type, areaId }) {
    return new Promise((resolve, reject) => {
      commit('SET_LOADING', true);
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      let url = process.env.VUE_APP_ELECTION_CANDIDATES + '?r=' + r;
      if (type && areaId) {
        url = url + `&type=${type}&areaId=${areaId}`;
      }
      axios
        .get(url, headers.createHeaders(rootState.user.token, 'abdulNewswav'))
        .then((res) => {
          commit('SET_ELECTION_CANDIDATES', res.data);
          commit('SET_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          console.log(err.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          console.log(err);
          commit('SET_LOADING', false);
          Bugsnag.notify(err);
          reject(err);
        });
    });
  },
  async getElectionData({ commit, rootState }, { type, state }) {
    console.log(state);
    commit('SET_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_ELECTION_DATA +
          state.id +
          '?type=' +
          type +
          '&r=' +
          r,
        headers.createHeaders(rootState.user.token, 'abdulNewswav')
      )
      .then((res) => {
        console.log(res.data);
        let data = res.data.map((v) => {
          Vue.set(v, 'edit', false); // https://vuejs.org/v2/guide/reactivity.html
          Vue.set(v, 'loading', false);
          Vue.set(v, 'candidate_list', []);
          v._classes = 'row-height';
          return v;
        });
        commit('SET_ELECTION_DATA', data);
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },

  async updateElectionData({ rootState }, data) {
    return new Promise((resolve, reject) => {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      axios
        .put(
          process.env.VUE_APP_ELECTION_DATA + '?r=' + r,
          data,
          headers.createHeaders(rootState.user.token, 'abdulNewswav')
        )
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  async updateElectionImage({ commit }, lang) {
    console.log(state);
    commit('SET_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .get(`${process.env.VUE_APP_ELECTION_IMAGE}?lang=${lang}`)
        .then((res) => {
          console.log(res);
          Vue.$toast.open({
            message: `Updated Election Image for ${lang}`,
            type: 'success',
            position: 'top-right',
          });
          commit('SET_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          console.log(err);
          Vue.$toast.open({
            message: `Failed to update image for ${lang}`,
            type: 'error',
            position: 'top-right',
          });
          commit('SET_LOADING', false);
          Bugsnag.notify(err);
          reject(err);
        });
    });
  },

  async rebuildSearchCache({ commit, rootState }) {
    console.log(state);
    commit('SET_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          `${process.env.VUE_APP_ELECTION_SEARCH_CACHE}`,
          {},
          headers.createHeaders(rootState.user.token, 'abdulNewswav')
        )
        .then((res) => {
          console.log(res);
          Vue.$toast.open({
            message: 'Election Search Cache Rebuilt',
            type: 'success',
            position: 'top-right',
          });
          commit('SET_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          console.log(err);
          Vue.$toast.open({
            message: `Failed to rebuild cache, check console for more info`,
            type: 'error',
            position: 'top-right',
          });
          commit('SET_LOADING', false);
          Bugsnag.notify(err);
          reject(err);
        });
    });
  },
};

const mutations = {
  SET_LOADING: (state, val) => (state.loading = val),
  SET_ELECTION: (state, val) => (state.hasElection = val),
  SET_ELECTION_STATES: (state, data) => (state.electionStates = data),
  SET_ELECTION_CANDIDATES: (state, data) => (state.electionCandidates = data),
  SET_ELECTION_DATA: (state, data) => (state.electionData = data),
};

export default {
  state,
  getters,
  actions,
  mutations,
};
