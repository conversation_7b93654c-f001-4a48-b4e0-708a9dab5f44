import axios from 'axios';
import * as headers from '@/helpers/headers';
import Bugsnag from '@bugsnag/js';
import slugify from 'slugify';

const state = {
  videos: [],
  loading: false,
  update_loading: false,
  pagination: {},
  publisherIds: [],
};

const getters = {
  getVideos: (state) => state.videos,
  getVideoLoading: (state) => state.loading,
  getUpdateLoading: (state) => state.update_loading,
  getPagination: (state) => state.pagination,
  getPublisherIds: (state) => state.publisherIds,
  getVideoTopics: (state) => state.topics,
};

const actions = {
  listVideos({ rootState, commit }, currentPage) {
    commit('SET_LOADING', true);
    return new Promise((resolve, reject) => {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      let url = process.env.VUE_APP_GET_VIDEOS;
      let page = currentPage ? currentPage : 1;
      axios
        .get(
          url + '?page=' + page + '&r=' + r,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_TOKEN_VALUE
          )
        )
        .then((res) => {
          commit('SET_VIDEO_LIST', res.data.videos);
          commit('SET_PAGINATION', res.data.pagination);
          commit('SET_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_LOADING', false);
          reject(err);
        });
    });
  },
  listVideosById({ rootState, commit }, id) {
    commit('SET_LOADING', true);
    return new Promise((resolve, reject) => {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      let url =
        process.env.VUE_APP_GET_ONE_VIDEO.replace('{id}', id) + `&r=${r}`;
      axios
        .get(url, headers.createHeaders(rootState.user.token))
        .then((res) => {
          commit('SET_LOADING', false);
          resolve(res.data.videos);
        })
        .catch((err) => {
          commit('SET_LOADING', false);
          reject(err);
        });
    });
  },
  listPublisherIds({ rootState, commit }) {
    commit('SET_LOADING', true);
    return new Promise((resolve, reject) => {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      let url = process.env.VUE_APP_GET_VIDEO_PUBLISHERS;
      axios
        .get(
          url + '?r=' + r,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_TOKEN_VALUE
          )
        )
        .then((res) => {
          commit('SET_PUBLISHER_IDS', res.data);
          commit('SET_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_LOADING', false);
          reject(err);
        });
    });
  },
  listVideoTopics({ rootState, commit }) {
    commit('SET_LOADING', true);
    return new Promise((resolve, reject) => {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      let url = process.env.VUE_APP_GET_VIDEO_TOPICS;
      axios
        .get(
          url + '?r=' + r,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_TOKEN_VALUE
          )
        )
        .then((res) => {
          commit('SET_TOPICS', res.data);
          commit('SET_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_LOADING', false);
          reject(err);
        });
    });
  },

  createVideo({ rootState, commit }, video) {
    commit('SET_LOADING', true);
    const videoPayload = {
      title: video.title,
      description: video.description,
      youtubeUrl: video.youtube_link,
      thumbnailUrl: video.source_thumb_url,
      publisherId: video.publisher_id,
      isLive: video.is_live,
      topicId: video.topic.value,
      language: video.language,
    };

    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_CREATE_LIVE_VIDEO,
          videoPayload,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          commit('SET_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_LOADING', false);
          reject(err);
        });
    });
  },
  updateVideo({ rootState, commit }, video) {
    commit('SET_LOADING', true);
    // const encodedTitle = encodeURIComponent(video.title);
    const videoPayload = {
      title: video.title,
      description: video.description,
      youtubeUrl: video.youtube_link,
      thumbnailUrl: video.source_thumb_url,
      publisherId: video.publisher_id,
      isLive: video.is_live,
      topicId: video.topic.value,
      language: video.language,
    };
    return new Promise((resolve, reject) => {
      let url = process.env.VUE_APP_UPDATE_LIVE_VIDEO.replace('{id}', video.id);
      axios
        .put(url, videoPayload, headers.createHeaders(rootState.user.token))
        .then((res) => {
          commit('SET_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_LOADING', false);
          console.log(err);
          reject(err);
        });
    });
  },

  getVideoThumbnailLink({ commit }, input) {
    const name = input.files[0].name;
    const lastDot = name.lastIndexOf('.');
    const nameOnly = name.substring(0, lastDot);
    const ext = name.substring(lastDot + 1);
    let randomChars =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    while (!/^(?=.*[A-Z])(?=.*\d).{6,}$/.test(result)) {
      result = '';
      for (let i = 0; i < 6; i++) {
        result += randomChars.charAt(
          Math.floor(Math.random() * randomChars.length)
        );
      }
    }
    let fileName = slugify(`${nameOnly} ${result}`) + `.${ext}`;
    let fileData = new FormData();
    fileData.append('path', 'adwav/temp');
    fileData.append('file', input.files[0]);
    fileData.append('name', fileName);
    commit('SET_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(process.env.VUE_APP_UPLOAD_FILE_TO_GCS, fileData)
        .then((res) => {
          commit('SET_LOADING', false);
          resolve(res.data.url);
        })
        .catch((error) => {
          commit('SET_LOADING', false);
          alert('Something went wrong with uploading media');
          console.log('----Error----');
          console.log(error);
          Bugsnag.notify(error);
          reject(error);
        });
    });
  },

  generatePostfix() {
    let randomChars =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    while (!/^(?=.*[A-Z])(?=.*\d).{6,}$/.test(result)) {
      result = '';
      for (let i = 0; i < 6; i++) {
        result += randomChars.charAt(
          Math.floor(Math.random() * randomChars.length)
        );
      }
    }
    return `${result} ${Date.now()}`;
  },
};

const mutations = {
  SET_LOADING: (state, val) => (state.loading = val),
  SET_UPDATE_VIDEO: (state, newUpd) => {
    const idx = state.videos.findIndex((a) => a.id === newUpd.id);
    if (idx !== -1) {
      state.videos.splice(idx, 1, newUpd);
    }
  },
  SET_NEW_VIDEO: (state, newVid) => state.videos.unshift(newVid),
  SET_VIDEO_LIST: (state, list) => (state.videos = list),
  SET_PAGINATION: (state, pagination) => (state.pagination = pagination),
  SET_PUBLISHER_IDS: (state, publisherIds) =>
    (state.publisherIds = publisherIds),
  SET_TOPICS: (state, topics) => (state.topics = topics),
};

export default {
  state,
  getters,
  actions,
  mutations,
};
