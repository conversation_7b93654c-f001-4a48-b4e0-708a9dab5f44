import axios from 'axios';

const config = {
  headers: {
    hash: process.env.VUE_APP_ADMIN_TOKEN,
  },
};

const state = {
  loading: false,
  articles: [],
  news_feed: [],
};

const getters = {
  getSabahLoading: (state) => state.loading,
  getSabahArticles: (state) => state.articles,
  getNewsFeed: (state) => state.news_feed,
};

const actions = {
  listNewsFeed({ commit }) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    commit('SET_LOADING', true);
    axios
      .get(process.env.VUE_APP_LIST_FEED + '?r=' + r, config)
      .then((res) => {
        commit('SET_NEWS_FEED', res.data);
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        commit('SET_LOADING', false);
        alert('Something went wrong, check logs');
        console.log(err);
      });
  },
  listArticles({ commit }) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    commit('SET_LOADING', true);
    axios
      .get(process.env.VUE_APP_LIST_ARTICLE + '?r=' + r, config)
      .then((res) => {
        commit('SET_LOADING', false);
        commit('SET_ARTICLES', res.data);
      })
      .catch((err) => {
        commit('SET_LOADING', false);
        alert('Something went wrong, check logs');
        console.log(err);
      });
  },
  hideArticle({ commit }, data) {
    commit('SET_LOADING', true);
    axios
      .post(process.env.VUE_APP_HIDE_ARTICLE_IN_SABAH, data, config)
      .then((res) => {
        commit('SET_UPDATE_ARTICLES', res.data);
        commit('SET_LOADING', false);
        alert('Data sent is successful');
      })
      .catch((err) => {
        commit('SET_LOADING', false);
        alert('Something went wrong, check logs');
        console.log(err);
      });
  },
};

const mutations = {
  SET_NEWS_FEED: (state, list) => (state.news_feed = list),
  SET_LOADING: (state, val) => (state.loading = val),
  SET_ARTICLES: (state, articles) => (state.articles = articles),
  SET_UPDATE_ARTICLES: (state, newArt) => {
    const index = state.articles.findIndex(
      (articles) => articles.article_unique_id === newArt.article_unique_id
    );
    if (index !== -1) {
      state.articles.splice(index, 1, newArt);
    }
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};
