import axios from 'axios';
import * as headers from '@/helpers/headers';

const state = {
  lead_forms: [],
  pagination: {},
  leadLoading: false,
};

const getters = {
  getLeadForms: (state) => state.lead_forms,
  getLeadLoading: (state) => state.leadLoading,
  getLeadPagination: (state) => state.pagination,
};

const actions = {
  listLeadForm(
    { commit, rootState },
    {
      id = '',
      name = null,
      title = null,
      sortBy = null,
      asc = null,
      currentPage = 1,
    } = {}
  ) {
    commit('SET_LEAD_LOADING', true);
    let url = process.env.VUE_APP_LIST_LEAD_FORMS;
    let page = currentPage ? currentPage : 1;
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    if (id) {
      url = process.env.VUE_APP_LIST_ONE_LEAD_FORM + id + '?r=' + r;
    } else {
      url = url + '?page=' + page + '&r=' + r;
      if (name) {
        url = url + '&name=' + name;
      }

      if (title) {
        url = url + '&title=' + title;
      }

      if (sortBy != null && asc != null) {
        url = url + '&sortBy=' + sortBy + '&sortDirection=' + asc;
      }
    }

    return new Promise((resolve, reject) => {
      axios
        .get(url, headers.createHeaders(rootState.user.token))
        .then((res) => {
          if (id === '') {
            commit('SET_LEAD_LIST', res.data.data);
            commit('SET_LEAD_PAGINATION', res.data.pagination);
          } else {
            resolve(res);
          }
          commit('SET_LEAD_LOADING', false);
        })
        .catch((err) => {
          commit('SET_LEAD_LOADING', false);
          reject(err);
          console.log(err);
        });
    });
  },
  createForm({ commit, rootState }, lead) {
    commit('SET_GENERAL_LOADING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_CREATE_LEAD_FORM,
          lead,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          commit('SET_NEW_LEAD', res.data);
          commit('SET_GENERAL_LOADING', false, { root: true });
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  editForm({ commit, rootState }, lead) {
    commit('SET_GENERAL_LOADING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .put(
          process.env.VUE_APP_UPDATE_LEAD_FORM + lead.id,
          lead,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          commit('SET_UPDATE_LEAD', res.data[0]);
          commit('SET_GENERAL_LOADING', false, { root: true });
          resolve(res);
        })
        .catch((err) => {
          commit('SET_GENERAL_LOADING', false, { root: true });
          reject(err);
        });
    });
  },
  downloadLeadCSV({ rootState }, lead) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    return new Promise((resolve, reject) => {
      axios
        .get(
          process.env.VUE_APP_DOWNLOAD_LEAD_CSV + lead.id + '?r=' + r,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          resolve(res.data);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
};

const mutations = {
  SET_LEAD_LOADING: (state, val) => (state.leadLoading = val),
  SET_LEAD_LIST: (state, list) => (state.lead_forms = list),
  SET_UPDATE_LEAD: (state, updLead) => {
    const idx = state.lead_forms.findIndex((ld) => ld.id === updLead.id);
    if (idx !== -1) {
      state.lead_forms.splice(idx, 1, updLead);
    }
  },
  SET_NEW_LEAD: (state, lead) => state.lead_forms.unshift(lead),
  SET_LEAD_PAGINATION: (state, value) => (state.pagination = value),
};

export default {
  state,
  getters,
  actions,
  mutations,
};
