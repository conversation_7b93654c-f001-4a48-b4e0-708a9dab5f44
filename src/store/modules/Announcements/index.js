import * as headers from '@/helpers/headers';
import Vue from 'vue';
import axios from 'axios';

const state = {
  ann_list: [],
  pagination: {},
  ann_loading: false,
  priority: [
    { value: '3', label: 'Low' },
    { value: '2', label: 'Medium' },
    { value: '1', label: 'High' },
  ],
};

const getters = {
  getAnnList: (state) => state.ann_list,
  getAnnLoading: (state) => state.ann_loading,
  getPriority: (state) => state.priority,
  getAnnouncementPagination: (state) => state.pagination,
};

const actions = {
  listAnnouncements(
    { rootState, commit, rootGetters },
    { sortBy = null, asc = null, currentPage } = {}
  ) {
    commit('SET_ANN_LOADING', true);
    return new Promise((resolve, reject) => {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      let url = process.env.VUE_APP_LIST_ANNOUNCEMENTS;
      let page = currentPage ? currentPage : 1;
      url = url + '?page=' + page;

      if (sortBy != null && asc != null) {
        url = url + '&sortBy=' + sortBy + '&sortDirection=' + asc;
      }

      axios
        .get(url + '&r=' + r, headers.createHeaders(rootState.user.token))
        .then((res) => {
          res.data.data.forEach((item) => {
            let newFeed = [];
            let feedShow = [];
            item.feed_id.forEach((i) => {
              let found = rootGetters.getFeeds.find((item) => item.id === i);
              let data = { value: found.id, label: found.name };
              newFeed.unshift(data);
            });
            item.feed_id = newFeed;
            newFeed.forEach((i) => {
              feedShow.unshift(i.label);
            });
            item.feedShow = feedShow.join(', ');
            let prioShow = state.priority.find(
              (data) => data.value === item.priority.toString()
            );
            item.prioShow = prioShow.label;
            item.titleShow = item.languages.en.title;
            item.descShow = item.languages.en.body_text;
            item.linkShow =
              item.languages.en.link !== '' ? item.languages.en.link : '-';
            return item;
          });
          commit('SET_ANN_LIST', res.data.data);
          commit('SET_PAGINATION', res.data.pagination);
          commit('SET_ANN_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_ANN_LOADING', false);
          reject(err);
        });
    });
  },
  getAnnouncementById({ rootState, commit, rootGetters }, id) {
    commit('SET_ANN_LOADING', true);
    return new Promise((resolve, reject) => {
      let url = process.env.VUE_APP_GET_ONE_ANNOUNCEMENTS;
      if (id) {
        axios
          .get(url + id, headers.createHeaders(rootState.user.token))
          .then((res) => {
            res.data.forEach((item) => {
              let newFeed = [];
              let feedShow = [];
              item.feed_id.forEach((i) => {
                let found = rootGetters.getFeeds.find((item) => item.id === i);
                let data = { value: found.id, label: found.name };
                newFeed.unshift(data);
              });
              item.feed_id = newFeed;
              newFeed.forEach((i) => {
                feedShow.unshift(i.label);
              });
              item.feedShow = feedShow.join(', ');
              let prioShow = state.priority.find(
                (data) => data.value === item.priority.toString()
              );
              item.prioShow = prioShow.label;
              item.titleShow = item.languages.en.title;
              item.descShow = item.languages.en.body_text;
              item.linkShow =
                item.languages.en.link !== '' ? item.languages.en.link : '-';
              return item;
            });
            commit('SET_ANN_LOADING', false);
            resolve(res);
          })
          .catch((err) => {
            commit('SET_ANN_LOADING', false);
            reject(err);
          });
      } else {
        commit('SET_ANN_LOADING', false);
        this.$swal.fire({
          icon: 'error',
          text: 'Something went wrong in getting advertiser!',
        });
      }
    });
  },
  createAnnouncement({ rootState, commit }, ann) {
    commit('SET_ANN_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_UPSERT_ANNOUNCEMENT,
          ann,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          commit('SET_ANN_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_ANN_LOADING', false);
          reject(err);
        });
    });
  },
  updateAnnouncement({ rootState, commit }, ann) {
    commit('SET_ANN_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .put(
          process.env.VUE_APP_UPSERT_ANNOUNCEMENT + ann.id,
          ann,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          commit('SET_ANN_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_ANN_LOADING', false);
          console.log(err);
          reject(err);
        });
    });
  },
  removeAnnouncement({ rootState, commit }, ann) {
    commit('SET_ANN_LOADING', true);
    axios
      .delete(
        process.env.VUE_APP_UPSERT_ANNOUNCEMENT + ann.id,
        headers.createHeaders(rootState.user.token)
      )
      .then(() => {
        Vue.swal.fire({
          icon: 'success',
          text: 'Announcement is removed successfully',
          showCancelButton: false,
        });
        commit('SET_REMOVE_ANN', ann.id);
        commit('SET_ANN_LOADING', false);
      })
      .catch((err) => {
        Vue.swal.fire({
          icon: 'error',
          text: 'Something went wrong in removing announcement',
          showCancelButton: false,
        });
        console.log(err);
        commit('SET_REMOVE_ANN', ann.id);
        commit('SET_ANN_LOADING', false);
      });
  },
};
const mutations = {
  SET_UPDATE_ANN: (state, newUpd) => {
    const idx = state.ann_list.findIndex((a) => a.id === newUpd.id);
    if (idx !== -1) {
      state.ann_list.splice(idx, 1, newUpd);
    }
  },
  SET_NEW_ANN: (state, newAnn) => state.ann_list.unshift(newAnn),
  SET_ANN_LIST: (state, list) => (state.ann_list = list),
  SET_ANN_LOADING: (state, val) => (state.ann_loading = val),
  SET_PAGINATION: (state, pagination) => (state.pagination = pagination),
  SET_REMOVE_ANN: (state, id) =>
    (state.ann_list = state.ann_list.filter((a) => a.id !== id)),
};

export default {
  state,
  getters,
  actions,
  mutations,
};
