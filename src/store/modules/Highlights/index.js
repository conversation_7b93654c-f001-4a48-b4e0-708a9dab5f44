import * as headers from '@/helpers/headers';
import Vue from 'vue';
import axios from 'axios';

const state = {
  highlights: [],
  highlights_loading: false,
  pagination: {},
};

const getters = {
  getHighlights: (state) => state.highlights,
  getHighlightsLoading: (state) => state.highlights_loading,
  getPagination: (state) => state.pagination,
};

const actions = {
  listHighlights({ rootState, commit, rootGetters }, currentPage) {
    commit('SET_HIGHLIGHT_LOADING', true);
    return new Promise((resolve, reject) => {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      let url = process.env.VUE_APP_LIST_HIGHLIGHTS;
      let page = currentPage ? currentPage : 1;
      axios
        .get(
          url + '?page=' + page + '&r=' + r,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          const highlightTypes = [
            { value: 'keywords', label: 'Keywords' },
            { value: 'publishers', label: 'Publishers' },
            { value: 'contents', label: 'Content IDs' },
          ];
          res.data.highlights.forEach((item) => {
            let newFeed = [];
            let feedShow = [];
            item.feedId.forEach((i) => {
              let found = rootGetters.getFeeds.find((item) => item.id === i);
              let data = { value: found.id, label: found.name };
              newFeed.unshift(data);
            });
            item.feedId = newFeed;
            newFeed.forEach((i) => {
              feedShow.unshift(i.label);
            });
            item.isPinnedShow = item.isPinned ? 'Yes' : 'No';
            item.statusShow =
              new Date(item.endDate) >= new Date() ? 'Active' : 'Idle';
            item.feedShow = feedShow.join(', ');
            item.titleShow = item.translations[item.languages[0]].title;
            item.descShow = item.translations[item.languages[0]].description;
            item.typeShow = highlightTypes.find(
              (i) => i.value === item.type
            ).label;
            if (item?.contentIds !== undefined) {
              item.contentIds = item.contentIds.map((j) => {
                return { value: j, label: j };
              });
            }
            return item;
          });
          commit('SET_HIGHLIGHT_LIST', res.data.highlights);
          commit('SET_HIGHLIGHT_LOADING', false);
          commit('SET_PAGINATION', res.data.pagination);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_HIGHLIGHT_LOADING', false);
          reject(err);
        });
    });
  },
  listHighlightsById({ rootState, commit, rootGetters }, id) {
    commit('SET_HIGHLIGHT_LOADING', true);
    return new Promise((resolve, reject) => {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      let url = process.env.VUE_APP_LIST_HIGHLIGHTS + id;
      axios
        .get(url + '&r=' + r, headers.createHeaders(rootState.user.token))
        .then((res) => {
          const highlightTypes = [
            { value: 'keywords', label: 'Keywords' },
            { value: 'publishers', label: 'Publishers' },
            { value: 'contents', label: 'Content IDs' },
          ];
          res.data.highlights.forEach((item) => {
            let newFeed = [];
            let feedShow = [];
            item.feedId.forEach((i) => {
              let found = rootGetters.getFeeds.find((item) => item.id === i);
              let data = { value: found.id, label: found.name };
              newFeed.unshift(data);
            });
            item.feedId = newFeed;
            newFeed.forEach((i) => {
              feedShow.unshift(i.label);
            });
            item.isPinnedShow = item.isPinned ? 'Yes' : 'No';
            item.statusShow =
              new Date(item.endDate) >= new Date() ? 'Active' : 'Idle';
            item.feedShow = feedShow.join(', ');
            item.titleShow = item.translations[item.languages[0]].title;
            item.descShow = item.translations[item.languages[0]].description;
            item.typeShow = highlightTypes.find(
              (i) => i.value === item.type
            ).label;
            if (item?.contentIds !== undefined) {
              item.contentIds = item.contentIds.map((j) => {
                return { value: j, label: j };
              });
            }
            return item;
          });
          commit('SET_HIGHLIGHT_LOADING', false);
          resolve(res.data.highlights);
        })
        .catch((err) => {
          commit('SET_HIGHLIGHT_LOADING', false);
          reject(err);
        });
    });
  },
  createHighlight({ rootState, commit }, high) {
    commit('SET_HIGHLIGHT_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_UPSERT_HIGHLIGHTS,
          high,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          commit('SET_HIGHLIGHT_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_HIGHLIGHT_LOADING', false);
          reject(err);
        });
    });
  },
  updateHighlight({ rootState, commit }, high) {
    commit('SET_HIGHLIGHT_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .put(
          process.env.VUE_APP_UPSERT_HIGHLIGHTS + high.id,
          high,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          commit('SET_HIGHLIGHT_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_HIGHLIGHT_LOADING', false);
          console.log(err);
          reject(err);
        });
    });
  },
  removeHighlight({ rootState, commit }, high) {
    commit('SET_HIGHLIGHT_LOADING', true);
    axios
      .delete(
        process.env.VUE_APP_UPSERT_HIGHLIGHTS + high.id,
        headers.createHeaders(rootState.user.token)
      )
      .then(() => {
        Vue.swal.fire({
          icon: 'success',
          text: 'Highlight is removed successfully',
          showCancelButton: false,
        });
        commit('SET_REMOVE_HIGHLIGHT', high.id);
        commit('SET_HIGHLIGHT_LOADING', false);
      })
      .catch((err) => {
        Vue.swal.fire({
          icon: 'error',
          text: 'Something went wrong in removing highlight',
          showCancelButton: false,
        });
        console.log(err);
        commit('SET_REMOVE_HIGHLIGHT', high.id);
        commit('SET_HIGHLIGHT_LOADING', false);
      });
  },
  highlightsContent({ rootState, commit }, objecto) {
    commit('SET_HIGHLIGHT_LOADING', true);
    let highlight = objecto.highlight;
    let page = objecto.page ? objecto.page : 1;
    let url =
      process.env.VUE_APP_HIGHLIGHT_CONTENTS.replace('{id}', highlight) +
      '?page=' +
      page;

    if (objecto.type) {
      url = url + '&type=' + objecto.type + '&search=' + objecto.search;
    }

    return new Promise((resolve, reject) => {
      axios
        .get(url, headers.createHeaders(rootState.user.token))
        .then((res) => {
          resolve(res);
          commit('SET_HIGHLIGHT_LOADING', false);
        })
        .catch((err) => {
          reject(err);
          commit('SET_HIGHLIGHT_LOADING', false);
        });
    });
  },
  hideHighlightContent({ rootState, commit }, highlight) {
    commit('SET_HIGHLIGHT_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_HIDE_HIGHLIGHT_CONTENT +
            highlight.id +
            '/' +
            highlight.unique_id,
          highlight,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          resolve(res);
          commit('SET_HIGHLIGHT_LOADING', false);
        })
        .catch((err) => {
          reject(err);
          commit('SET_HIGHLIGHT_LOADING', false);
        });
    });
  },
  unhideHighlightContent({ rootState, commit }, highlight) {
    commit('SET_HIGHLIGHT_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_UNHIDE_HIGHLIGHT_CONTENT +
            highlight.id +
            '/' +
            highlight.unique_id,
          highlight,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          resolve(res);
          commit('SET_HIGHLIGHT_LOADING', false);
        })
        .catch((err) => {
          reject(err);
          commit('SET_HIGHLIGHT_LOADING', false);
        });
    });
  },
  listHighlightPublishers({ rootState }, query = null) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    let url = process.env.VUE_APP_HIGHLIGHT_PUBLISHERS;
    if (query !== null) {
      url += '?' + new URLSearchParams(query) + '&r=' + r;
    }
    return axios.get(
      url,
      headers.createHeaders(
        rootState.user.token,
        process.env.VUE_APP_ADMIN_TOKEN
      )
    );
  },
};
const mutations = {
  SET_UPDATE_HIGHLIGHT: (state, newUpd) => {
    const idx = state.highlights.findIndex((a) => a.id === newUpd.id);
    if (idx !== -1) {
      state.highlights.splice(idx, 1, newUpd);
    }
  },
  SET_NEW_HIGHLIGHT: (state, newHigh) => state.highlights.unshift(newHigh),
  SET_HIGHLIGHT_LIST: (state, list) => (state.highlights = list),
  SET_HIGHLIGHT_LOADING: (state, val) => (state.highlights_loading = val),
  SET_REMOVE_HIGHLIGHT: (state, id) =>
    (state.highlights = state.highlights.filter((a) => a.id !== id)),
  SET_PAGINATION: (state, page) => (state.pagination = page),
};

export default {
  state,
  getters,
  actions,
  mutations,
};
