import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import * as headers from '@/helpers/headers';
import Vue from 'vue';
import { format, parseISO } from 'date-fns';

const contentMap = {
  article: 'Article',
  video: 'Video',
  podcast: 'Podcast',
  feed: 'Feed',
};
const reviewMap = {
  hide: 'Hidden',
  unhide: 'Unhidden',
  ignore: 'Ignored',
};

const state = {
  reports: [],
  pagination: {},
  aiPagination: {},
  aiReports: [],
  loading: false,
  aiLoading: false,
  comments: [],
  report_keywords: '',
  hide_keywords: '',
  random: 0,
  filters: {},
  columnFilters: {},
  pageSettings: {},
  sort: [],
  nextPageUrl: null,
  prevPageUrl: null,
};

const getters = {
  getReports: (state) => state.reports,
  getAIReports: (state) => state.aiReports,
  getReportsLoading: (state) => state.loading,
  getAIReportsLoading: (state) => state.aiLoading,
  getComments: (state) => state.comments,
  getRandom: (state) => state.random,
  getReportKeywords: (state) => state.report_keywords,
  getHideKeywords: (state) => state.hide_keywords,
  getFilters: (state) => state.filters,
  getColumnFilters: (state) => state.columnFilters,
  getPageSettings: (state) => state.pageSettings,
  getSort: (state) => state.sort,
  getNextPageUrl: (state) => state.nextPageUrl,
  getPrevPageUrl: (state) => state.prevPageUrl,
  getCommentsPagination: (state) => state.pagination,
  getAiCommentsPagination: (state) => state.aiPagination,
};

const actions = {
  getReports({ commit, rootState }, { startDate, endDate }) {
    commit('SET_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_GET_REPORT_COMMENTS +
          '?startDate=' +
          startDate +
          '&endDate=' +
          endDate +
          '&r=' +
          r,
        headers.createHeaders(rootState.user.token, 'abdulNewswav')
      )
      .then((res) => {
        res.data.forEach((a) => {
          a.user_info = `${a.username} (${a.comment.userID})`;
          let r = '';
          let item;
          for (item of a.reason_id) {
            r = r + item.reason_en + ',';
          }
          a.report_reason = r.slice(0, -1);
        });
        commit('SET_REPORTS', res.data);
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err);
        //console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  getServiceComments({ commit, rootState }, { sort, filters }) {
    commit('SET_LOADING', true);
    const query = new URLSearchParams();
    if (Object.keys(sort).length > 0)
      query.append('sorting', JSON.stringify(sort));
    if (Object.keys(filters).length > 0)
      query.append('filters', JSON.stringify(filters));

    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    query.append('r', r);
    const url = new URL(process.env.VUE_APP_COMMENTSERVICE_GET_COMMENTS);
    url.search = query.toString();

    axios
      .get(
        url.toString(),
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then(({ data }) => {
        data.data.items.forEach((i) => {
          let status = '-';
          if (i.statusField.hidden === true) {
            status = 'Hidden';
          }
          if (i.statusField.pinned === true) {
            status = 'Pinned';
          }
          i.reportStatus = status;
          return i;
        });
        commit('SET_COMMENTS', data.data.items);
        commit('SET_NEXT_PAGE_URL', data.data.nextPageUrl);
        commit('SET_PREV_PAGE_URL', data.data.previousPageUrl);
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  getServiceCommentsByPage(context, url) {
    context.commit('SET_LOADING', true);
    axios
      .get(
        url,
        headers.createHeaders(
          context.rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then(({ data }) => {
        data.data.items.forEach((i) => {
          let status = '-';
          if (i.statusField.hidden === true) {
            status = 'Hidden';
          }
          if (i.statusField.pinned === true) {
            status = 'Pinned';
          }
          i.reportStatus = status;
          return i;
        });
        context.commit('SET_COMMENTS', data.data.items);
        context.commit('SET_NEXT_PAGE_URL', data.data.nextPageUrl);
        context.commit('SET_PREV_PAGE_URL', data.data.previousPageUrl);
        context.commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err);
        context.commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  updateServiceComment(context, { id, payload, status }) {
    context.commit('SET_LOADING', true);
    axios
      .post(
        process.env.VUE_APP_COMMENTSERVICE_UPDATE_COMMENT + id,
        payload,
        headers.createHeaders(
          context.rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then(({ data }) => {
        if (data.success) {
          let showStatus = '-';
          if (data.data.statusField.hidden === true) {
            showStatus = 'Hidden';
          }
          if (data.data.statusField.pinned === true) {
            showStatus = 'Pinned';
          }
          data.data.reportStatus = showStatus;
          context.commit('SET_UPDATE_COMMENT', data.data);
          Vue.swal({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            showCancelButton: false,
            timer: 5000,
            icon: 'success',
            title: 'Success!',
            text: `Comment is ${status}`,
            allowOutsideClick: false,
          });
        }
        context.commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err);
        context.commit('SET_LOADING', false);
      });
  },
  getServiceKeywords(context) {
    context.commit('SET_LOADING', true);
    const r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    return axios
      .get(
        process.env.VUE_APP_COMMENTSERVICE_KEYWORDS + '?r=' + r,
        headers.createHeaders(
          context.rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then(({ data }) => {
        context.commit('SET_REPORT_KEYWORDS', data.data.report);
        context.commit('SET_HIDE_KEYWORDS', data.data.hide);
        context.commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.error(err);
        context.commit('SET_LOADING', false);
      });
  },
  updateServiceKeywords(context, { report, hide }) {
    const keywords = { report, hide };
    context.commit('SET_LOADING', true);
    axios
      .post(
        process.env.VUE_APP_LIVE_COMMENTS_UPDATE_KEYWORDS,
        keywords,
        headers.createHeaders(
          context.rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then(() => {
        console.log('--- keywords updated to live comment socket ---');
      });
    const v3keywords = {
      report: report.join(','),
      hide: hide.join(','),
    };
    axios
      .post(
        process.env.VUE_APP_COMMENT_KEYWORD,
        v3keywords,
        headers.createHeaders(
          context.rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then(() => {
        console.log('--- keywords updated to v3 api ---');
      });
    return axios
      .post(
        process.env.VUE_APP_COMMENTSERVICE_KEYWORDS,
        keywords,
        headers.createHeaders(
          context.rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then(({ data }) => {
        if (data.success === true) {
          context.commit('SET_REPORT_KEYWORDS', data.data.report);
          context.commit('SET_HIDE_KEYWORDS', data.data.hide);
          context.commit('SET_LOADING', false);
          Vue.swal({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            showCancelButton: false,
            timer: 5000,
            icon: 'success',
            title: 'Success',
            text: `Update keywords successful`,
            allowOutsideClick: false,
          });
        } else {
          const title = 'Error';
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `Action Failed : update keywords`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
        }
      })
      .catch((err) => {
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        context.commit('SET_LOADING', false);
        context.dispatch('getKeywords');
      });
  },
  getServiceReports(
    context,
    {
      startDate,
      endDate,
      comment = null,
      user = null,
      source = null,
      reviewer = null,
      sortBy = null,
      asc = null,
      currentPage,
    }
  ) {
    const r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    const page = currentPage ? currentPage : 1;
    const query = new URLSearchParams();
    query.append('from', startDate);
    query.append('to', endDate);
    query.append('page', page);
    query.append('r', r);

    if (comment) {
      query.append('comment', comment);
    }

    if (user) {
      query.append('user', user);
    }

    if (source) {
      query.append('source', source);
    }

    if (reviewer) {
      query.append('reviewer', reviewer);
    }

    if (sortBy != null && asc != null) {
      query.append('sortBy', sortBy);
      query.append('sortDirection', asc);
    }

    const url = new URL(
      process.env.VUE_APP_COMMENTSERVICE_GET_REPORTED_COMMENTS
    );
    url.search = query.toString();

    context.commit('SET_LOADING', true);
    axios
      .get(
        url.toString(),
        headers.createHeaders(
          context.rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then((res) => {
        res.data.data.data.forEach(async (i) => {
          await mapCommentObject(i);
          return i;
        });
        context.commit('SET_REPORTS', res.data.data.data);
        context.commit('SET_COMMENTS_PAGINATION', res.data.data.pagination);
        context.commit('SET_LOADING', false);
      })
      .catch((err) => {
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        context.commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  getServiceAIReports(
    context,
    {
      startDate,
      endDate,
      comment = null,
      user = null,
      source = null,
      reviewer = null,
      sortBy = null,
      asc = null,
      currentPage,
    }
  ) {
    const r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    const page = currentPage ? currentPage : 1;
    const query = new URLSearchParams();
    query.append('from', startDate);
    query.append('to', endDate);
    query.append('page', page);
    query.append('r', r);

    if (comment) {
      query.append('comment', comment);
    }

    if (user) {
      query.append('user', user);
    }

    if (source) {
      query.append('source', source);
    }

    if (reviewer) {
      query.append('reviewer', reviewer);
    }

    if (sortBy != null && asc != null) {
      query.append('sortBy', sortBy);
      query.append('sortDirection', asc);
    }

    const url = new URL(
      process.env.VUE_APP_COMMENTSERVICE_GET_AI_REPORTED_COMMENTS
    );
    url.search = query.toString();

    context.commit('SET_AI_LOADING', true);
    axios
      .get(
        url.toString(),
        headers.createHeaders(
          context.rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then((res) => {
        res.data.data.data.forEach(async (i) => {
          await mapCommentObject(i);
          return i;
        });
        context.commit('SET_AI_REPORTS', res.data.data.data);
        context.commit('SET_AI_COMMENTS_PAGINATION', res.data.data.pagination);
        context.commit('SET_AI_LOADING', false);
      })
      .catch((err) => {
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        context.commit('SET_AI_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  ignoreReported(context, reviewId) {
    context.commit('SET_LOADING', true);
    axios
      .post(
        process.env.VUE_APP_COMMENTSERVICE_IGNORE_COMMENT + reviewId,
        {},
        headers.createHeaders(
          context.rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then(async ({ data }) => {
        if (data.success) {
          await mapCommentObject(data.data);
          context.commit('SET_UPDATED_REPORTED_COMMENT', data.data);
          Vue.swal({
            toast: true,
            icon: 'success',
            title: 'Success!',
            text: 'Comment is ignored',
            position: 'top-end',
            timer: 3000,
            showConfirmButton: false,
            showCancelButton: false,
            allowOutsideClick: false,
          });
        }
        context.commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err);
        context.commit('SET_LOADING', false);
      });
  },
  unhideReported(context, reviewId) {
    context.commit('SET_LOADING', true);
    axios
      .post(
        process.env.VUE_APP_COMMENTSERVICE_UNHIDE_COMMENT + reviewId,
        {},
        headers.createHeaders(
          context.rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then(async ({ data }) => {
        if (data.success) {
          await mapCommentObject(data.data);
          context.commit('SET_UPDATED_REPORTED_COMMENT', data.data);
          Vue.swal({
            toast: true,
            icon: 'success',
            title: 'Success!',
            text: 'Comment is unhidden',
            position: 'top-end',
            timer: 3000,
            showConfirmButton: false,
            showCancelButton: false,
            allowOutsideClick: false,
          });
        }
        context.commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err);
        context.commit('SET_LOADING', false);
      });
  },
  hideReported(context, reviewId) {
    context.commit('SET_LOADING', true);
    axios
      .post(
        process.env.VUE_APP_COMMENTSERVICE_HIDE_COMMENT + reviewId,
        {},
        headers.createHeaders(
          context.rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then(async ({ data }) => {
        if (data.success) {
          await mapCommentObject(data.data);
          context.commit('SET_UPDATED_REPORTED_COMMENT', data.data);
          Vue.swal({
            toast: true,
            icon: 'success',
            title: 'Success!',
            text: 'Comment is hidden',
            position: 'top-end',
            timer: 3000,
            showConfirmButton: false,
            showCancelButton: false,
            allowOutsideClick: false,
          });
        }
        context.commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err);
        context.commit('SET_LOADING', false);
      });
  },
  getComments(
    { commit, rootState },
    { startDate, endDate, page, comment, commentby, source, type }
  ) {
    commit('SET_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    let url =
      process.env.VUE_APP_GET_COMMENTS +
      '?startDate=' +
      startDate +
      '&endDate=' +
      endDate +
      '&page=' +
      page;
    if (comment) {
      url = url + '&comment=' + comment;
    }
    if (commentby) {
      url = url + '&commentby=' + commentby;
    }
    if (source) {
      url = url + '&source=' + source;
    }
    if (type) {
      url = url + '&type=' + type;
    }
    axios
      .get(
        url + '&r=' + r,
        headers.createHeaders(rootState.user.token, 'abdulNewswav')
      )
      .then((res) => {
        console.log(res);
        commit('SET_COMMENTS', res.data);
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  reportAction({ commit, rootState }, { data, status, adwav_user, dateRange }) {
    commit('SET_LOADING', true);
    let action_data = {
      entity_id: data.entity_id,
      comment_id: data.comment_id,
      review_status: status,
      reviewed_by: adwav_user,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    };
    axios
      .post(
        process.env.VUE_APP_REPORT_COMMENT_ACTION,
        action_data,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then((res) => {
        if (res.data.status == 'success') {
          res.data.reports.forEach((a) => {
            a.user_info = `${a.username} (${a.comment.profile_id})`;
            let r = '';
            let item;
            for (item of a.reason_id) {
              r = r + item.reason_en + ',';
            }
            a.report_reason = r.slice(0, -1);
          });
          commit('SET_REPORTS', res.data.reports);
          commit('SET_LOADING', false);
          Vue.swal({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            showCancelButton: false,
            timer: 5000,
            icon: 'success',
            title: 'Success',
            text: `${status} comment successful`,
          });
        } else {
          //alert(`${status} comment unsuccessful : ${res.data.message}`)
          console.log(res.data.message);
          let message = 'Check console for more info';
          let title = 'Error';
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `Action Failed : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
        }
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  commentAction(
    { commit, rootState, dispatch },
    {
      data,
      status,
      adwav_user,
      dateRange,
      page,
      comment,
      commentby,
      source,
      type,
    }
  ) {
    commit('SET_LOADING', true);
    let action_data = {
      entity_id: data.articleID,
      comment_id: data.id,
      review_status: status,
      reviewed_by: adwav_user,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    };
    axios
      .post(
        process.env.VUE_APP_COMMENT_ACTION,
        action_data,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then((res) => {
        if (res.data.status == 'success') {
          let startDate = dateRange.startDate;
          let endDate = dateRange.endDate;
          console.log(page);
          dispatch('getComments', {
            startDate,
            endDate,
            page,
            comment,
            commentby,
            source,
            type,
          });
          let random = +new Date();
          commit('SET_RANDOM', random);
          commit('SET_LOADING', false);
          Vue.swal({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            showCancelButton: false,
            timer: 5000,
            icon: 'success',
            title: 'Success',
            text: `${status} comment successful`,
          });
        } else {
          //alert(`${status} comment unsuccessful : ${res.data.message}`)
          //console.log(res.data.message);
          let message = 'Check console for more info';
          let title = 'Error';
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `Action Failed : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
        }
      })
      .catch((err) => {
        console.log(err);
        //console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },

  getKeywords({ commit, rootState }) {
    console.log('calling getkeywords');
    commit('SET_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    return new Promise((resolve, reject) => {
      axios
        .get(
          process.env.VUE_APP_COMMENT_KEYWORD + '?r=' + r,
          headers.createHeaders(rootState.user.token, 'abdulNewswav')
        )
        .then((res) => {
          let report = res.data.report.join(', ');
          let hide = res.data.hide.join(', ');

          commit('SET_REPORT_KEYWORDS', report);
          commit('SET_HIDE_KEYWORDS', hide);
          commit('SET_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          console.log('error');
          console.log(err);
          //console.log(err.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          console.log(err);
          commit('SET_LOADING', false);
          Bugsnag.notify(err);
          reject(err);
        });
    });
  },

  updateKeyword({ commit, rootState, dispatch }, { report, hide }) {
    commit('SET_LOADING', true);
    let action_data = { report: report, hide: hide };
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_COMMENT_KEYWORD + '?r=' + r,
          action_data,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          console.log(res.data);
          if (res.data.status == 'success') {
            let report = res.data.report.join(', ');
            let hide = res.data.hide.join(', ');
            commit('SET_REPORT_KEYWORDS', report);
            commit('SET_HIDE_KEYWORDS', hide);
            commit('SET_LOADING', false);
            Vue.swal({
              toast: true,
              position: 'top-end',
              showConfirmButton: false,
              showCancelButton: false,
              timer: 5000,
              icon: 'success',
              title: 'Success',
              text: `Update keywords successful`,
            });
            resolve(res);
          } else {
            //alert(`${status} comment unsuccessful : ${res.data.message}`)
            console.log(res.data.message);
            let title = 'Error';
            Vue.notify({
              group: 'error',
              title: title,
              type: 'error',
              text: `Action Failed : update keywords`,
              duration: -1,
              position: 'bottom right',
              closeOnClick: true,
            });
            resolve(res);
          }
        })
        .catch((err) => {
          console.log(err.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          console.log(err);
          commit('SET_LOADING', false);
          Bugsnag.notify(err);
          dispatch('getKeywords');
          reject(err);
        });
    });
  },
};

const mutations = {
  SET_LOADING: (state, val) => (state.loading = val),
  SET_REPORTS: (state, data) => (state.reports = data),
  SET_AI_LOADING: (state, val) => (state.aiLoading = val),
  SET_AI_REPORTS: (state, data) => (state.aiReports = data),
  SET_COMMENTS: (state, data) => (state.comments = data),
  SET_REPORT_KEYWORDS: (state, val) => (state.report_keywords = val),
  SET_HIDE_KEYWORDS: (state, val) => (state.hide_keywords = val),
  SET_RANDOM: (state, val) => (state.random = val),
  SET_FILTERS: (state, filters) => (state.filters = filters),
  SET_PAGE_SETTINGS: (state, settings) => (state.pageSettings = settings),
  SET_COLUMN_FILTERS: (state, columns) => (state.columnFilters = columns),
  SET_SORT: (state, sort) => (state.sort = sort),
  SET_NEXT_PAGE_URL: (state, nextUrl) => (state.nextPageUrl = nextUrl),
  SET_PREV_PAGE_URL: (state, prevUrl) => (state.prevPageUrl = prevUrl),
  SET_UPDATE_COMMENT: (state, updated) => {
    const idx = state.comments.findIndex((i) => i._id === updated._id);
    if (idx !== -1) {
      Vue.set(state.comments, idx, updated);
    }
  },
  SET_UPDATED_REPORTED_COMMENT: (state, updated) => {
    const idx = state.reports.findIndex((i) => i._id === updated._id);
    if (idx !== -1) {
      Vue.set(state.reports, idx, updated);
    }
  },
  SET_COMMENTS_PAGINATION: (state, data) => (state.pagination = data),
  SET_AI_COMMENTS_PAGINATION: (state, data) => (state.aiPagination = data),
};

export default {
  state,
  getters,
  actions,
  mutations,
};

async function mapCommentObject(data) {
  data.userInfo = `${data.comment.user.name} (${data.comment.user.id})`;
  data.source = `${contentMap[data.comment.contentType]} - ${data.comment.contentId}`;
  data.createdAt = format(parseISO(data.createdAt), 'yyyy-MM-dd HH:mm:ss');
  // eslint-disable-next-line no-prototype-builtins
  data.reviewedAt = data.hasOwnProperty('reviewedAt')
    ? format(parseISO(data.reviewedAt), 'yyyy-MM-dd HH:mm:ss')
    : '-';
  let reviewer = '-';
  // eslint-disable-next-line no-prototype-builtins
  if (data.hasOwnProperty('reviewer')) {
    if (data.reviewer.type === 1) {
      reviewer = 'System';
    } else if (data.reviewer.type === 2) {
      reviewer = `${data.reviewer.userName ?? '-'} (${data.reviewer.userId ?? '-'})`;
    }
  }
  // eslint-disable-next-line no-prototype-builtins
  data.reviewer = reviewer;
  // eslint-disable-next-line no-prototype-builtins
  data.reviewType = data.hasOwnProperty('reviewType')
    ? reviewMap[data.reviewType]
    : '-';
}
