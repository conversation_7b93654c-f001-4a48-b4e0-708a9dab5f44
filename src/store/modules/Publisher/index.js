import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const sortMapping = {
  id: 'id',
  name: 'name',
  isVerified: 'verified',
  isEnabled: 'enabled',
  isNative: 'native',
  languageShow: 'language',
  ga_id: 'gaId',
  project_show: 'projectType',
  ad_share: 'adShare',
};
const filtersMapping = {
  id: 'id',
  name: 'name',
  contentType: 'contentType',
  verified: 'verified',
  status: 'status',
  languages: 'languages',
  ga_id: 'gaId',
  content: 'contentType',
  native: 'native',
  projectType: 'projectType',
  autoFollow: 'autoFollow',
  ad_share: 'adShare',
  topics: 'topics',
  limit: 'limit',
};

const state = {
  publishers: [],
  loading: false,
  topics: [],
  default_follow_list: [],
  total_list: [],
  default_loading: false,
  currentPublisher: {},
  channels: [],
  prioritized_pub_list: [],
  pagination: {},
  sorts: {},
  filters: {},
};

const getters = {
  getAllPublisher: (state) => state.publishers,
  getPublisherLoading: (state) => state.loading,
  getTopics: (state) => state.topics,
  getDefaultFollowList: (state) => state.default_follow_list,
  getDefaultLoading: (state) => state.default_loading,
  getTotalList: (state) => state.total_list,
  getCurrentPublisher: (state) => state.currentPublisher,
  getCurrentChannels: (state) => state.channels,
  getPrioritizedPubList: (state) => state.prioritized_pub_list,
  getPublisherPagination: (state) => state.pagination,
  getPublisherSort: (state) => state.sorts,
  getPublisherFilters: (state) => state.filters,
};

const actions = {
  listAllPublishers({ commit, getters, rootState }, query = {}) {
    commit('SET_PUBLISHER_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    // always reset page to 1 when sort/filters change
    if (!query?.page) query.page = 1;
    if (!query?.limit)
      query.limit = getters.getPublisherPagination?.['per_page'] ?? 10;

    // map sort queries
    const sort = getters.getPublisherSort;
    if (Object.keys(sort).length !== 0)
      query.sort = [`${sortMapping[sort.column]} ${sort.asc ? 'asc' : 'desc'}`];

    // map filters queries
    let mappedFilters = {};
    const filters = JSON.parse(JSON.stringify(getters.getPublisherFilters));
    for (const key in filters) {
      mappedFilters[filtersMapping[key]] = filters[key];
    }
    query = { ...query, ...mappedFilters };

    // sanitize empty/null/undefined object
    for (var propName in query) {
      if (
        query[propName] === null ||
        query[propName] === undefined ||
        query[propName] === ''
      ) {
        delete query[propName];
      }
    }

    const url =
      process.env.VUE_APP_LIST_PUBLISHERS +
      '?' +
      new URLSearchParams(query) +
      '&r=' +
      r;
    return new Promise((resolve, reject) => {
      axios
        .get(
          url,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          let list = [];
          res.data.data.forEach((item) => {
            item.hasLogo = item.logo_url !== '' ? 'Yes' : 'No';
            item.imageLink = item.logo_url ?? '';
            item.isVerified = item.verified ? 'Yes' : 'No';
            // item.isNative = item.isReaderMode ? 'Yes' : 'No'
            item.isEnabled = item.enabled === 1 ? 'Enabled' : 'Disabled';
            item.autoFollowShow = item.autoFollow === 1 ? 'Yes' : 'No';
            item.languageShow = item.language;
            item.language = item.language.split(',');
            item.ga_id =
              item.ga_id !== null || item.ga_id !== '' ? item.ga_id : '-';
            let topicShow = item.topics.map((topic) => {
              const found = getters.getTopics.find(
                (top) => top.value === topic
              )?.label;
              return found;
            });
            item.topicShow = topicShow !== undefined ? topicShow : ['-'];

            list.push(item);
          });
          commit('SET_PUBLISHER_LIST', list);
          commit('SET_PUBLISHER_LOADING', false);
          // eslint-disable-next-line no-unused-vars
          const { data, ...paginationDetails } = res.data;
          commit('SET_PAGINATION', paginationDetails);
          resolve(list);
        })
        .catch((err) => {
          commit('SET_GENERAL_LOADING', false, { root: true });
          commit('SET_PUBLISHER_LOADING', false);
          console.log('----Error----');
          console.log(err);
          Bugsnag.notify(err);
          Vue.notify({
            group: 'error',
            title: 'Error',
            type: 'error',
            text: `${err.response.status} : Error From API`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          reject(err);
          reject(err);
        });
    });
  },
  listPublisherById({ commit, getters, rootState }, id) {
    commit('SET_PUBLISHER_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    const url = process.env.VUE_APP_LIST_PUBLISHERS + id + '?r=' + r;
    return new Promise((resolve, reject) => {
      axios
        .get(
          url,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          let list = [];
          res.data.forEach((item) => {
            item.hasLogo = item.logo_url !== '' ? 'Yes' : 'No';
            item.imageLink = item.logo_url ?? '';
            item.isVerified = item.verified ? 'Yes' : 'No';
            // item.isNative = item.isReaderMode ? 'Yes' : 'No'
            item.isEnabled = item.enabled === 1 ? 'Enabled' : 'Disabled';
            item.autoFollowShow = item.autoFollow === 1 ? 'Yes' : 'No';
            item.languageShow = item.language;
            item.language = item.language.split(',');
            item.ga_id =
              item.ga_id !== null || item.ga_id !== '' ? item.ga_id : '-';

            item.topics = item.topics.map((topic) => {
              return getters.getTopics.find((top) => top.value === topic);
            });

            item.channels.forEach((ch) => {
              let sudoHost = [];
              if (ch.endpoints !== null) {
                let endpoints = ch.endpoints.split(',');
                endpoints.forEach((item) => {
                  let newHost = item.replace(`?language=${ch.language}`, '');
                  sudoHost.push(newHost);
                });
              } else {
                let sudo = ch.host.replace(`?language=${ch.language}`, '');
                sudoHost.push(sudo);
              }
              let type = 'Articles';
              let content_type = 'article';
              if (ch.has_videos === 1) {
                type = 'Videos';
                content_type = 'video';
              } else if (ch.has_podcasts === 1) {
                type = 'Podcasts';
                content_type = 'podcast';
              }
              ch.host = sudoHost.join(', ');
              ch.contentType = type;
              ch.content_type = content_type;
              ch.isEnabled = ch.enabled ? 'Enabled' : 'Disabled';
              ch.ch_language = ch.language;
              ch.contentLang = ch.language.toUpperCase();
              return ch;
            });
            list.push(item);
          });
          commit('SET_CURRENT_PUBLISHER', list[0]);
          commit('SET_CURRENT_CHANNELS', list[0].channels);
          commit('SET_PUBLISHER_LOADING', false);
          resolve(list);
        })
        .catch((err) => {
          commit('SET_PUBLISHER_LOADING', false);
          console.log('----Error----');
          console.log(err);
          Bugsnag.notify(err);
          reject(err);
        });
    });
  },
  listPublisherMonitoringById({ commit, rootState }, id) {
    commit('SET_PUBLISHER_LOADING', true);
    const url = process.env.VUE_APP_CREATE_PUBLISHER_MONITORING + id;
    return new Promise((resolve, reject) => {
      axios
        .get(
          url,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          commit('SET_PUBLISHER_LOADING', false);
          resolve(res.data);
        })
        .catch((err) => {
          commit('SET_PUBLISHER_LOADING', false);
          console.log('----Error----');
          console.log(err);
          Bugsnag.notify(err);
          reject(err);
        });
    });
  },
  updatePublisherMonitoringById({ commit, rootState }, pub) {
    commit('SET_GENERAL_LOADING', true, { root: true });
    const url = process.env.VUE_APP_CREATE_PUBLISHER_MONITORING + pub.id;
    return new Promise((resolve, reject) => {
      axios
        .put(
          url,
          pub,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          commit('SET_GENERAL_LOADING', false, { root: true });
          resolve(res);
        })
        .catch((err) => {
          commit('SET_GENERAL_LOADING', false, { root: true });
          console.log('----Error----');
          console.log(err);
          Bugsnag.notify(err);
          Vue.swal({
            icon: 'error',
            text: err,
            showCancelButton: false,
          });
          reject(err);
        });
    });
  },
  listTopics({ commit, rootState }) {
    return new Promise((resolve, reject) => {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      axios
        .get(
          process.env.VUE_APP_LIST_TOPICS + '?r=' + r,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          res.data.sort((a, b) => a.label.localeCompare(b.label));
          commit('SET_TOPIC_LIST', res.data);
          resolve('done');
        })
        .catch((err) => {
          commit('SET_GENERAL_LOADING', false, { root: true });
          console.log('----Error----');
          console.log(err);
          Bugsnag.notify(err);
          reject(err);
        });
    });
  },
  editPublisher({ commit, getters, rootState }, pub) {
    commit('SET_GENERAL_LOADING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .put(
          process.env.VUE_APP_EDIT_PUBLISHER + pub.id,
          pub,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          let list = [];
          res.data.forEach((item) => {
            item.hasLogo = item.logo_url !== '' ? 'Yes' : 'No';
            item.imageLink = item.logo_url ?? '';
            item.isVerified = item.verified ? 'Yes' : 'No';
            // item.isNative = item.isReaderMode ? 'Yes' : 'No'
            item.isEnabled = item.enabled === 1 ? 'Enabled' : 'Disabled';
            item.autoFollowShow = item.autoFollow === 1 ? 'Yes' : 'No';
            item.languageShow = item.language;
            item.language = item.language.split(',');
            item.ga_id =
              item.ga_id !== null || item.ga_id !== '' ? item.ga_id : '-';

            let topicShow = item.topics.map((topic) => {
              const found = getters.getTopics.find(
                (top) => top.value === topic
              )?.label;
              return found;
            });
            item.topicShow = topicShow !== undefined ? topicShow : ['-'];

            item.channels.forEach((ch) => {
              let sudoHost = [];
              if (ch.endpoints !== null) {
                let endpoints = ch.endpoints.split(',');
                endpoints.forEach((item) => {
                  let newHost = item.replace(`?language=${ch.language}`, '');
                  sudoHost.push(newHost);
                });
              } else {
                let sudo = ch.host.replace(`?language=${ch.language}`, '');
                sudoHost.push(sudo);
              }
              let type = 'Articles';
              let content_type = 'article';
              if (ch.has_videos === 1) {
                type = 'Videos';
                content_type = 'video';
              } else if (ch.has_podcasts === 1) {
                type = 'Podcasts';
                content_type = 'podcast';
              }
              ch.host = sudoHost.join(', ');
              ch.contentType = type;
              ch.content_type = content_type;
              ch.isEnabled = ch.enabled ? 'Yes' : 'No';
              ch.ch_language = ch.language;
              ch.contentLang = ch.language.toUpperCase();
              return ch;
            });
            list.push(item);
          });
          commit('SET_NEWUPDATE_PUBLISHER', list[0]);
          commit('SET_GENERAL_LOADING', false, { root: true });
          resolve(res);
        })
        .catch((err) => {
          commit('SET_GENERAL_LOADING', false, { root: true });
          console.log('----Error----');
          console.log(err);
          Bugsnag.notify(err);
          let msg = `Something went wrong when updating ${pub.name} information!`;
          if (err.response.data.message) {
            msg = err.response.data.message;
          }
          Vue.swal({
            icon: 'error',
            text: msg,
            showCancelButton: false,
          });
          reject(err);
        });
    });
  },
  createNewPublisher({ commit, getters, rootState }, pub) {
    commit('SET_GENERAL_LOADING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_CREATE_PUBLISHER,
          pub,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          let list = [];
          res.data.forEach((item) => {
            item.hasLogo = item.logo_url !== '' ? 'Yes' : 'No';
            item.imageLink = item.logo_url ?? '';
            item.isVerified = item.verified ? 'Yes' : 'No';
            item.isNative = item.isReaderMode ? 'Yes' : 'No';
            item.isEnabled = item.enabled === 1 ? 'Enabled' : 'Disabled';
            item.autoFollowShow = item.autoFollow === 1 ? 'Yes' : 'No';
            item.languageShow = item.language;
            item.language = item.language.split(',');
            item.ga_id =
              item.ga_id !== null || item.ga_id !== '' ? item.ga_id : '-';
            let topicShow = item.topics.map((topic) => {
              const found = getters.getTopics.find(
                (top) => top.value === topic
              )?.label;
              return found;
            });
            item.topicShow = topicShow !== undefined ? topicShow : ['-'];
            list.push(item);
          });
          commit('SET_NEW_PUBLISHER', list[0]);
          commit('SET_GENERAL_LOADING', false, { root: true });
          resolve(res);
        })
        .catch((err) => {
          commit('SET_GENERAL_LOADING', false, { root: true });
          console.log(err.response.data);
          console.log('----Error----');
          console.log(err);
          Bugsnag.notify(err);
          let msg = `Something went wrong while creating ${pub.name} as a publisher on Newswav`;
          if (
            err.response &&
            err.response.status &&
            err.response.status == 400
          ) {
            if (err.response.data.message) {
              msg = err.response.data.message;
            } else if (err.response.data.error) {
              msg = err.response.data.error;
            }

            Vue.swal({
              icon: 'error',
              text: `${msg}`,
              showCancelButton: false,
            });
          }
          reject(err);
        });
    });
  },
  createPublisherChannel({ rootState }, channel) {
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_CREATE_PUB_CHANNEL + channel.publisher_id,
          channel,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  editPublisherChannel({ rootState }, channel) {
    return new Promise((resolve, reject) => {
      axios
        .put(
          process.env.VUE_APP_EDIT_PUB_CHANNEL + channel.id,
          channel,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  listDefaultFollowList({ commit, rootState }) {
    commit('SET_DEFAULT_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_DEFAULT_FOLLOW_LIST + '?r=' + r,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        commit('SET_DEFAULT_FOLLOW_LIST', res.data.follow_list);
        commit('SET_DEFAULT_LOADING', false);
      })
      .catch((err) => {
        console.log(err);
        alert('Something went wrong in getting default follow list');
        commit('SET_DEFAULT_LOADING', false);
      });
  },
  listTotalList({ commit, rootState }) {
    commit('SET_DEFAULT_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_TOTAL_LIST + '?r=' + r,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        res.data.total_list.sort((a, b) => a.name.localeCompare(b.name));
        commit('SET_TOTAL_LIST', res.data.total_list);
        commit('SET_DEFAULT_LOADING', false);
      })
      .catch((err) => {
        console.log(err);
        alert('Something went wrong in getting total list');
        commit('SET_DEFAULT_LOADING', false);
      });
  },
  addToFollowList({ commit, rootState }, item) {
    commit('SET_DEFAULT_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_DEFAULT_FOLLOW_LIST,
          item,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  updateFollowList({ commit, rootState }, id) {
    commit('SET_DEFAULT_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .put(
          process.env.VUE_APP_DEFAULT_FOLLOW_LIST + id,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          resolve(res);
          commit('SET_DEFAULT_LOADING', false);
        })
        .catch((err) => {
          reject(err);
          commit('SET_DEFAULT_LOADING', false);
        });
    });
  },
  listPubPriority({ commit, rootState }) {
    commit('SET_DEFAULT_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_PUB_PRIORITY_LIST + '?r=' + r,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        commit('SET_PUB_PRIORITY_LIST', res.data.priority_list);
        commit('SET_DEFAULT_LOADING', false);
      })
      .catch((err) => {
        console.log(err);
        alert('Something went wrong in getting PN-Prioritized Pub list');
        commit('SET_DEFAULT_LOADING', false);
      });
  },
  addToPubPriorityList({ commit, rootState }, item) {
    commit('SET_DEFAULT_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_PUB_PRIORITY_LIST,
          item,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          resolve(res);
          commit('SET_DEFAULT_LOADING', false);
        })
        .catch((err) => {
          reject(err);
          commit('SET_DEFAULT_LOADING', false);
        });
    });
  },
  updatePubPriorityList({ commit, rootState }, id) {
    commit('SET_DEFAULT_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .delete(
          process.env.VUE_APP_PUB_PRIORITY_LIST + id,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          resolve(res);
          commit('SET_DEFAULT_LOADING', false);
        })
        .catch((err) => {
          reject(err);
          commit('SET_DEFAULT_LOADING', false);
        });
    });
  },
};

const mutations = {
  SET_PUBLISHER_LIST: (state, list) => {
    state.publishers = list;
  },
  SET_NEWUPDATE_PUBLISHER: (state, updPub) => {
    const index = state.publishers.findIndex((pub) => pub.id === updPub.id);
    if (index !== -1) {
      state.publishers.splice(index, 1, updPub);
    }
  },
  SET_TOPIC_LIST: (state, list) => (state.topics = list),
  SET_NEW_PUBLISHER: (state, newPub) => state.publishers.unshift(newPub),
  SET_PUBLISHER_LOADING: (state, val) => (state.loading = val),
  SET_DEFAULT_FOLLOW_LIST: (state, list) => (state.default_follow_list = list),
  SET_TOTAL_LIST: (state, list) => (state.total_list = list),
  SET_DEFAULT_LOADING: (state, val) => (state.default_loading = val),
  SET_CURRENT_PUBLISHER: (state, obj) => (state.currentPublisher = obj),
  SET_CURRENT_CHANNELS: (state, list) => (state.channels = list),
  SET_ADD_TO_CURRENT_CHANNELS: (state, list) => {
    list.forEach((ch) => {
      let type = 'Articles';
      if (ch.has_videos === 1) {
        type = 'Videos ';
      } else if (ch.has_podcasts) {
        type = 'Podcasts';
      }
      ch.contentType = type;
      ch.isEnabled = ch.enabled ? 'Yes' : 'No';
      ch.contentLang = ch.language.toUpperCase();
      return ch;
    });
    list.forEach((item) => {
      state.channels.unshift(item);
    });
  },
  SET_PUB_PRIORITY_LIST: (state, list) => (state.prioritized_pub_list = list),
  SET_PAGINATION: (state, page) => (state.pagination = page),
  SET_SORT: (state, sort) => (state.sorts = sort),
  SET_FILTERS: (state, filters) => (state.filters = filters),
};

export default {
  state,
  getters,
  actions,
  mutations,
};
