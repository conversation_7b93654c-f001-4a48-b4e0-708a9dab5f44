import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import * as Sorter from '@/helpers/sort';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const state = {
  currentCampaignHistories: [],
  currentCampaignEvents: [],
  currentCampaign: {},
  campaigns: [],
  oneCampaign: {},
  allAdsInCampaign: [],
  campaign_progress: [],
  campaignLoading: false,
  progressLoading: false,
  locations: [],
  interests: [],
  feeds: [],
  telcos: [],
  devices: [],
};

const getters = {
  getAllCampaigns: (state) => state.campaigns,
  getAllAdsInCampaign: (state) => state.allAdsInCampaign,
  getCampaignProgress: (state) => state.campaign_progress,
  getCampaign: (state) => (id) => {
    return state.campaigns.find((camp) => camp.id === id);
  },
  getCampaignDetail: (state) => state.oneCampaign,
  getCampaignLoading: (state) => state.campaignLoading,
  getProgressLoading: (state) => state.progressLoading,
  getCurrentCampaign: (state) => state.currentCampaign,
  getCampaignEvents: (state) => state.currentCampaignEvents,
  getCampaignHistory: (state) => state.currentCampaignHistories,
  getLocations: (state) => state.locations,
  getInterests: (state) => state.interests,
  getFeeds: (state) => state.feeds,
  getTelcos: (state) => state.telcos,
  getDevices: (state) => state.devices,
};

const actions = {
  addAdvertInCampaign({ commit, rootState }, advert) {
    commit('SET_GENERAL_LOADING', true, { root: true });
    axios
      .post(
        process.env.VUE_APP_ADD_ADVERTISEMENT_ENDPOINT,
        advert,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        commit('SET_NEW_AD_IN_CAMPAIGN', res.data);
        commit('SET_GENERAL_LOADING', false, { root: true });
        Vue.swal.fire({
          icon: 'success',
          text: 'Advertisement(s) created successfully',
          showCancelButton: false,
        });
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. You have no permission to add advertisement';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        commit('SET_GENERAL_LOADING', false, { root: true });
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  getAllCampaign({ commit, rootState }, id) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    commit('SET_CAMPAIGN_LOADING', true);
    axios
      .get(
        process.env.VUE_APP_GET_CAMPAIGN_BY_ADVERTISER + id + '?r=' + r,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        commit('SET_CAMPAIGNS', res.data);
        commit('SET_CAMPAIGN_LOADING', false);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. You have no permission to access this page';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        commit('SET_CAMPAIGN_LOADING', false);
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  getCampaignDetail({ commit, rootState }, id) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    let url = process.env.VUE_APP_GET_CAMPAIGN_DETAIL.replace(
      '{campaign_id}',
      id
    );
    commit('SET_CAMPAIGN_LOADING', true);
    commit('SET_GENERAL_LOADING', true);
    axios
      .get(url + '?r=' + r, headers.createHeaders(rootState.user.token))
      .then((res) => {
        commit('SET_CAMPAIGN', res.data);
        commit('SET_CAMPAIGN_LOADING', false);
        commit('SET_GENERAL_LOADING', false);
      })
      .catch((error) => {
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. You have no permission to access this page';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        commit('SET_CAMPAIGN_LOADING', false);
        commit('SET_GENERAL_LOADING', false);
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  getOneCampaignDetail({ commit, rootState }, id) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    let url = process.env.VUE_APP_GET_CAMPAIGN_DETAIL.replace(
      '{campaign_id}',
      id
    );
    commit('SET_CAMPAIGN_LOADING', true);
    commit('SET_GENERAL_LOADING', true);
    return axios
      .get(url + '?r=' + r, headers.createHeaders(rootState.user.token))
      .then((res) => {
        commit('SET_CAMPAIGN_LOADING', false);
        commit('SET_GENERAL_LOADING', false);
        console.log('loaded', res.data);

        if (res.data) {
          return res.data;
        } else {
          throw new Error('No campaign data found in the response.');
        }
      })
      .catch((error) => {
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. You have no permission to access this page';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        commit('SET_CAMPAIGN_LOADING', false);
        commit('SET_GENERAL_LOADING', false);
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  getAdsProgressCampaign({ commit, rootState }, id) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    commit('SET_PROGRESS_LOADING', true);
    commit('SET_CAMPAIGN_LOADING', true);
    axios
      .get(
        process.env.VUE_APP_GET_ADS_IN_CAMPAIGN + id + '?r=' + r,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        commit('SET_ADS_IN_CAMPAIGN', res.data.ads);
        commit('SET_CURRENT_CAMPAIGN', res.data.campaign[0]);
        commit('SET_CAMPAIGN_PROGRESS', res.data.progress[0]);
        commit('SET_CURRENT_CAMPAIGN_EVENTS', res.data.events);
        commit('SET_CURRENT_CAMPAIGN_HISTORIES', res.data.histories);
        commit('SET_PROGRESS_LOADING', false);
        commit('SET_CAMPAIGN_LOADING', false);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message =
            'Unauthorized. You have no permission to access campaign data';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        commit('SET_PROGRESS_LOADING', false);
        commit('SET_CAMPAIGN_LOADING', false);
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  editCampaign({ commit, rootState }, campaign) {
    commit('SET_GENERAL_LOADING', true, { root: true });
    campaign.user_email = rootState.user.user.email;
    axios
      .put(
        process.env.VUE_APP_EDIT_CAMPAIGN_ENDPOINT + campaign.id,
        campaign,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        commit('SET_UPDATE_CAMPAIGN', res.data);
        commit('SET_GENERAL_LOADING', false, { root: true });
        Vue.swal.fire({
          icon: 'success',
          text: 'Campaign successfully updated!',
          showCancelButton: false,
        });
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. You have no permission to edit campaign';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        commit('SET_GENERAL_LOADING', false, { root: true });
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  removeCampaign({ commit, rootState }, id) {
    axios
      .delete(
        process.env.VUE_APP_DELETE_CAMPAIGN_ENDPOINT + id,
        headers.createHeaders(rootState.user.token)
      )
      .then(() => {
        commit('SET_DELETE_CAMPAIGN', id);
        Vue.swal.fire({
          icon: 'success',
          text: 'Campaign successfully deleted!',
          showCancelButton: false,
        });
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. You have no permission to delete campaign';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  unLinkAd({ commit, rootState }, id) {
    axios
      .delete(
        process.env.VUE_APP_UNLINK_AD + id,
        headers.createHeaders(rootState.user.token)
      )
      .then(() => {
        commit('SET_UNLINK_AD', id);
        Vue.swal.fire({
          icon: 'success',
          text: 'Ad was unlinked with the campaign',
          showCancelButton: false,
        });
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. You have no permission to unlink ad';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log('---Error---');
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  exportAd({ rootState }, id) {
    axios
      .get(process.env.VUE_APP_EXPORT_AD.replace('{id}', id), {
        responseType: 'blob',
        headers: {
          Authorization: `Bearer ${rootState.user.token}`,
        },
      })
      .then((response) => {
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `ad_${id}.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. You have no permission to export ad';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log('---Error---');
        console.log(error);
        Bugsnag.notify(error);
      });
  },

  listLocations({ commit, rootState }) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    return axios
      .get(
        process.env.VUE_APP_GET_LOCATIONS + '?r=' + r,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        let data = Sorter.sortByProperty(res.data, 'location');
        commit('SET_LOCATIONS', data);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message =
            'Unauthorized. You have no permission to access locations data';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  listInterests({ commit, rootState }) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    return axios
      .get(
        process.env.VUE_APP_GET_INTEREST_ESTIMATES + '?r=' + r,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        commit('SET_INTERESTS', res.data);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message =
            'Unauthorized. You have no permission to access interests data';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  async listFeeds({ commit, rootState }) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    return axios
      .get(
        process.env.VUE_APP_GET_FEEDS + '?r=' + r,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        commit('SET_FEEDS', res.data);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. You have no permission to access feeds data';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  listTelcos({ commit, rootState }) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    return axios
      .get(
        process.env.VUE_APP_GET_TELCOS + '?r=' + r,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        commit('SET_TELCOS', res.data);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. You have no permission to access feeds data';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  listDevices({ commit, rootState }) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    return axios
      .get(
        process.env.VUE_APP_GET_DEVICES + '?r=' + r,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        commit('SET_DEVICES', res.data);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. You have no permission to access feeds data';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(error);
        Bugsnag.notify(error);
      });
  },
};

const mutations = {
  SET_LOCATIONS: (state, list) => (state.locations = list),
  SET_INTERESTS: (state, list) => (state.interests = list),
  SET_FEEDS: (state, list) => (state.feeds = list),
  SET_TELCOS: (state, list) => (state.telcos = list),
  SET_DEVICES: (state, list) => (state.devices = list),
  SET_CURRENT_CAMPAIGN_HISTORIES: (state, histories) =>
    (state.currentCampaignHistories = histories),
  SET_CURRENT_CAMPAIGN_EVENTS: (state, events) =>
    (state.currentCampaignEvents = events),
  SET_CURRENT_CAMPAIGN: (state, camp) => (state.currentCampaign = camp),
  SET_CAMPAIGN_LOADING: (state, value) => (state.campaignLoading = value),
  SET_ADS_IN_CAMPAIGN: (state, ads) => (state.allAdsInCampaign = ads),
  SET_CAMPAIGNS: (state, campaigns) => (state.campaigns = campaigns),
  SET_CAMPAIGN: (state, campaign) => (state.oneCampaign = campaign),
  SET_UPDATE_CAMPAIGN: (state, updCampaign) => {
    const index = state.campaigns.findIndex(
      (campaigns) => campaigns.id === updCampaign.id
    );
    if (index != -1) {
      state.campaigns.splice(index, 1, updCampaign);
    }
  },
  SET_DELETE_CAMPAIGN: (state, id) =>
    (state.campaigns = state.campaigns.filter(
      (campaign) => campaign.id !== id
    )),
  SET_NEW_CAMPAIGN: (state, newCampaign) => state.campaigns.push(newCampaign),
  SET_CAMPAIGN_PROGRESS: (state, progress) =>
    (state.campaign_progress = progress),

  SET_PROGRESS_LOADING: (state, value) => (state.progressLoading = value),

  SET_NEW_AD_IN_CAMPAIGN: (state, newAd) => {
    newAd.forEach((data) => {
      state.allAdsInCampaign.push(data);
    });
  },
  SET_CLEAR_CAMPAIGN_SETTINGS: (state) => {
    state.campaign_progress = [];
    state.allAdsInCampaign = [];
  },

  SET_UNLINK_AD: (state, link_id) =>
    (state.allAdsInCampaign = state.allAdsInCampaign.filter(
      (ads) => ads.link_id !== link_id
    )),
  SET_DELETE_UNLINK_AD: (state, id) =>
    (state.allAdsInCampaign = state.allAdsInCampaign.filter(
      (ads) => ads.id !== id
    )),
  SET_UPDATE_ADS_CAMPAIGN: (state, updAds) => {
    updAds.forEach((updatedAd) => {
      const index = state.allAdsInCampaign.findIndex(
        (ad) => ad.id === updatedAd.id
      );

      if (index != -1) {
        state.allAdsInCampaign.splice(index, 1, updatedAd);
      }
    });
  },
};

export default {
  state,
  actions,
  getters,
  mutations,
};
