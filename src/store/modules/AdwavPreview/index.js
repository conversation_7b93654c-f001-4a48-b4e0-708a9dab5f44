import axios from 'axios';
import * as headers from '@/helpers/headers';
import fromUnixTime from 'date-fns/fromUnixTime';
import format from 'date-fns/format';

const state = {
  preview_link_list: [],
};

const getters = {
  getPreviewLinkList: (state) => state.preview_link_list,
};

const actions = {
  generateLink({ commit, rootState }, ids) {
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_ADWAV_PREVIEW_LINK,
          ids,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          let preview_link =
            process.env.VUE_APP_ADWAV_PREVIEW_HOST +
            res.data.object.unique_code;
          resolve(preview_link);
          commit('SET_NEW_PREVIEW_LINK_LIST', res.data.object);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  listPreviewLink({ commit, rootState }) {
    axios
      .get(
        process.env.VUE_APP_ADWAV_PREVIEW_LINK,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        res.data.forEach((item) => {
          let base = item.campaign_ids.split(',');
          item.campaign_ids = base.sort();
          item.link = process.env.VUE_APP_ADWAV_PREVIEW_HOST + item.unique_code;
          item.created_at = format(
            fromUnixTime(item.created_at),
            'yyyy/MM/dd kk:mm:ss'
          );
          item.valid_until = format(
            fromUnixTime(item.valid_until),
            'yyyy/MM/dd kk:mm:ss'
          );
          return item;
        });
        commit('SET_PREVIEW_LINK_LIST', res.data);
      })
      .catch((err) => {
        console.log(err);
      });
  },
};

const mutations = {
  SET_PREVIEW_LINK_LIST: (state, list) => (state.preview_link_list = list),
  SET_NEW_PREVIEW_LINK_LIST: (state, item) => {
    let base = item.campaign_ids.split(',');
    item.campaign_ids = base.sort();
    item.link = process.env.VUE_APP_ADWAV_PREVIEW_HOST + item.unique_code;
    item.created_at = format(
      fromUnixTime(item.created_at),
      'yyyy/MM/dd kk:mm:ss'
    );
    item.valid_until = format(
      fromUnixTime(item.valid_until),
      'yyyy/MM/dd kk:mm:ss'
    );
    state.preview_link_list.unshift(item);
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};
