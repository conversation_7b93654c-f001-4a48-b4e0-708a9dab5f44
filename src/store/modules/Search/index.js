import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const state = {
  results: [],
  loading: false,
};

const getters = {
  getResults: (state) => state.results,
  getSearchLoading: (state) => state.loading,
  getArrItem: (state) => (uniqueID) => {
    return state.results.find((item) => item.uniqueID === uniqueID);
  },
};

const actions = {
  getResourceByID({ commit, rootState }, id) {
    commit('SET_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_GET_RESOURCE_BY_UNIQUE_ID + id + '?r=' + r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        let results = [];
        let data = {};
        if (id.charAt(0) === 'A') {
          data = {
            type: 'article',
            id: res.data.id,
            uniqueID: res.data.uniqueID,
            url: res.data.canonicalURL,
            title: res.data.title,
            author: res.data.author,
            description: res.data.description,
            channelImageURL: res.data.channelImageURL,
            language: res.data.language,
            publisher: res.data.publisher.publisherName,
            publishedDate: res.data.publishedDate,
            createdDate: res.data.createdDate,
            media: res.data.mediaArray,
            viewCount: res.data.viewCount,
            reactionCount: res.data.reactionCount,
            commentCount: res.data.commentCount,
            channelID: res.data.channelID,
            deleted: res.data.deleted,
            comments_disabled: res.data.comments_disabled,
            permalink: res.data.permalink,
            isTranslated: res.data.isTranslated,
          };
        } else if (id.charAt(0) === 'P') {
          data = {
            type: 'podcast',
            id: res.data.id,
            uniqueID: res.data.unique_id,
            url: res.data.url,
            title: res.data.title,
            author: res.data.author,
            description: res.data.description,
            show_name: res.data.show_name,
            channelImageURL: res.data.channel.logo_url,
            pod_url: res.data.pod_url,
            language: res.data.channel.language,
            publisher: res.data.channel.name,
            publishedDate: res.data.published_date,
            createdDate: res.data.created_at,
            media: res.data.images[0].url,
            viewCount: res.data.viewsCount.toString(),
            reactionCount: res.data.reactionsCount.toString(),
            commentCount: res.data.commentsCount.toString(),
            channelID: res.data.channel.channel_id,
            deleted: res.data.deleted,
            comments_disabled: res.data.comments_disabled,
            permalink: res.data.permalink,
            isTranslated: false,
          };
        } else if (id.charAt(0) === 'M') {
          data = {
            type: 'mikrowav',
            id: res.data.id,
            uniqueID: res.data.unique_id,
            url: null,
            title: res.data.content,
            author: res.data.username,
            description: res.data.content,
            channelImageURL: res.data.user_profile_picture,
            language: 'en',
            publisher: res.data.username,
            publishedDate: res.data.created_at,
            createdDate: res.data.created_at,
            media: res.data.media,
            viewCount: res.data.total_views,
            reactionCount: 0,
            commentCount: res.data.total_comments,
            channelID: null,
            deleted: res.data.is_flagged ? '1' : '0',
            comments_disabled: res.data.is_comment_disabled ? '1' : '0',
            permalink: res.data.unique_id,
            isTranslated: false,
          };
        } else {
          data = {
            type: 'video',
            id: res.data.id,
            uniqueID: res.data.unique_id,
            url: res.data.page_url,
            title: res.data.title,
            author: res.data.author,
            description: res.data.description,
            channelImageURL: res.data.channel.logo_url,
            language: res.data.channel.language,
            publisher: res.data.channel.name,
            publishedDate: res.data.published_date,
            createdDate: res.data.created_at,
            media: res.data.files[0].url,
            viewCount: res.data.viewsCount.toString(),
            reactionCount: res.data.reactionsCount.toString(),
            commentCount: res.data.commentsCount.toString(),
            channelID: res.data.channel.id,
            deleted: res.data.deleted,
            comments_disabled: res.data.comments_disabled,
            permalink: res.data.permalink,
            isTranslated: false,
          };
        }
        results.unshift(data);
        commit('SET_RESULTS', results);
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err);
        commit('SET_LOADING', false);
      });
  },
  getArticle({ commit, rootState }, { keyword, type }) {
    // set loading to true
    commit('SET_LOADING', true);
    // set result to empty (reset filtered result each time)
    commit('SET_RESULTS', []);

    // encode # before building request
    let encodedKeyword = keyword.replace(/#/g, encodeURIComponent('#'));

    // get random number to confuse cache so it process data everytime
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;

    console.log('Keyword index gotten is');
    console.log(keyword);

    // form request to API and create header for authentication, then send request to API
    axios
      .get(
        process.env.VUE_APP_GET_SEARCH +
          '?q=' +
          encodedKeyword +
          '&type=' +
          type +
          '&r=' +
          r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        var articles = res.data.articles.map(function (article) {
          var data = {
            type: 'article',
            id: article.id,
            uniqueID: article.uniqueID,
            url: article.canonicalURL,
            title: article.title,
            author: article.author,
            description: article.description,
            channelImageURL: article.channelImageURL,
            language: article.language,
            publisher: article.publisher.publisherName,
            publishedDate: article.publishedDate,
            createdDate: article.createdDate,
            media: article.mediaArray,
            viewCount: article.viewCount,
            reactionCount: article.reactionCount,
            commentCount: article.commentCount,
            channelID: article.channelID,
            deleted: article.deleted,
            comments_disabled: article.comments_disabled,
            permalink: article.permalink,
          };
          return data;
        });
        commit('SET_RESULTS', articles);
        commit('SET_LOADING', false);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        commit('SET_LOADING', false);
        Bugsnag.notify(error);
      });
  },
  getArticlePage({ commit, rootState }, { keyword, page, type }) {
    commit('SET_LOADING', true);
    commit('SET_RESULTS', []);

    let encodedKeyword = keyword.replace(/#/g, encodeURIComponent('#'));

    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_GET_SEARCH +
          '?q=' +
          encodedKeyword +
          '&page=' +
          page +
          '&type=' +
          type +
          '&r=' +
          r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        let articles = res.data.articles.map(function (article) {
          let data = {
            type: 'article',
            id: article.id,
            uniqueID: article.uniqueID,
            url: article.url,
            title: article.title,
            author: article.author,
            description: article.description,
            channelImageURL: article.channelImageURL,
            language: article.language,
            publisher: article.publisher.publisherName,
            publishedDate: article.publishedDate,
            createdDate: article.createdDate,
            media: article.mediaArray,
            viewCount: article.viewCount,
            reactionCount: article.reactionCount,
            commentCount: article.commentCount,
            channelID: article.channelID,
            deleted: article.deleted,
            comments_disabled: article.comments_disabled,
            permalink: article.permalink,
          };
          return data;
        });
        commit('SET_CURRENT_RESULTS', articles);
        commit('SET_LOADING', false);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        commit('SET_LOADING', false);
        Bugsnag.notify(error);
      });
  },
  getVideo({ commit, rootState }, { keyword, type }) {
    commit('SET_LOADING', true);
    commit('SET_RESULTS', []);

    let encodedKeyword = keyword.replace(/#/g, encodeURIComponent('#'));

    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_GET_SEARCH +
          '?q=' +
          encodedKeyword +
          '&type=' +
          type +
          '&r=' +
          r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        let videos = res.data.videos.map(function (video) {
          let data = {
            type: 'video',
            id: video.id,
            uniqueID: video.unique_id,
            url: video.page_url,
            title: video.title,
            author: video.author,
            description: video.description,
            channelImageURL: video.channel.logo_url,
            language: video.channel.language,
            publisher: video.channel.name,
            publishedDate: video.published_date,
            createdDate: video.created_at,
            media: video.files[0].url,
            viewCount: video.viewsCount.toString(),
            reactionCount: video.reactionsCount.toString(),
            commentCount: video.commentsCount.toString(),
            channelID: video.channel.id,
            deleted: video.deleted,
            comments_disabled: video.comments_disabled,
            permalink: video.permalink,
          };
          return data;
        });
        commit('SET_RESULTS', videos);
        commit('SET_LOADING', false);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        commit('SET_LOADING', false);
        Bugsnag.notify(error);
      });
  },
  getVideoPage({ commit, rootState }, { keyword, page, type }) {
    commit('SET_LOADING', true);
    commit('SET_RESULTS', []);
    let encodedKeyword = keyword.replace(/#/g, encodeURIComponent('#'));

    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_GET_SEARCH +
          '?q=' +
          encodedKeyword +
          '&type=' +
          type +
          '&page=' +
          page +
          '&r=' +
          r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        let videos = res.data.videos.map(function (video) {
          let data = {
            type: 'video',
            id: video.id,
            uniqueID: video.unique_id,
            url: video.page_url,
            title: video.title,
            author: video.author,
            description: video.description,
            channelImageURL: video.channel.logo_url,
            language: video.channel.language,
            publisher: video.channel.name,
            publishedDate: video.published_date,
            createdDate: video.created_at,
            media: video.files[0].url,
            viewCount: video.viewsCount.toString(),
            reactionCount: video.reactionsCount.toString(),
            commentCount: video.commentsCount.toString(),
            channelID: video.channel.id,
            deleted: video.deleted,
            comments_disabled: video.comments_disabled,
            permalink: video.permalink,
          };
          return data;
        });
        commit('SET_CURRENT_RESULTS', videos);
        commit('SET_LOADING', false);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        commit('SET_LOADING', false);
        Bugsnag.notify(error);
      });
  },
  getPodcast({ commit, rootState }, search) {
    commit('SET_LOADING', true);
    commit('SET_RESULTS', []);
    let encodedKeyword = search.keyword.replace(/#/g, encodeURIComponent('#'));

    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    let url =
      process.env.VUE_APP_GET_SEARCH +
      '?q=' +
      encodedKeyword +
      '&type=' +
      search.type +
      '&r=' +
      r;

    if (search.page) {
      url += '&page=' + search.page;
    }
    axios
      .get(
        url,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        let podcasts = res.data.podcasts.map((item) => {
          let data = {
            type: 'podcast',
            id: item.id,
            uniqueID: item.unique_id,
            url: item.url,
            title: item.title,
            author: item.author,
            description: item.description,
            show_name: item.show_name,
            channelImageURL: item.channel.logo_url,
            pod_url: item.pod_url,
            language: item.channel.language,
            publisher: item.channel.name,
            publishedDate: item.published_date,
            media: item.images[0].url,
            viewCount: item.viewsCount.toString(),
            reactionCount: item.reactionsCount.toString(),
            commentCount: item.commentsCount.toString(),
            channelID: item.channel.channel_id,
            deleted: item.deleted,
            comments_disabled: item.comments_disabled,
            permalink: item.permalink,
          };
          return data;
        });
        commit('SET_CURRENT_RESULTS', podcasts);
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err);
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  hideResource({ commit, rootState }, article) {
    commit('SET_GENERAL_LOADING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .put(
          process.env.VUE_APP_HIDE_ARTICLE + article.unique_id,
          {},
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          let stateData = rootState.search.results.find(
            (item) => item.uniqueID === article.unique_id
          );
          let newData = {
            ...stateData,
            deleted: '1',
          };
          commit('SET_UPDATE_SEARCH_ARTICLE', newData);
          commit('SET_GENERAL_LOADING', false, { root: true });
          resolve(res);
        })
        .catch((error) => {
          console.log(error.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (error.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${error.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          commit('SET_GENERAL_LOADING', false, { root: true });
          reject(error);
        });
    });
  },
  showResource({ commit, rootState }, article) {
    commit('SET_GENERAL_LOADING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .put(
          process.env.VUE_APP_SHOW_ARTICLE + article.unique_id,
          article,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          let stateData = rootState.search.results.find(
            (item) => item.uniqueID === article.unique_id
          );
          let newData = {
            ...stateData,
            deleted: '0',
          };

          commit('SET_UPDATE_SEARCH_ARTICLE', newData);
          commit('SET_GENERAL_LOADING', false, { root: true });
          resolve(res);
        })
        .catch((error) => {
          console.log(error.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (error.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${error.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          commit('SET_GENERAL_LOADING', false, { root: true });
          reject(error);
        });
    });
  },
  toggleComment({ commit, rootState }, resource) {
    commit('SET_GENERAL_LOADING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_TOGGLE_COMMENT,
          {
            enableComment: resource.status,
            contentId: resource.unique_id,
          },
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          commit(
            'SET_UPDATE_SEARCH_ARTICLE_COMMENT_STATUS',
            resource.unique_id
          );
          commit('SET_GENERAL_LOADING', true, { root: true });
          resolve(res);
        })
        .catch((err) => {
          commit('SET_GENERAL_LOADING', true, { root: true });
          reject(err);
        });
    });
  },
  commentToggle({ commit, rootState }, uniqueId) {
    commit('SET_GENERAL_LOADING', true, { root: true });

    let url = process.env.VUE_APP_COMMENT_TOGGLE.replace(
      '{uniqueId}',
      uniqueId
    );

    return new Promise((resolve, reject) => {
      axios
        .post(
          url,
          uniqueId,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          commit('SET_UPDATE_SEARCH_ARTICLE_COMMENT_STATUS', uniqueId);
          commit('SET_GENERAL_LOADING', true, { root: true });
          resolve(res);
        })
        .catch((err) => {
          commit('SET_GENERAL_LOADING', true, { root: true });
          reject(err);
        });
    });
  },
  resetResult({ commit }) {
    commit('SET_CURRENT_RESULTS', []);
  },
};

const mutations = {
  SET_LOADING: (state, val) => (state.loading = val),
  SET_RESULTS: (state, data) => {
    state.results = [];
    state.results = [...data];
  },
  SET_CURRENT_RESULTS: (state, data) => {
    state.results = data;
  },
  SET_UPDATE_SEARCH_ARTICLE: (state, newData) => {
    const results = state.results;
    let idx = state.results.findIndex((re) => re.uniqueID === newData.uniqueID);
    if (idx !== -1) {
      results.splice(idx, 1, newData);
    }
    state.results = results;
  },
  SET_UPDATE_SEARCH_ARTICLE_COMMENT_STATUS: (state, uniqueID) => {
    const results = state.results;
    const item = state.results.find((obj) => obj.uniqueID === uniqueID);
    if (item) {
      item.comments_disabled = item.comments_disabled == '1' ? '0' : '1';
    }
    state.results = results;
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};
