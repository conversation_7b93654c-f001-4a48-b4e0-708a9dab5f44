import axios from 'axios';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const state = {
  contents: [],
  mikrowav_pages: 1,
  mikrowav_current_page: 1,
  next_page_url: '',
  loading: false,
};

const getters = {
  getMikrowavContentLoading: (state) => state.loading,
  getMikrowavContents: (state) => state.contents,
  getMikrowavPages: (state) => state.mikrowav_pages,
  getMikrowavCurrentPage: (state) => state.mikrowav_current_page,
  getMikrowavNextPage: (state) => state.next_page_url,
};

const actions = {
  getMikrowavContent(
    { commit, rootState },
    {
      keyword = null,
      type = null,
      startDate = null,
      endDate = null,
      currentPage = 1,
    } = {}
  ) {
    commit('SET_LOADING', true);
    if (!startDate || !endDate) {
      startDate = new Date();
      endDate = new Date();
      startDate.setDate(startDate.getDate() - 7);
      endDate.setDate(endDate.getDate());
    }

    const formattedStartDate = startDate.toISOString().split('T')[0];
    const formattedEndDate = endDate.toISOString().split('T')[0];

    let req = `${process.env.VUE_APP_GET_MIKROWAV_CONTENT_DASHBOARD}?page=${currentPage}&start_date=${formattedStartDate}&end_date=${formattedEndDate}`;
    if (keyword && type) {
      req += `&${type}=${keyword}`;
    }

    return new Promise((resolve, reject) => {
      axios
        .get(
          req,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_TOKEN_VALUE
          )
        )
        .then((res) => {
          let contents = Array.isArray(res.data) ? res.data : res.data || [];
          commit('SET_LOADING', false);
          commit('SET_CONTENTS', contents);
          commit('SET_MIKROWAV_PAGES', res.data.pagination.total_pages);
          commit('SET_MIKROWAV_CURRENT_PAGE', res.data.pagination.current_page);
          commit('SET_NEXT_PAGE_URL', res.data.pagination.next_page_url);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_LOADING', false);
          reject(err);
          console.log(err);
        });
    });
  },
  exportToExcel(
    { commit, rootState },
    {
      keyword = null,
      type = null,
      startDate = null,
      endDate = null,
      currentPage = 1,
    } = {}
  ) {
    commit('SET_LOADING', true);
    if (!startDate || !endDate) {
      startDate = new Date();
      endDate = new Date();
      startDate.setDate(startDate.getDate() - 7);
      endDate.setDate(endDate.getDate());
    }

    const formattedStartDate = startDate.toISOString().split('T')[0];
    const formattedEndDate = endDate.toISOString().split('T')[0];

    let req = `${process.env.VUE_APP_GET_MIKROWAV_EXCEL_DASHBOARD}?page=${currentPage}&start_date=${formattedStartDate}&end_date=${formattedEndDate}`;
    if (keyword && type) {
      req += `&${type}=${keyword}`;
    }

    return new Promise((resolve, reject) => {
      axios
        .get(req, {
          headers: headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_TOKEN_VALUE
          ),
          responseType: 'blob',
        })
        .then((response) => {
          commit('SET_LOADING', false);

          const url = window.URL.createObjectURL(new Blob([response.data]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', 'mikrowav_data.xlsx');
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          resolve(response);
        })
        .catch((error) => {
          commit('SET_LOADING', false);
          reject(error);
          let message = 'Check console for more info';
          let title = 'Error';

          if (error.response && error.response.status === 401) {
            message = 'Unauthorized. You have no permission to export data';
            title = 'Unauthorized';
          }

          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${error.response ? error.response.status : ''} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });

          console.log('---Error---');
          console.log(error);
        });
    });
  },
  hideMikrowavResource({ commit, rootState }, mikrowav) {
    commit('SET_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .put(
          process.env.VUE_APP_HIDE_ARTICLE + mikrowav.unique_id,
          {},
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          const now = new Date();
          const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

          let newData = {
            ...mikrowav,
            is_flagged: true,
            flagged_at: formattedDate,
          };
          commit('UPDATE_CONTENT', newData);
          commit('SET_LOADING', false);
          resolve(res);
        })
        .catch((error) => {
          let message = 'Check console for more info';
          let title = 'Error';
          if (error.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${error.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          commit('SET_LOADING', false);
          reject(error);
        });
    });
  },
  showMikrowavResource({ commit, rootState }, mikrowav) {
    commit('SET_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .put(
          process.env.VUE_APP_SHOW_ARTICLE + mikrowav.unique_id,
          mikrowav,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          let newData = {
            ...mikrowav,
            is_flagged: false,
            flagged_at: null,
          };
          commit('UPDATE_CONTENT', newData);
          commit('SET_LOADING', false);
          resolve(res);
        })
        .catch((error) => {
          let message = 'Check console for more info';
          let title = 'Error';
          if (error.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${error.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          commit('SET_LOADING', false);
          reject(error);
        });
    });
  },
};

const mutations = {
  SET_LOADING: (state, val) => (state.loading = val),
  SET_CONTENTS: (state, val) => (state.contents = val),
  SET_MIKROWAV_PAGES: (state, page) => (state.mikrowav_pages = page),
  SET_NEXT_PAGE_URL: (state, url) => (state.next_page_url = url),
  SET_MIKROWAV_CURRENT_PAGE: (state, page) =>
    (state.mikrowav_current_page = page),
  UPDATE_CONTENT: (state, updatedItem) => {
    const index = state.contents.data.findIndex(
      (item) => item.unique_id === updatedItem.unique_id
    );
    if (index !== -1) {
      Vue.set(state.contents.data, index, {
        ...state.contents.data[index],
        is_flagged: updatedItem.is_flagged,
        flagged_at: updatedItem.flagged_at,
      });
    }
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};
