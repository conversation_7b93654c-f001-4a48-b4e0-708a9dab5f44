import axios from 'axios';
import * as headers from '@/helpers/headers';

const state = {
  articles: [],
  loading: false,
  pages: 1,
  currentPage: 1,
};

const getters = {
  getMostViewedArticles: (state) => state.articles,
  getMostViewedArticlesLoading: (state) => state.loading,
  getMostViewedArticlesPages: (state) => state.pages,
  getMostViewedArticlesCurrentPage: (state) => state.currentPage,
};

const actions = {
  getMostViewedArticles(
    { commit, rootState },
    { startDate, endDate, uniqueId, language, project, currentPage }
  ) {
    commit('SET_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    let url =
      process.env.VUE_APP_GET_MOST_VIEWED_ARTICLES +
      '?fromDate=' +
      startDate +
      '&toDate=' +
      endDate +
      '&page=' +
      currentPage;
    if (uniqueId != null) {
      url = url + '&uniqueId=' + uniqueId;
    }

    if (language != null) {
      url = url + '&language=' + language;
    }

    if (project != null) {
      url = url + '&projects=' + project;
    }

    return new Promise((resolve, reject) => {
      axios
        .get(
          url + '&r=' + r,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          commit('SET_ARTICLES', res.data.data);
          commit('SET_PAGES', res.data.pagination.total_pages);
          commit('SET_CURRENT_PAGE', res.data.pagination.current_page);
          commit('SET_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          commit('SET_LOADING', false);
          reject(err);
        });
    });
  },
};

const mutations = {
  SET_ARTICLES: (state, val) => (state.articles = val),
  SET_LOADING: (state, val) => (state.loading = val),
  SET_PAGES: (state, val) => (state.pages = val),
  SET_MOST_VIEWED_ARTICLES_CURRENT_PAGE: (state, val) =>
    (state.currentPage = val),
};

export default {
  state,
  getters,
  actions,
  mutations,
};
