import axios from 'axios';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const state = {
  blocked_users: [],
  users_list: [],
  current_page: 1,
  pages: 1,
  next_page_url: '',
  prev_page_url: '',
  user_loading: false,
  ban_loading: false,
  current_ban_history: [],
};

const getters = {
  getBlockedUsers: (state) => state.blocked_users,
  getListOfUsers: (state) => state.users_list,
  getUserLoading: (state) => state.user_loading,
  getPages: (state) => state.pages,
  getCurrentPage: (state) => state.current_page,
  getNextPage: (state) => state.next_page_url,
  getPrevPage: (state) => state.prev_page_url,
  getBanHistory: (state) => state.current_ban_history,
  getBanLoading: (state) => state.ban_loading,
};

const actions = {
  getUserData({ commit, rootState }, page) {
    commit('SET_USER_LOADING', true);
    return new Promise((resolve, reject) => {
      let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
      axios
        .post(
          process.env.VUE_APP_GET_USERS + '?page=' + page.page + '&r=' + r,
          page,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          commit('SET_USER_LIST', res.data.data);
          commit('SET_PAGES', res.data.last_page);
          commit('SET_CURRENT_PAGE', res.data.current_page);
          commit('SET_NEXT_PAGE_URL', res.data.next_page_url);
          commit('SET_PREV_PAGE_URL', res.data.prev_page_url);
          commit('SET_USER_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          console.log(err.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          commit('SET_USER_LOADING', false);
          reject(err);
        });
    });
  },
  listBlockedUsers({ commit, rootState }) {
    commit('SET_USER_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_GET_BANNED_LIST + '?r=' + r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then((res) => {
        commit('SET_BLOCKED_USERS', res.data);
        commit('SET_USER_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        commit('SET_USER_LOADING', false);
      });
  },
  banUser({ commit, rootState }, user) {
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_BAN_USER,
          user,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          commit('SET_UPDATE_USER', res.data.user);
          commit('SET_NEW_BLOCKED_USER', res.data.user);
          resolve(res.data.message);
        })
        .catch((err) => {
          console.log(err.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          reject(err);
        });
    });
  },
  unbanUser({ commit, rootState }, user) {
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_UNBAN_USER,
          user,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          commit('SET_UPDATE_USER', res.data.user[0]);
          commit('SET_REMOVE_FROM_BLOCKED', res.data.user[0]);
          resolve(res.data.message);
        })
        .catch((err) => {
          console.log(err.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          reject(err);
        });
    });
  },
  userBanHistory({ commit, rootState }, id) {
    commit('SET_BAN_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    return new Promise((resolve, reject) => {
      axios
        .get(
          process.env.VUE_APP_BAN_HISTORY + id + '?r=' + r,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          commit('SET_CURRENT_BAN_HISTORY', res.data);
          commit('SET_BAN_LOADING', false);
          resolve(res);
        })
        .catch((err) => {
          console.log(err.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (err.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${err.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          console.log(err);
          reject(err);
          commit('SET_BAN_LOADING', false);
        });
    });
  },
  verifyUser({ commit, rootState }, userId) {
    commit('SET_BAN_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_VERIFY_USER,
          { user_id: userId },
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          commit('SET_UPDATE_USER', res.data.user[0]);
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  unverifyUser({ commit, rootState }, userId) {
    commit('SET_BAN_LOADING', true);
    return new Promise((resolve, reject) => {
      axios
        .post(
          process.env.VUE_APP_UNVERIFY_USER,
          { user_id: userId },
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_ADMIN_TOKEN
          )
        )
        .then((res) => {
          commit('SET_UPDATE_USER', res.data.user[0]);
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  updateUserAction(context, { payload, status }) {
    context.commit('SET_USER_LOADING', true);
    axios
      .post(
        process.env.VUE_APP_COMMENTSERVICE_UPDATE_USER_ACTION,
        payload,
        headers.createHeaders(
          context.rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then(({ data }) => {
        Vue.swal({
          toast: true,
          position: 'top-end',
          showConfirmButton: false,
          showCancelButton: false,
          timer: 5000,
          icon: 'success',
          title: 'Success!',
          text: `User is ${status}!`,
          allowOutsideClick: false,
        });
        context.commit('SET_UPDATE_USER_STATUS', {
          firebaseId: payload.firebaseId,
          ...data.data,
        });
        context.commit('SET_USER_LOADING', false);
      })
      .catch((err) => {
        console.error(err);
        context.commit('SET_USER_LOADING', false);
      });
  },
};

const mutations = {
  SET_BLOCKED_USERS: (state, list) => (state.blocked_users = list),
  SET_USER_LIST: (state, userList) => (state.users_list = userList),
  SET_PAGES: (state, page) => (state.pages = page),
  SET_NEXT_PAGE_URL: (state, url) => (state.next_page_url = url),
  SET_PREV_PAGE_URL: (state, url) => (state.prev_page_url = url),
  SET_CURRENT_PAGE: (state, page) => (state.current_page = page),
  SET_USER_LOADING: (state, val) => (state.user_loading = val),
  SET_NEW_BLOCKED_USER: (state, user) => state.blocked_users.unshift(user),
  SET_UPDATE_USER: (state, user) => {
    const index = state.users_list.findIndex((list) => list.id === user.id);
    if (index !== -1) {
      state.users_list.splice(index, 1, user);
    }
  },
  SET_UPDATE_USER_STATUS: (state, res) => {
    const idx = state.users_list.findIndex(
      (i) => i.loginProviderUID === res.firebaseId
    );
    const find = state.users_list[idx];
    if (res.whitelisted === true) {
      find.isVerified = 'Yes';
    } else {
      find.isVerified = 'No';
    }
    if (res.banned === true) {
      find.isBlocked = 'Blocked';
    } else {
      find.isBlocked = '-';
    }
    if (idx !== -1) {
      state.users_list.splice(idx, 1, find);
    }
  },
  SET_REMOVE_FROM_BLOCKED: (state, user) => {
    state.blocked_users = state.blocked_users.filter(
      (use) => use.user_id !== user.id
    );
  },

  SET_BAN_LOADING: (state, val) => (state.ban_loading = val),
  SET_CURRENT_BAN_HISTORY: (state, list) => (state.current_ban_history = list),
};

export default {
  state,
  getters,
  actions,
  mutations,
};
