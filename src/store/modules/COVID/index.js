import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const state = {
  new_loading: false,
  track_loading: false,
  old_loading: false,
  new_api_state: '',
  new_api_process: {},
  old_api_state: '',
  old_api_process: {},
  track_api_state: '',
  track_api_process: {},
  covid_images_history: {},
};

const getters = {
  getNewAPIState: (state) => state.new_api_state,
  getNewAPIData: (state) => state.new_api_process,
  getOldAPIState: (state) => state.old_api_state,
  getOldAPIData: (state) => state.old_api_process,
  getTrackAPIState: (state) => state.track_api_state,
  getTrackAPIData: (state) => state.track_api_process,

  getNewAPILoading: (state) => state.new_loading,
  getTrackAPILoading: (state) => state.track_loading,
  getOldAPILoading: (state) => state.old_loading,
};

const actions = {
  newAPI({ commit, rootState }) {
    commit('SET_NEW_API_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_GET_NEW_API_PROCESS + '?r=' + r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        commit('SET_NEW_API_DATA', res.data);
        commit('SET_NEW_API_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        Bugsnag.notify(err);
        commit('SET_NEW_API_LOADING', false);
      });
  },
  trackAPI({ commit, rootState }) {
    commit('SET_TRACK_API_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_GET_TRACK_API_PROCESS + '?r=' + r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        commit('SET_TRACK_API_DATA', res.data);
        commit('SET_TRACK_API_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        Bugsnag.notify(err);
        commit('SET_TRACK_API_LOADING', false);
      });
  },
  oldAPI({ commit, rootState }) {
    commit('SET_OLD_API_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_GET_OLD_API_PROCESS + '?r=' + r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        commit('SET_OLD_API_DATA', res.data);
        commit('SET_OLD_API_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        Bugsnag.notify(err);
        commit('SET_OLD_API_LOADING', false);
      });
  },
  scaleNewAPI({ commit, rootState }, new_api) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_SCALE_NEW_API + new_api + '?r=' + r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        let resp = res.data;
        if (resp[0] instanceof String || typeof resp[0] === 'string') {
          commit('SET_NEW_API_STATE', 'fail');
        } else {
          commit('SET_NEW_API_STATE', 'pass');
        }
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        Bugsnag.notify(err);
      });
  },
  scaleTrackAPI({ commit, rootState }, track_api) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_SCALE_TRACK_API + track_api + '?r=' + r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        let resp = res.data;
        if (resp[0] instanceof String || typeof resp[0] === 'string') {
          commit('SET_TRACK_API_STATE', 'fail');
        } else {
          commit('SET_TRACK_API_STATE', 'pass');
        }
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        Bugsnag.notify(err);
      });
  },
  scaleOldAPI({ commit, rootState }, old_api) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    axios
      .get(
        process.env.VUE_APP_SCALE_OLD_API + old_api + '?r=' + r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        let resp = res.data;
        if (resp[0] instanceof String || typeof resp[0] === 'string') {
          commit('SET_OLD_API_STATE', 'fail');
        } else {
          commit('SET_OLD_API_STATE', 'pass');
        }
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        Bugsnag.notify(err);
      });
  },
  downloadData({ rootState }, { region, language }) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    var url =
      region == 'msia'
        ? process.env.VUE_APP_GET_MALAYSIA_DATA
        : process.env.VUE_APP_GET_GLOBAL_DATA;
    axios
      .get(
        url + language + '?r=' + r,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        var fileURL = window.URL.createObjectURL(new Blob([res.data]));
        var fileLink = document.createElement('a');
        fileLink.href = fileURL;
        fileLink.setAttribute('download', region + language + '.csv');
        document.body.appendChild(fileLink);
        fileLink.click();
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${err.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log(err);
        Bugsnag.notify(err);
      });
  },
  async getCovidImagesHistory({ commit, rootState, state }) {
    if (!state.covid_images_history_loading) {
      commit('SET_COVID_IMAGES_HISTORY_LOADING', true);
      try {
        let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
        const res = await axios.get(
          `${process.env.VUE_APP_GET_COVID_ARTICLE_HISTORY}?type=image&r=${r}`,
          headers.createHeaders(rootState.user.token)
        );
        commit('SET_COVID_IMAGES_HISTORY', res.data);
      } catch (error) {
        alert('Something went wrong');
        console.log(error);
        Bugsnag.notify(error);
      }
      commit('SET_COVID_IMAGES_HISTORY_LOADING', false);
    }
  },
};

const mutations = {
  SET_NEW_API_LOADING: (state, val) => (state.new_loading = val),
  SET_TRACK_API_LOADING: (state, val) => (state.track_loading = val),
  SET_OLD_API_LOADING: (state, val) => (state.old_loading = val),
  SET_NEW_API_DATA: (state, data) => (state.new_api_process = data),
  SET_NEW_API_STATE: (state, val) => (state.new_api_state = val),
  SET_TRACK_API_DATA: (state, data) => (state.track_api_process = data),
  SET_TRACK_API_STATE: (state, val) => (state.track_api_state = val),
  SET_OLD_API_DATA: (state, data) => (state.old_api_process = data),
  SET_OLD_API_STATE: (state, val) => (state.old_api_state = val),
  SET_COVID_IMAGES_HISTORY_LOADING: (state, val) =>
    (state.covid_images_history_loading = val),
  SET_COVID_IMAGES_HISTORY: (state, val) => (state.covid_images_history = val),
};

export default {
  state,
  getters,
  actions,
  mutations,
};
