import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const state = {
  advertiser_list: [],
  advertiserLoading: false,
};

const getters = {
  allAdvertisers: (state) => state.advertiser_list,
  getAdvertiserLoading: (state) => state.advertiserLoading,
  getAdvertiser: (state) => (id) => {
    return state.advertiser_list.find((adv) => adv.id === id);
  },
};

const actions = {
  getAllAdvertisers({ commit, rootState }) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    commit('SET_ADVERTISER_LOADING', true);
    axios
      .get(
        process.env.VUE_APP_GET_ADVERTISER_ENDPOINT + '?r=' + r,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        commit('SET_ADVERTISERS', res.data);
        commit('SET_ADVERTISER_LOADING', false);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log('---Error---');
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  addAdvertiser({ commit, rootState }, adv) {
    return new Promise((resolve, reject) => {
      commit('SET_GENERAL_LOADING', true, { root: true });
      axios
        .post(
          process.env.VUE_APP_ADD_ADVERTISER_ENDPOINT,
          adv,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          commit('SET_NEW_ADVERTISER', res.data.object);
          commit('SET_GENERAL_LOADING', false, { root: true });
          resolve(res);
        })
        .catch((error) => {
          console.log(error.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (error.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${error.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          commit('SET_GENERAL_LOADING', false, { root: true });
          console.log('----Error----');
          console.log(error);
          Bugsnag.notify(error);
          reject(error);
        });
    });
  },
  alterAdvertiser({ commit, rootState }, adv) {
    return new Promise((resolve, reject) => {
      commit('SET_GENERAL_LOADING', true, { root: true });
      axios
        .put(
          process.env.VUE_APP_EDIT_ADVERTISER_ENDPOINT + adv.id,
          adv,
          headers.createHeaders(rootState.user.token)
        )
        .then((res) => {
          commit('SET_GENERAL_LOADING', false, { root: true });
          commit('SET_UPDATED_ADVERTISER', res.data.object);
          resolve(res);
        })
        .catch((error) => {
          console.log(error.response.data);
          let message = 'Check console for more info';
          let title = 'Error';
          if (error.response.status == 401) {
            message = 'Unauthorized. Check console for more info';
            title = 'Unauthorized';
          }
          Vue.notify({
            group: 'error',
            title: title,
            type: 'error',
            text: `${error.response.status} : ${message}`,
            duration: -1,
            position: 'bottom right',
            closeOnClick: true,
          });
          commit('SET_GENERAL_LOADING', false, { root: true });
          console.log('----Error----');
          console.log(error);
          Bugsnag.notify(error);
          reject(error);
        });
    });
  },
};

const mutations = {
  SET_ADVERTISER_LOADING: (state, value) => (state.advertiserLoading = value),
  SET_ADVERTISERS: (state, advs) => (state.advertiser_list = advs),
  SET_NEW_ADVERTISER: (state, newAdv) => state.advertiser_list.unshift(newAdv),
  SET_UPDATED_ADVERTISER: (state, updAdv) => {
    const index = state.advertiser_list.findIndex(
      (adv) => adv.id === updAdv.id
    );
    if (index !== -1) {
      state.advertiser_list.splice(index, 1, updAdv);
    }
  },
  SET_DELETED_ADVERTISER: (state, id) =>
    state.advertiser_list.filter((adv) => adv.id !== id),
};

export default {
  state,
  actions,
  getters,
  mutations,
};
