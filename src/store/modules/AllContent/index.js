import axios from 'axios';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const state = {
  publishers: [],
  topics: [],
  contents: [],
  loading: false,
  articleTranslations: [],
};

const getters = {
  getContentPublishers: (state) => state.publishers,
  getContentTopics: (state) => state.topics,
  getContentLoading: (state) => state.loading,
  getContents: (state) => state.contents,
  getArticleTranslations: (state) => state.articleTranslations,
};

const actions = {
  getContentFilters({ commit, rootState }, { hours, type_array, native }) {
    commit('SET_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    let req =
      process.env.VUE_APP_GET_CONTENT_FILTER + '?hours=' + hours + '&r=' + r;
    if (type_array.length > 0) {
      let type = type_array.join();
      req = req + '&type_array=' + type;
    }
    if (native !== 'all') {
      req += '&native=' + native;
    }
    return new Promise((resolve, reject) => {
      axios
        .get(
          req,
          headers.createHeaders(
            rootState.user.token,
            process.env.VUE_APP_TOKEN_VALUE
          )
        )
        .then((res) => {
          commit('SET_PUBLISHERS', res.data.publishers);
          commit('SET_TOPICS', res.data.topics);
          commit('SET_LOADING', false);
          resolve();
        })
        .catch(() => {
          commit('SET_LOADING', false);
          reject();
        });
    });
  },
  getContent(
    { commit, rootState },
    { hours, publishers_array, topics_array, type_array, lang_array, native }
  ) {
    commit('SET_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    let req =
      process.env.VUE_APP_GET_CONTENT_DASHBOARD + '?hours=' + hours + '&r=' + r;
    if (publishers_array.length > 0) {
      let p = publishers_array.join();
      req = req + '&publishers_array=' + p;
    }
    if (topics_array.length > 0) {
      let t = topics_array.join();
      req = req + '&topics_array=' + t;
    }
    if (type_array.length > 0) {
      let type = type_array.join();
      req = req + '&type_array=' + type;
    }
    if (lang_array.length > 0) {
      let lang = lang_array.join();
      req = req + '&lang_array=' + lang;
    }
    if (native !== 'all') {
      req += '&native=' + native;
    }
    axios
      .get(
        req,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_TOKEN_VALUE
        )
      )
      .then((res) => {
        commit('SET_LOADING', false);
        commit('SET_CONTENTS', res.data);
      })
      .catch(() => {
        commit('SET_LOADING', false);
      });
  },
  translate({ commit, rootState }, item) {
    commit('SET_LOADING', true);
    const url = process.env.VUE_APP_TRANSLATE_ARTICLE.replace(
      '{id}',
      item.unique_id
    );
    const headersConfig = headers.createHeaders(
      rootState.user.token,
      process.env.VUE_APP_TOKEN_VALUE
    );

    // Create an array of API requests for each language
    const requests = item.languages.map((language) => {
      const requestBody = { language }; // Adjust the request body as needed
      return axios.post(url, requestBody, headersConfig);
    });

    let translations = [];
    return Promise.all(requests)
      .then((responses) => {
        responses.forEach((response) => {
          translations = translations.concat(response.data);
        });
      })
      .catch((error) => {
        commit('SET_LOADING', false);

        let errorMessage = 'An unknown error occurred. Please try again.';

        // Display error reason
        switch (error.response.status) {
          case 412:
            errorMessage =
              'RECITATION_ERROR. Please contact tech team to resolve this issue.';
            break;
          case 403:
            errorMessage = 'Non-native article selected.';
            break;
          case 400:
            errorMessage =
              'Bad Request: The translation api returned an error.';
            break;
          case 404:
            errorMessage = 'Not Found: Article not found in database.';
            break;
        }

        Vue.swal.fire({
          title: 'Error',
          text: errorMessage,
          icon: 'error',
          confirmButtonText: 'Close',
        });
      })
      .finally(() => {
        commit('SET_ARTICLE_TRANSLATIONS', translations);
        commit('SET_LOADING', false);
        return translations;
      });
  },
};

const mutations = {
  SET_LOADING: (state, val) => (state.loading = val),
  SET_PUBLISHERS: (state, val) => (state.publishers = val),
  SET_TOPICS: (state, val) => (state.topics = val),
  SET_CONTENTS: (state, val) => (state.contents = val),
  SET_ARTICLE_TRANSLATIONS: (state, val) => (state.articleTranslations = val),
};

export default {
  state,
  getters,
  actions,
  mutations,
};
