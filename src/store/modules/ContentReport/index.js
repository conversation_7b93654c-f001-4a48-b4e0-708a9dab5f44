import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const state = {
  reports: [],
  pagination: {},
  loading: false,
  random: 0,
};

const getters = {
  getContentReports: (state) => state.reports,
  getContentReportsLoading: (state) => state.loading,
  getContentPagination: (state) => state.pagination,
  getContentRandom: (state) => state.random,
};

const actions = {
  getContentReports(
    { commit, rootState },
    {
      startDate,
      endDate,
      id = null,
      title = null,
      sortBy = null,
      asc = null,
      currentPage,
    }
  ) {
    commit('SET_LOADING', true);
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    let page = currentPage ? currentPage : 1;
    let url =
      process.env.VUE_APP_GET_REPORT_CONTENT +
      '?startDate=' +
      startDate +
      '&endDate=' +
      endDate +
      '&page=' +
      page;

    if (id) {
      url = url + '&id=' + id;
    }

    if (title) {
      url = url + '&title=' + title;
    }

    if (sortBy != null && asc != null) {
      url = url + '&sortBy=' + sortBy + '&sortDirection=' + asc;
    }

    axios
      .get(
        url + '&r=' + r,
        headers.createHeaders(rootState.user.token, 'abdulNewswav')
      )
      .then((res) => {
        const reportsData = Object.values(res.data.data);

        reportsData.forEach((a) => {
          let r = '';
          let item;
          for (item of a.reason_id) {
            r = r + item.reason_en + ',';
          }
          a.report_reason = r.slice(0, -1);
        });
        commit('SET_REPORTS', reportsData);
        commit('SET_CONTENT_PAGINATION', res.data.pagination);
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err);
        //console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.swal({
          toast: true,
          position: 'top-end',
          showConfirmButton: true,
          showCancelButton: false,
          timer: 5000,
          icon: 'error',
          title: title,
          text: `${err.response.status} : ${message}`,
        });
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
  reportContentAction(
    { commit, rootState },
    { data, status, adwav_user, dateRange }
  ) {
    commit('SET_LOADING', true);
    let action_data = {
      entity_id: data.entity_id,
      entity_type: data.entity_type,
      review_status: status,
      reviewed_by: adwav_user,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    };
    axios
      .put(
        process.env.VUE_APP_REPORT_CONTENT_ACTION,
        action_data,
        headers.createHeaders(
          rootState.user.token,
          process.env.VUE_APP_ADMIN_TOKEN
        )
      )
      .then((res) => {
        if (res.data.status == 'success') {
          commit('SET_UPDATED_REPORTED_CONTENT', res.data.data);
          let random = +new Date();
          commit('SET_RANDOM', random);
          Vue.swal({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            showCancelButton: false,
            timer: 5000,
            icon: 'success',
            title: 'Success',
            text: `${status} content ${action_data.entity_id} successful`,
          });
        } else {
          console.log(res.data.message);
          let message = 'Check console for more info';
          let title = 'Error';
          Vue.swal({
            toast: true,
            position: 'top-end',
            showConfirmButton: true,
            showCancelButton: false,
            timer: 5000,
            icon: 'error',
            title: title,
            text: `Action Failed : ${message}`,
          });
        }
        commit('SET_LOADING', false);
      })
      .catch((err) => {
        console.log(err.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (err.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.swal({
          toast: true,
          position: 'top-end',
          showConfirmButton: true,
          showCancelButton: false,
          timer: 5000,
          icon: 'error',
          title: title,
          text: `${err.response.status} : ${message}`,
        });
        console.log(err);
        commit('SET_LOADING', false);
        Bugsnag.notify(err);
      });
  },
};

const mutations = {
  SET_LOADING: (state, val) => (state.loading = val),
  SET_REPORTS: (state, data) => (state.reports = data),
  SET_CONTENT_PAGINATION: (state, data) => (state.pagination = data),
  SET_RANDOM: (state, val) => (state.random = val),
  SET_UPDATED_REPORTED_CONTENT: (state, updatedReport) => {
    const index = state.reports.findIndex(
      (report) => report.entity_id === updatedReport.entity_id
    );
    if (index !== -1) {
      state.reports.splice(index, 1, updatedReport);
    }
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};
