import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import * as headers from '@/helpers/headers';
import Vue from 'vue';

const state = {
  ads: [],
  adsLoading: false,
};

const getters = {
  allAds: (state) => state.ads,
  getAdsLoading: (state) => state.adsLoading,
};

const actions = {
  getAllAds({ commit, rootState }, id) {
    let r = Math.floor(Math.random() * (1000 - 1 + 1)) + 1;
    commit('SET_ADS_LOADING', true);
    axios
      .get(
        process.env.VUE_APP_GET_ADVERTISEMENT_BY_ADVERTISER + id + '?r=' + r,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        commit('SET_ADS', res.data);
        commit('SET_ADS_LOADING', false);
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        commit('SET_ADS_LOADING', false);
        console.log('---Error---');
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  updateAdvertisement({ commit, rootState }, ads) {
    commit('SET_GENERAL_LOADING', true, { root: true });
    return axios
      .put(
        process.env.VUE_APP_EDIT_ADVERTISEMENT_ENDPOINT + ads.id,
        ads,
        headers.createHeaders(rootState.user.token)
      )
      .then((res) => {
        commit('SET_UPDATE_ADS', res.data.ads);
        commit('SET_UPDATE_ADS_CAMPAIGN', res.data.ads, { root: true });
        commit('SET_GENERAL_LOADING', false, { root: true });
        Vue.swal.fire({
          icon: 'success',
          text: 'Advertisement successfully updated!',
          showCancelButton: false,
        });
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        commit('SET_GENERAL_LOADING', false, { root: true });
        console.log('----Error----');
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  reuseAdvertisement({ commit, rootState }, post) {
    commit('SET_GENERAL_LOADING', true, { root: true });
    axios
      .post(
        process.env.VUE_APP_REUSE_ADVERTISEMENT_ENDPOINT,
        {
          ad_id: post.advert_id,
          campaign_ids: post.campaign_ids,
        },
        headers.createHeaders(rootState.user.token)
      )
      .then(() => {
        Vue.swal.fire({
          icon: 'success',
          text: 'Advertisement linked successfully to selected campaign(s)!',
          showCancelButton: false,
        });
        commit('SET_GENERAL_LOADING', false, { root: true });
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        commit('SET_GENERAL_LOADING', false, { root: true });
        console.log(error);
        Bugsnag.notify(error);
      });
  },
  removeAdvertisement({ commit, rootState }, id) {
    axios
      .delete(
        process.env.VUE_APP_DELETE_ADVERTISEMENT_ENDPOINT + id,
        headers.createHeaders(rootState.user.token)
      )
      .then(() => {
        commit('SET_DELETE_ADS', id);
        commit('SET_DELETE_UNLINK_AD', id, { root: true });
        Vue.swal.fire({
          icon: 'success',
          text: 'Advertisement successfully deleted!',
          showCancelButton: false,
        });
      })
      .catch((error) => {
        console.log(error.response.data);
        let message = 'Check console for more info';
        let title = 'Error';
        if (error.response.status == 401) {
          message = 'Unauthorized. Check console for more info';
          title = 'Unauthorized';
        }
        Vue.notify({
          group: 'error',
          title: title,
          type: 'error',
          text: `${error.response.status} : ${message}`,
          duration: -1,
          position: 'bottom right',
          closeOnClick: true,
        });
        console.log('---Error---');
        console.log(error);
        Bugsnag.notify(error);
      });
  },
};

const mutations = {
  SET_ADS_LOADING: (state, value) => (state.adsLoading = value),
  SET_ADS: (state, ads) => (state.ads = ads),
  SET_UPDATE_ADS: (state, updAds) => {
    updAds.forEach((updatedAd) => {
      const index = state.ads.findIndex((ad) => ad.id === updatedAd.id);

      if (index != -1) {
        state.ads.splice(index, 1, updatedAd);
      }
    });
  },
  SET_DELETE_ADS: (state, id) =>
    (state.ads = state.ads.filter((ad) => ad.id !== id)),
};

export default {
  state,
  actions,
  getters,
  mutations,
};
