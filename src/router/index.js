import Vue from 'vue';
import Router from 'vue-router';
import store from '../store';
import MostViewedArticlesDashboard from '../components/MostViewedArticles/MostViewedArticlesDashboard.vue';
const MainLayout = () => import('@/containers/MainLayout');

const HomeDashboard = () => import('@/components/HomeDashboard');

const ListAdvertiser = () => import('@/components/Advertisers/ListAdvertisers');
const ShowAdvertiser = () => import('@/components/Advertisers/ShowAdvertiser');
const ShowCampaign = () => import('@/components/Campaigns/ShowCampaign');

const AdwavFormWizard = () =>
  import('@/components/FormWizard/AdWav/AdwavFormWizard');

const PollWavFormWizard = () =>
  import('@/components/FormWizard/PollWav/PollFormWizard');
const ListPolls = () => import('@/components/Polls/ListPolls');
const ShowPoll = () => import('@/components/Polls/ShowPoll');

const ListNewsletter = () => import('@/components/Newsletter/ListNewsletter');
const NewswletterFormWizard = () =>
  import('@/components/FormWizard/Newsletter/StartFormWizard');
const ShowNewsletter = () => import('@/components/Newsletter/ShowNewsletter');
const AllContent = () => import('@/components/AllContent/AllContentDashboard');
const AllMikrowav = () =>
  import('@/components/AllMikrowav/AllMikrowavDashboard');
const SearchArticle = () => import('@/components/Search/SearchArticle');

const ListVideo = () => import('@/components/YoutubeVideos/ListVideos');
const AddVideo = () =>
  import('@/components/FormWizard/YoutubeVideos/AddVideoForm');

const ReportComment = () => import('@/components/CommentsReport/ViewReport');

const AIReportComment = () =>
  import('@/components/CommentsReport/ViewAIReport');

const AllComments = () => import('@/components/CommentsReport/ViewComments');

const ViewKeywords = () => import('@/components/CommentsReport/ViewKeywords');

const Election = () => import('@/components/Election/Election');

// const ListWavMakers = () => import('@/components/WavMakers/ListWavMakers')

// const SabahArticle = () => import('@/components/SabahArticles/SabahArticle')
// const SabahNewsFeed = () => import('@/components/SabahArticles/SabahNewsFeed')
const CPM = () => import('@/components/CPM/ListCPM');
const Covid = () => import('@/components/COVID/COVIDForm');
const Login = () => import('@/components/User/Login');
const Profile = () => import('@/components/User/Profile');
const ForgotPassword = () => import('@/components/User/ForgotPassword');
const ResetPassword = () => import('@/components/User/ResetPassword');

// const Banned = () => import('@/components/Comments/ListBanned')
const PNStop = () => import('@/components/PushNotification/PNStop');
const PNScalePods = () => import('@/components/PushNotification/PNScalePods');
// const PNPodcast = () => import('@/components/PushNotification/PNPodcast')
// const PNVideo = () => import('@/components/PushNotification/PNVideo')
const PNTest = () => import('@/components/PushNotification/PNTest');
const PN = () => import('@/components/PushNotification/PN');
const PN2 = () => import('@/components/PushNotification/PN2');
const PNScheduled = () => import('@/components/PushNotification/PNSchedule');
const EditPNScheduled = () =>
  import('@/components/PushNotification/EditScheduledPN');
const UpsertAiPnConfiguration = () =>
  import('@/components/PushNotification/UpsertAiPnConfiguration');

// const Blocked = () => import('@/components/BlockUser/ListBlockUser')
const Users = () => import('@/components/BlockUser/Users');
const PinPage = () => import('@/components/Pin/PinPage');

const Publishers = () => import('@/components/Publishers/ListPublishers');
const ShowPublisher = () => import('@/components/Publishers/ShowPublisher');
const CreatePublisher = () =>
  import('@/components/FormWizard/Publisher/PublisherFormWizard');
const EditPublisher = () => import('@/components/Publishers/EditPublisher');

const ListLeadForm = () => import('@/components/LeadForm/ListLeadForm');
const CreateLeadForm = () => import('@/components/LeadForm/CreateLeadForm');
const Revenue = () => import('@/components/Revenue/ListRevenue');
const Settings = () => import('@/components/views/Settings');

const PreviewLink = () => import('@/components/AdwavPreview/PreviewLink');

const ReportContent = () => import('@/components/ContentsReport/ViewReport');
const ListAnnouncements = () =>
  import('@/components/Announcements/ListAnnouncements');
const CreateAnnouncements = () =>
  import('@/components/Announcements/CreateAnnouncements');

const ListHightlights = () => import('@/components/Highlights/Highlights');
const CreateHighlights = () => import('@/components/Highlights/HighlightsForm');
const HighlightContents = () =>
  import('@/components/Highlights/HighlightsContent');

const LiveReportedComment = () =>
  import('@/components/LiveComments/LiveReportedComment');
const LiveBannedUsers = () =>
  import('@/components/LiveComments/LiveBannedUsers');
const LiveSession = () => import('@/components/LiveComments/LiveSession');
const OngoingLiveSession = () =>
  import('@/components/LiveComments/OngoingLiveSession');
const LiveSessionForm = () =>
  import('@/components/LiveComments/LiveSessionForm');

Vue.use(Router);

export const router = new Router({
  mode: 'history',
  linkActiveClass: 'active',
  scrollBehavior: () => ({ y: 0 }),
  routes: configRoutes(),
});

function configRoutes() {
  return [
    {
      path: '/login',
      component: Login,
    },
    {
      path: '/forgot-password',
      component: ForgotPassword,
    },
    {
      path: '/reset-password/:token/:email',
      component: ResetPassword,
    },
    {
      path: '/',
      redirect: '/dashboard',
      name: 'Home',
      meta: {
        requiresAuth: true,
      },
      component: MainLayout,
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          meta: {
            requiresAuth: true,
          },
          component: HomeDashboard,
        },
        {
          path: 'advertisers',
          meta: {
            label: 'Adwav',
            requiresAuth: true,
            roles: ['bd', 'admin'],
            actions: ['advertiser_list'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: ListAdvertiser,
              meta: {
                label: 'Advertisers',
                roles: ['bd', 'admin', 'investor'],
                actions: ['advertiser_list'],
              },
            },
            {
              path: ':adv_id',
              meta: {
                label: 'Advertiser Details',
                requiresAuth: true,
                roles: ['bd', 'admin', 'investor'],
              },
              name: 'Show Advertiser',
              component: ShowAdvertiser,
            },
            {
              path: ':adv_id/show-campaign/:c_id',
              meta: {
                label: 'Campaign Details',
                requiresAuth: true,
                roles: ['bd', 'admin', 'investor'],
              },
              component: ShowCampaign,
            },
            {
              path: 'create-campaign-ads/:adv_id',
              meta: {
                label: 'Create Campaign & Ads',
                requiresAuth: true,
                roles: ['bd', 'admin', 'investor'],
              },
              name: 'Create Form Wizard',
              component: AdwavFormWizard,
            },
          ],
        },
        {
          path: 'lead-forms',
          meta: {
            label: 'Lead Forms',
            requiresAuth: true,
            roles: ['bd', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: ListLeadForm,
              meta: {
                roles: ['bd', 'admin'],
                actions: ['lead_list'],
              },
            },
            {
              path: 'create-lead-form/:id?',
              label: 'Create Lead Form',
              name: 'Create Lead Form',
              component: CreateLeadForm,
              meta: {
                requiresAuth: true,
                roles: ['bd', 'admin'],
              },
            },
          ],
        },
        {
          path: 'preview-link',
          meta: {
            label: 'Preview Link',
            requiresAuth: true,
            roles: ['bd', 'admin'],
            actions: ['preview_link'],
          },
          component: PreviewLink,
        },
        {
          path: 'polls',
          meta: {
            label: 'Polls',
            requiresAuth: true,
            roles: ['bd', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: ListPolls,
              meta: {
                roles: ['bd', 'admin'],
                actions: ['poll_list'],
              },
            },
            {
              path: 'create-polls',
              name: 'Create Polls',
              meta: {
                label: 'Create Polls',
                requiresAuth: true,
                roles: ['bd', 'admin'],
              },
              component: PollWavFormWizard,
            },
            {
              path: 'show-poll/:poll_id',
              meta: {
                label: 'Poll Details',
                requiresAuth: true,
                roles: ['bd', 'admin'],
              },
              component: ShowPoll,
            },
          ],
        },
        {
          path: 'newsletter',
          meta: {
            label: 'Newsletter',
            requiresAuth: true,
            roles: ['admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: ListNewsletter,
              meta: {
                roles: ['admin'],
                actions: ['newsletter_list'],
              },
            },
            {
              path: 'create-newsletter',
              name: 'Create Newsletter',
              meta: {
                label: 'Create Newsletter',
                requiresAuth: true,
                roles: ['admin'],
              },
              component: NewswletterFormWizard,
            },
            {
              path: 'show-newsletter/:newsletter_id',
              meta: {
                label: 'Newsletter Details',
                requiresAuth: true,
                roles: ['admin'],
              },
              component: ShowNewsletter,
            },
          ],
        },
        {
          path: 'most-viewed-articles',
          meta: {
            label: 'Most Viewed Articles',
            requiresAuth: true,
            roles: ['admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: MostViewedArticlesDashboard,
              meta: {
                roles: ['admin'],
                // actions: ["most_viewed_articles_list"],
              },
            },
          ],
        },
        {
          path: 'all-content',
          meta: {
            label: 'All Content',
            requiresAuth: true,
            roles: ['cs', 'bd', 'pn', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: AllContent,
              meta: {
                roles: ['cs', 'bd', 'pn', 'admin'],
                actions: ['all_content_list'],
              },
            },
          ],
        },
        {
          path: 'all-mikrowav',
          meta: {
            label: 'All Mikrowav',
            requiresAuth: true,
            roles: ['cs', 'bd', 'pn', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: AllMikrowav,
              meta: {
                roles: ['cs', 'bd', 'pn', 'admin'],
                // actions: ["all_mikrowav_list"],
              },
            },
          ],
        },
        {
          path: 'search',
          meta: {
            label: 'Search',
            requiresAuth: true,
            roles: ['cs', 'bd', 'pn', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: SearchArticle,
              meta: {
                roles: ['cs', 'bd', 'pn', 'admin'],
                actions: ['search_list'],
              },
            },
          ],
        },
        {
          path: 'report-content',
          meta: {
            label: 'Reported Content',
            requiresAuth: true,
            roles: ['cs', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: ReportContent,
              meta: {
                roles: ['cs', 'admin'],
                // actions:['report_comment_list'],
              },
            },
          ],
        },
        {
          path: 'report-comment',
          meta: {
            label: 'Reported Comments',
            requiresAuth: true,
            roles: ['cs', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: ReportComment,
              meta: {
                roles: ['cs', 'admin'],
                actions: ['report_comment_list'],
              },
            },
          ],
        },
        {
          path: 'ai-report-comment',
          meta: {
            label: 'AI Reported Comments',
            requiresAuth: true,
            roles: ['cs', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: AIReportComment,
              meta: {
                roles: ['cs', 'admin'],
                actions: ['report_comment_list'],
              },
            },
          ],
        },
        {
          path: 'ytvideo',
          meta: {
            label: 'Youtube Videos',
            requiresAuth: true,
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: ListVideo,
              meta: {
                requiresAuth: true,
              },
            },
            {
              path: 'add-video/:id?',
              name: 'Add Video',
              component: AddVideo,
            },
          ],
        },
        {
          path: 'comments',
          meta: {
            label: 'All Comments',
            requiresAuth: true,
            roles: ['cs', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: AllComments,
              meta: {
                roles: ['cs', 'admin'],
                actions: ['all_comment_list'],
              },
            },
          ],
        },
        {
          path: 'report-keywords',
          meta: {
            label: 'Auto Report Keywords',
            requiresAuth: true,
            roles: ['cs', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: ViewKeywords,
              meta: {
                roles: ['cs', 'admin'],
                actions: ['keyword_list'],
              },
            },
          ],
        },
        {
          path: 'reported-live-comment',
          meta: {
            label: 'Live Comment',
            actions: ['view_live_reported_comments'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: LiveReportedComment,
            },
          ],
        },
        {
          path: 'live-session',
          meta: {
            label: 'Live Session',
            actions: ['view_live_sessions'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: LiveSession,
            },
            {
              path: 'create',
              name: 'Create Live Session',
              component: LiveSessionForm,
            },
          ],
        },
        {
          path: 'ongoing-live-session',
          meta: {
            label: 'Ongoing Live Session',
            actions: ['view_ongoing_live_sessions'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: OngoingLiveSession,
            },
          ],
        },
        {
          path: 'live-banned-users',
          meta: {
            label: 'Live Banned Users',
            actions: ['view_live_banned_users'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: LiveBannedUsers,
            },
          ],
        },
        {
          path: 'covid',
          meta: {
            label: 'COVID-19',
            requiresAuth: true,
            roles: ['covid', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: Covid,
              meta: {
                roles: ['covid', 'admin'],
                actions: ['covid_19'],
              },
            },
          ],
        },
        {
          path: 'pn-dashboard',
          meta: {
            label: 'Push Notifications',
            requiresAuth: true,
            actions: ['pn_article'],
            roles: ['pn', 'admin'],
          },
          component: PN,
        },
        {
          path: 'pn-dashboard-2',
          meta: {
            label: 'Push Notifications 2',
            requiresAuth: true,
            actions: ['pn_article'],
            roles: ['pn', 'admin'],
          },
          component: PN2,
        },
        {
          path: 'ai-pn-configuration/upsert',
          meta: {
            label: 'Create AI PN Configuration',
            requiresAuth: true,
            actions: ['pn_article'],
            roles: ['pn', 'admin'],
          },
          component: UpsertAiPnConfiguration,
        },
        {
          path: 'ai-pn-configuration/upsert/:id',
          meta: {
            label: 'Edit AI PN Configuration',
            requiresAuth: true,
            actions: ['pn_article'],
            roles: ['pn', 'admin'],
          },
          component: UpsertAiPnConfiguration,
          props: true,
        },
        {
          path: 'pn-test',
          meta: {
            label: 'Test Push Notifications',
            requiresAuth: true,
            roles: ['pn', 'admin', 'developer'],
            actions: ['pn_test'],
          },
          component: PNTest,
        },
        // {
        //     path : 'pn-video',
        //     meta: {
        //         label : 'Video Push Notifications',
        //         requiresAuth : true,
        //         roles:['pn', 'admin'],
        //         actions:['pn_video'],
        //     },
        //     component : PNVideo
        // },
        // {
        //     path : 'pn-podcast',
        //     component : PNPodcast,
        //     meta: {
        //         label : 'Podcast Push Notifications',
        //         requiresAuth: true,
        //         roles:['pn', 'admin'],
        //         actions:['pn_podcast'],
        //     }
        // },
        {
          path: 'pn-scheduled',
          component: {
            render(c) {
              return c('router-view');
            },
          },
          meta: {
            label: 'Scheduled PN',
            requiresAuth: true,
            roles: ['pn', 'admin'],
          },
          children: [
            {
              path: '',
              component: PNScheduled,
              meta: {
                roles: ['pn', 'admin'],
                actions: ['pn_schedule'],
              },
            },
            {
              path: 'edit-pn/:id',
              name: 'Edit Scheduled PN',
              label: 'Edit Scheduled PN',
              component: EditPNScheduled,
              meta: {
                roles: ['pn', 'admin'],
              },
            },
          ],
        },
        {
          path: 'pn-stop',
          component: PNStop,
          meta: {
            label: 'Cancel/Delete PN',
            roles: ['pn', 'admin'],
            actions: ['pn_delete', 'pn_cancel'],
          },
        },
        {
          path: 'scale-pods',
          component: PNScalePods,
          meta: {
            label: 'Scale Pods',
            roles: ['pn', 'admin'],
          },
        },
        {
          path: 'users',
          meta: {
            label: 'User List',
            requiresAuth: true,
            roles: ['cs', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: Users,
              meta: {
                roles: ['cs', 'admin'],
                actions: ['user_list'],
              },
            },
            // {
            //     path : 'blocked',
            //     component : Blocked
            // }
          ],
        },
        {
          path: 'pin',
          meta: {
            label: 'Pin',
            requiresAuth: true,
            roles: ['bd', 'pn', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: PinPage,
              meta: {
                roles: ['bd', 'pn', 'admin'],
                actions: ['pin_list'],
              },
            },
          ],
        },
        {
          path: 'publishers',
          meta: {
            label: 'Pub. Management',
            requiresAuth: true,
            roles: ['publisher', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: Publishers,
              meta: {
                roles: ['publisher', 'admin'],
              },
            },
            {
              path: 'create',
              name: 'Add Publisher',
              component: CreatePublisher,
              meta: {
                roles: ['publisher', 'admin'],
              },
            },
            {
              path: ':pub_id',
              name: 'Publisher Details',
              label: 'Publisher Data',
              component: ShowPublisher,
              meta: {
                requiresAuth: true,
                roles: ['publisher', 'admin'],
              },
            },
            {
              path: 'edit-pub/:pub_id',
              label: 'Edit Publisher',
              name: 'Edit Publisher',
              component: EditPublisher,
              // define meta roles
              meta: {
                roles: ['publisher', 'admin'],
              },
            },
          ],
        },
        {
          path: 'cpm',
          meta: {
            label: 'Daily eCPM',
            requiresAuth: true,
            roles: ['publisher', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: CPM,
              meta: {
                roles: ['publisher', 'admin'],
                actions: ['list_cpm'],
              },
            },
          ],
        },
        {
          path: 'monetization',
          meta: {
            label: 'Monetization',
            requiresAuth: true,
            roles: ['publisher', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: Revenue,
              meta: {
                roles: ['publisher', 'admin'],
                actions: ['list_revenue'],
              },
            },
          ],
        },
        {
          path: 'monetization',
          meta: {
            label: 'Monetization',
            requiresAuth: true,
            roles: ['publisher', 'admin'],
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: Revenue,
              meta: {
                roles: ['publisher', 'admin'],
              },
            },
          ],
        },
        {
          path: 'announcements',
          meta: {
            label: 'Announcements',
            requiresAuth: true,
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: ListAnnouncements,
              meta: {
                actions: ['announcement_list'],
              },
            },
            {
              path: 'upsert/:id?',
              name: 'Create Announcements',
              component: CreateAnnouncements,
              meta: {
                actions: ['announcement_add', 'announcement_edit'],
              },
            },
          ],
        },
        {
          path: 'highlights',
          meta: {
            label: 'Highlights',
            requiresAuth: true,
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: ListHightlights,
              meta: {
                // actions: ['announcement_list']
              },
            },
            {
              path: 'upsert/:id?',
              name: 'Create Highlight',
              component: CreateHighlights,
              meta: {
                // actions: ['announcement_add', 'announcement_edit']
              },
            },
            {
              path: 'contents/:id',
              name: 'Highlight Contents',
              component: HighlightContents,
              meta: {
                // actions: ['announcement_add', 'announcement_edit']
              },
            },
          ],
        },
        {
          path: 'election',
          meta: {
            label: 'Election',
            requiresAuth: false,
          },
          component: {
            render(c) {
              return c('router-view');
            },
          },
          children: [
            {
              path: '',
              component: Election,
              meta: {
                // actions: ['announcement_list']
              },
            },
          ],
        },
        // {
        //     path : 'banned',
        //     meta : {
        //         label : 'Banned Users',
        //         requiresAuth : true
        //     },
        //     component : {
        //         render(c) {
        //             return c('router-view')
        //         }
        //     },
        //     children: [
        //         {
        //             path : '',
        //             component : Banned
        //         }
        //     ]
        // },
        // {
        //     path : 'wavmakers',
        //     meta : {
        //         label : 'WavMakers'
        //     },
        //     component : {
        //         render(c) {
        //             return c('router-view');
        //         }
        //     },
        //     children: [
        //         {
        //             path: '',
        //             component : ListWavMakers
        //         }
        //     ]
        // },
        // {
        //     path : 'sabah',
        //     meta: {
        //         label : 'Sabah'
        //     },
        //     component : {
        //         render(c) {
        //             return c('router-view');
        //         }
        //     },
        //     children: [
        //         {
        //             path: '',
        //             meta : {
        //                 label : 'Sabah Articles'
        //             },
        //             component : SabahArticle
        //         },
        //         {
        //             path : 'feed',
        //             meta : {
        //                 label : 'Sabah News Feed'
        //             },
        //             component : SabahNewsFeed
        //         }
        //     ]
        // }
        {
          path: '/profile',
          name: 'User Profile',
          meta: {
            requiresAuth: true,
          },
          component: Profile,
        },
        {
          path: '/settings',
          name: 'Dashboard Settings',
          meta: {
            requiresAuth: true,
          },
          component: Settings,
        },
      ],
    },
    {
      path: '*',
      redirect: '/',
    },
  ];
}

router.beforeEach(async (to, from, next) => {
  console.log('Route Object:', to);

  if (to.matched.some((record) => record.meta.requiresAuth)) {
    let loggedIn = store.getters.isLoggedIn;
    let tokenValid = store.getters.isTokenValid;
    let actions = store.getters.getActions;
    let allow = false;
    // store.dispatch('setPageLoading', true)
    if (loggedIn && tokenValid) {
      console.log(to.meta.roles);
      if (to.meta.actions) {
        if (actions.length == 0) {
          await store.dispatch('getRoles');
          actions = store.getters.getActions;
        }
        for (var i = 0; i < actions.length; i++) {
          if (to.meta.actions.indexOf(actions[i]) != -1) {
            allow = true;
          }
        }
        if (allow == true) {
          next();
        } else {
          console.log('unauthorized access');
          Vue.notify({
            group: 'error',
            title: 'Unauthorized',
            type: 'error',
            text: `No permission to view the page`,
            duration: 10000,
            position: 'bottom right',
            closeOnClick: true,
          });
          next('/dashboard');
        }
      } else {
        next();
      }
      //next()
    } else {
      store.commit('SET_USER', {});
      store.commit('SET_TOKEN', null);
      store.commit('SET_EXPIRY_DATE', null);
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('expiry');
      next({ path: '/login', returnUrl: { query: to.fullPath } });
    }
  } else {
    next();
  }
});

router.afterEach(() => {
  store.dispatch('setPageLoading', false);
});
