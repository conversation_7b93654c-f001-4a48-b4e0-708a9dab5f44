import 'core-js/stable';
import Vue from 'vue';
import App from './App';
import { router } from './router';
import CoreuiVue from '@coreui/vue';
import { iconsSet as iconSet } from './assets/icons/icons';
import {
  freeSet,
  cilPlus,
  cilPencil,
  cilEnvelopeOpen,
  cilTrash,
  cilShare,
  cilMoney,
  cilCalendarCheck,
  cilSpeedometer,
  cilHome,
  cilMediaPause,
  cilCheckCircle,
  cilMediaPlay,
  cilAvTimer,
  cilLibraryAdd,
  cilBan,
  cilMouse,
  cilPeople,
  cilLinkBroken,
  cilPowerStandby,
  cilBook,
  cilActionRedo,
  cilCommentSquare,
  cilChatBubble,
  cilPaint,
  cilFire,
  cilOptions,
  cilVideo,
  cilViewStream,
  cilMagnifyingGlass,
  cilTextSquare,
  cilNotes,
  cibFacebookF,
  cibTwitter,
  cilExitToApp,
  cilHappy,
  cilUser,
  cilLockLocked,
  cilReportSlash,
  cilUserX,
  cilBug,
  cilSend,
  cilSync,
  cilActionUndo,
  cilSpeech,
  cilBullhorn,
  cilListRich,
  cilLowVision,
  cilXCircle,
  cilCenterFocus,
  cibPinboard,
  cilWifiSignalOff,
  cilCasino,
  cilInstitution,
  cilCommentBubble,
  cilBasket,
  cilCursorMove,
  cilArrowThickToBottom,
  cilClock,
  cilCalendar,
  cilMediaStop,
  cilDescription,
  cilSettings,
  cilExternalLink,
  cilClone,
  cilVoiceOverRecord,
  cilJustifyCenter,
  cilWarning,
  cilHighlighter,
  cilUserFollow,
  cilUserUnfollow,
  cilToggleOn,
  cibProbot,
  cilCloudDownload,
  cilNewspaper,
} from '@coreui/icons';
import store from './store';
import VueFormWizard from 'vue-form-wizard/src';
import 'vue-form-wizard/dist/vue-form-wizard.min.css';
import Vidle from 'v-idle';
import Bugsnag from '@bugsnag/js';
import BugsnagPluginVue from '@bugsnag/plugin-vue';
import { library } from '@fortawesome/fontawesome-svg-core';
import {
  faUserSecret,
  faEye,
  faEyeSlash,
  faCheck,
  faTimes,
  faInfoCircle,
  faThumbtack,
  faTimesCircle,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import Notifications from 'vue-notification';
import VueSweetalert2 from 'vue-sweetalert2';
import 'sweetalert2/dist/sweetalert2.min.css';
import VCalendar from 'v-calendar';

import interceptorSetup from '@/helpers/interceptor';
interceptorSetup();

import VueToast from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-default.css';

Bugsnag.start({
  apiKey: process.env.VUE_APP_BUGSNAG_API_KEY,
  releaseStage: process.env.VUE_APP_APP_ENV,
  enabledReleaseStages: ['production', 'staging'],
  plugins: [new BugsnagPluginVue()],
});
Bugsnag.getPlugin('vue').installVueErrorHandler(Vue);

Vue.use(VCalendar, { componentPrefix: 'vc' });
Vue.use(Vidle);
Vue.use(VueFormWizard);
Vue.use(CoreuiVue);
library.add(
  faUserSecret,
  faEye,
  faEyeSlash,
  faCheck,
  faTimes,
  faInfoCircle,
  faThumbtack,
  faTimesCircle
);
Vue.config.performance = true;
Vue.prototype.$log = console.log.bind(console);
Vue.component('font-awesome-icon', FontAwesomeIcon);
Vue.config.productionTip = false;
Vue.use(Notifications);
const sweetConfig = {
  allowOutsideClick: false,
  allowEscapeKey: false,
  showCancelButton: true,
  showConfirmButton: true,
};
Vue.use(VueSweetalert2, sweetConfig);
Vue.use(VueToast);
Vue.mixin({
  methods: {
    checkAction(action) {
      if (this.$store.getters.getActions.find((element) => element == action)) {
        return true;
      } else {
        return false;
      }
    },

    //   checkInvestor(){
    //     if (this.$store.getters.getRoles.find(element => element == "investor")){
    //       return true
    //     }
    //     return false
    //   }
  },
  computed: {
    checkInvestor() {
      if (
        this.$store.getters.getRoles.find((element) => element == 'investor')
      ) {
        return true;
      }
      return false;
    },
  },
});

new Vue({
  el: '#app',
  icons: {
    freeSet,
    cilPlus,
    cilPencil,
    cilEnvelopeOpen,
    cilTrash,
    cilShare,
    cilMoney,
    cilCalendarCheck,
    cilSpeedometer,
    cilHome,
    cilCheckCircle,
    cilMediaPause,
    cilMediaPlay,
    cilAvTimer,
    cilLibraryAdd,
    cilBan,
    cilMouse,
    cilPeople,
    cilLinkBroken,
    cilPowerStandby,
    cilBook,
    cilActionRedo,
    cilCommentSquare,
    cilChatBubble,
    cilPaint,
    cilFire,
    cilOptions,
    cilVideo,
    cilViewStream,
    cilMagnifyingGlass,
    cilTextSquare,
    cilNotes,
    cibFacebookF,
    cibTwitter,
    cilExitToApp,
    cilHappy,
    cilUser,
    cilLockLocked,
    cilReportSlash,
    cilUserX,
    cilActionUndo,
    cilSpeech,
    cilBullhorn,
    cilLowVision,
    cilXCircle,
    cilBug,
    cilSend,
    cilSync,
    cilListRich,
    cilCenterFocus,
    cibPinboard,
    cilWifiSignalOff,
    cilCasino,
    cilInstitution,
    cilCommentBubble,
    cilBasket,
    cilCursorMove,
    cilArrowThickToBottom,
    cilClock,
    cilCalendar,
    cilMediaStop,
    cilDescription,
    cilSettings,
    cilExternalLink,
    cilClone,
    cilVoiceOverRecord,
    cilJustifyCenter,
    cilWarning,
    cilHighlighter,
    cilUserFollow,
    cilUserUnfollow,
    cilToggleOn,
    cibProbot,
    cilCloudDownload,
    cilNewspaper,
  },
  iconSet,
  store,
  router,
  template: '<App/>',
  components: {
    App,
  },
});
