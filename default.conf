server {
    listen       80;
    listen  [::]:80;
    server_name  localhost;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log warn;

    location ~ ^/(COPYRIGHT|INSTALL|README|htaccess)\.(txt|md)$ {
        deny all;
    }

    gzip on;
    gzip_min_length 500000;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}