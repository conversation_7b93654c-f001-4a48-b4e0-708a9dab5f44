# **Newswav-Dashboard**

## **What is it for?**

This web application is essentially a dashboard for our internal teams. It covers a lot of modules that affects the Newswav app.

## **Core Modules**

- Adwav - Affects Advertising
- Content - Affects Content
- Comments - Affects User Comments
- Live Comments - Affects Live Comments (Toggleable)
- Push Notifications - Affects PN sent
- Publisher - Publisher Management
- Users - User Management
- Election - Election Data Management
- COVID-19 - COVID-19 Data Management
  > We have an Access Control Level (ACL) module managed via an [Admin Dashboard](https://admin.newswav.com/admin/login) for the module access mentioned above.

## **Technologies Used**

1. [Vue.js V2](https://v2.vuejs.org/v2/guide/index.html)
2. [Core UI](https://coreui.io/vue/docs/3.2/introduction/)

## **Setting up for Development**

### **Running without Docker**

> Before setting up for development, it is assumed that you have Node installed in your machine. If you haven't you can install it first before proceeding

1. Open the terminal in the current dashboard codebase, and simply run the following command
   ```bash
   npm run install && npm run serve
   ```
   This will install the required packages based off `package.json` file. The application will be accessible via http://localhost:8080

### **Running with Docker**

1. Open the terminal in the current dashboard codebase, and run the following command. This command will build the Docker image for the project
   ```bash
   docker build -t <tag_of_image> -f <dev_dockerfile> <context>
   ```
   In your case will be as below:
   ```bash
   docker build -t newswav_dashboard -f Dockerfile.development .
   ```
2. After the image successfully built. Can continue to run the command below

   ```bash
   docker run newswav_dashboard
   ```

   To verify if the container is running or not can run the following command. This will list out all the running containers currently in your machine

   ```bash
   docker ps
   ```

   You can access the container's terminal, this can be done by running the command below. `<container_id>` can be found from the list generated by `docker ps`

   ```bash
   docker exec -it <container_id> bash
   ```

   After you can access the terminal, you can run the following command to run the application

   ```bash
   npm run serve
   ```
