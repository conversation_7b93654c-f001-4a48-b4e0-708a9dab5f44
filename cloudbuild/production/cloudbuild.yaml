timeout: 1200s
substitutions:
  _PROJECT_NAME: adwav-dashboard
  _PROJECT_APP_NAME: adwav-dashboard

steps:
  - name: gcr.io/kaniko-project/executor
    id: Build
    args:
      - --destination=asia-southeast1-docker.pkg.dev/$PROJECT_ID/$_PROJECT_NAME/$_PROJECT_APP_NAME:$COMMIT_SHA
      - --cache=true
      - --dockerfile=Dockerfile

  - name: 'gcr.io/cloud-builders/gcloud'
    id: Deploy
    args:
      - run
      - deploy
      - $_PROJECT_APP_NAME
      - --image=asia-southeast1-docker.pkg.dev/$PROJECT_ID/$_PROJECT_NAME/$_PROJECT_APP_NAME:$COMMIT_SHA
      - --region=asia-southeast1
      - --network=prod-vpc-asse1-nw
      - --subnet=prod-subnet-asse1-nw
      - --platform=managed
      - --allow-unauthenticated
      - --project=$PROJECT_ID
      - --port=80
      - --service-account=<EMAIL>
    waitFor: ['Build']
